import user from "./content-types/user";

export default (plugin: any): any => {
  // Extend or override the user content type
  plugin.contentTypes.user = user;

  // Create the user list api in controller with pagination, sort, search etc.
  plugin.controllers.user.list = async (ctx) => {
    try {
      const { pagination, sort, filters, populate } = ctx.query;
      const page = Number(pagination?.page) || 1;
      const pageSize = Number(pagination?.pageSize) || 25;

      const start = (page - 1) * pageSize;
      const limit = pageSize;

      // ✅ Fetch paginated data
      let data = await strapi
        .documents("plugin::users-permissions.user")
        .findMany({
          filters,
          sort,
          start,
          limit,
          populate,
        });

      // ✅ Fetch total count using findMany() (since count() does not respect filters correctly)
      const total = (
        await strapi.documents("plugin::users-permissions.user").findMany({
          filters,
          limit: -1, // ✅ Get all filtered records without pagination
        })
      ).length; // ✅ Correct total count

      // Exclude unwanted fields
      const excludedFields = [
        "locale",
        "password",
        "provider",
        "publishedAt",
        "resetPasswordToken",
        "confirmationToken",
      ];

      // Remove excluded fields from the data
      data = data.map((user: any) => {
        excludedFields.forEach((field) => {
          delete user[field]; // Delete each unwanted field
        });
        return user;
      });

      // ✅ Send response with correct pagination meta
      ctx.send({
        data,
        meta: {
          pagination: {
            page,
            pageSize,
            pageCount: Math.ceil(total / pageSize),
            total,
          },
        },
      });
    } catch (error) {
      console.error("Error fetching users:", error);
      ctx.throw(500, "Internal Server Error");
    }
  };

  // Get logged in user and admin user data.
  plugin.controllers.user.oneself = async (ctx) => {
    try {
      const { documentId } = ctx.params;

      const vendorFields = [
        "documentId",
        "address",
        "country",
        "city",
        "zipcode",
        "invoice_ref",
        "purchase_order",
        "department",
        "phone",
        "website",
      ];

      // Check for a normal user with the documentId
      const user = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { documentId },
          select: [
            "documentId",
            "firstname",
            "lastname",
            "username",
            "email",
            "address",
            "last_logout_at",
          ],
          populate: {
            role: true,
            cart: {
              populate: {
                customer: {
                  populate: {
                    business_partner: {
                      select: "bp_full_name",
                      populate: {
                        addresses: {
                          populate: {
                            address_usages: true,
                          },
                        },
                      },
                    },
                    partner_functions: {
                      where: { partner_function: "SP" },
                    },
                  },
                },
                cart_items: true,
              },
            },
            vendor: {
              select: vendorFields,
              populate: {
                customer: {
                  populate: {
                    partner_functions: {
                      where: { partner_function: "SP" },
                    },
                  },
                },
                supplier: true,
              },
            },
          },
        });

      if (user) {
        ctx.body = user;
        return;
      }

      // If no normal user is found, check for an admin user with the documentId
      const admin = await strapi.query("admin::user").findOne({
        where: { documentId },
        select: ["documentId", "firstname", "lastname", "username", "email"],
        populate: {
          roles: true,
        },
      });

      if (admin) {
        const knex = strapi.db.connection;

        // Fetch cart ID from carts_admin_user_lnk table using raw SQL
        const cartLink = await knex("carts_admin_user_lnk")
          .select("cart_id")
          .where("user_id", admin.id)
          .first();

        let cart = null;
        if (cartLink?.cart_id) {
          // Fetch and populate cart details using the cart ID
          cart = await strapi.query("api::cart.cart").findOne({
            where: { id: cartLink.cart_id },
            populate: {
              customer: {
                populate: {
                  business_partner: {
                    select: "bp_full_name",
                    populate: {
                      addresses: {
                        populate: {
                          address_usages: true,
                        },
                      },
                    },
                  },
                  partner_functions: {
                    where: { partner_function: "SP" },
                  },
                },
              },
              cart_items: true,
            },
          });
        }

        // Fetch Vendor ID from user_vendors_admin_user_lnk table using raw SQL
        const userVendorLink = await knex("user_vendors_admin_user_lnk")
          .select("user_vendor_id")
          .where("user_id", admin.id)
          .first();

        let vendor = null;
        if (userVendorLink?.user_vendor_id) {
          // Fetch and populate vendor user details using the vendor user ID
          vendor = await strapi.query("api::user-vendor.user-vendor").findOne({
            where: { id: userVendorLink?.user_vendor_id },
            select: vendorFields,
            populate: {
              customer: {
                populate: {
                  partner_functions: {
                    where: { partner_function: "SP" },
                  },
                },
              },
              supplier: true,
            },
          });
        }

        const adminExtn = await knex("admin_user_extensions_admin_user_lnk")
          .where("admin_user_extensions_admin_user_lnk.user_id", admin.id)
          .join(
            "admin_user_extensions",
            "admin_user_extensions_admin_user_lnk.admin_user_extension_id",
            "admin_user_extensions.id"
          )
          .select(
            "admin_user_extensions.address",
            "admin_user_extensions.last_logout_at"
          )
          .first();

        // Add role and cart details to the admin user response
        ctx.body = {
          isAdmin: true,
          ...admin,
          ...{
            address: adminExtn?.address || null,
            last_logout_at: adminExtn?.last_logout_at || null,
          },
          cart,
          vendor,
        };
        return;
      }

      // If neither user nor admin is found, return a Not Found error
      return ctx.notFound(
        "User or admin with the provided documentId not found."
      );
    } catch (err) {
      strapi.log.error("Error retrieving user or admin data:", err);
      ctx.internalServerError("An error occurred while retrieving data.");
    }
  };

  plugin.controllers.user.customers = async (ctx) => {
    try {
      const { documentId } = ctx.params;

      // Extract pagination and sorting from query parameters
      const { pagination, sort, filters, populate, fields  } = ctx.query;
      const { page, pageSize }: any = pagination || {
        page: 1,
        pageSize: 25,
      };

      // Convert page and pageSize to numbers
      const start = (Number(page) - 1) * Number(pageSize);
      const limit = Number(pageSize);

      // Define query filters
      const queryFilters = {
        $or: [{ users: { documentId } }, { admin_users: { documentId } }],
        ...filters,
      };

      // Fetch paginated data using strapi.documents
      const data = await strapi.documents("api::customer.customer").findMany({
        fields, 
        filters: queryFilters,
        populate,
        sort,
        start,
        limit,
      });

      // Count total documents for pagination meta
      const total = await strapi.documents("api::customer.customer").count({
        filters: queryFilters,
      });

      // Return response with pagination meta
      ctx.send({
        data,
        meta: {
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total,
          },
        },
      });
    } catch (err) {
      strapi.log.error("Error fetching customers:", err);
      ctx.internalServerError("An error occurred while retrieving customers.");
    }
  };

  plugin.controllers.user.suppliers = async (ctx) => {
    try {
      const { documentId } = ctx.params;

      // Extract pagination and sorting from query parameters
      const { pagination, sort, filters, populate, fields } = ctx.query;
      const { page, pageSize }: any = pagination || {
        page: 1,
        pageSize: 25,
      };

      // Convert page and pageSize to numbers
      const start = (Number(page) - 1) * Number(pageSize);
      const limit = Number(pageSize);

      // Define query filters
      const queryFilters = {
        $or: [{ users: { documentId } }, { admin_users: { documentId } }],
        ...filters,
      };

      // Fetch paginated data using strapi.documents
      const data = await strapi.documents("api::supplier.supplier").findMany({
        fields,
        filters: queryFilters,
        populate,
        sort,
        start,
        limit,
      });

      // Count total documents for pagination meta
      const total = await strapi.documents("api::supplier.supplier").count({
        filters: queryFilters,
      });

      // Return response with pagination meta
      ctx.send({
        data,
        meta: {
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total,
          },
        },
      });
    } catch (err) {
      strapi.log.error("Error fetching suppliers:", err);
      ctx.internalServerError("An error occurred while retrieving suppliers.");
    }
  };

  plugin.controllers.user.permissionVendor = async (ctx) => {
    try {
      const { documentId } = ctx.params;
      const data = await strapi
        .query("api::permission-vendor.permission-vendor")
        .findMany({
          where: {
            $or: [
              { user_role: { users: { documentId } } },
              { admin_user_role: { users: { documentId } } },
            ],
          },
        });
      ctx.send({ data: data.map((p: any) => p.code) });
    } catch (error) {
      ctx.throw(500, error);
    }
  };

  plugin.controllers.user.permissionCRM = async (ctx) => {
    try {
      const { documentId } = ctx.params;
      const data = await strapi
        .query("api::permission-crm.permission-crm")
        .findMany({
          where: {
            $or: [
              { user_role: { users: { documentId } } },
              { admin_user_role: { users: { documentId } } },
            ],
          },
        });
      ctx.send({ data: data.map((p: any) => p.code) });
    } catch (error) {
      ctx.throw(500, error);
    }
  };

  plugin.controllers.user.permissionPIM = async (ctx) => {
    try {
      const { documentId } = ctx.params;
      const data = await strapi
        .query("api::permission-pim.permission-pim")
        .findMany({
          where: {
            $or: [
              { user_role: { users: { documentId } } },
              { admin_user_role: { users: { documentId } } },
            ],
          },
        });
      ctx.send({ data: data.map((p: any) => p.code) });
    } catch (error) {
      ctx.throw(500, error);
    }
  };

  plugin.controllers.user.linkAllCustomer = async (ctx) => {
    try {
      const { documentId } = ctx.params;
      const knex = strapi.db.connection;

      // Fetch user from 'users-permissions' plugin
      let user = await strapi.query("plugin::users-permissions.user").findOne({
        where: { documentId },
      });

      // If not found, fetch from Strapi admin users
      if (!user) {
        user = await strapi.query("admin::user").findOne({
          where: { documentId },
        });
        if (user) {
          user.isAdmin = true;
        }
      }

      if (!user) {
        return ctx.notFound(
          "User or admin with the provided documentId not found."
        );
      }

      // Fetch all customer IDs
      const customers = await strapi.query("api::customer.customer").findMany({
        select: ["id"],
        where: {
          $or: [
            {
              business_partner: {
                roles: {
                  bp_role: {
                    $ne: "FLVN00",
                  },
                },
              },
            },
            {
              business_partner: {
                roles: {
                  bp_role: {
                    $ne: "FLVN01",
                  },
                },
              },
            },
          ],
        },
      });

      const customerIds = [
        ...new Set(customers.map((customer) => customer.id)),
      ];

      if (customerIds.length === 0) {
        return ctx.send({ message: "No customers found to link." });
      }

      const BATCH_SIZE = 1000;

      if (user.isAdmin) {
        // Prepare mappings for admin linking
        const customerMappings = customerIds.map((customerId, index) => ({
          user_id: user.id,
          customer_id: customerId,
          user_ord: index + 1,
        }));

        // Batch insert admin-user relationships
        for (let i = 0; i < customerMappings.length; i += BATCH_SIZE) {
          const batch = customerMappings.slice(i, i + BATCH_SIZE);
          await knex("customers_admin_users_lnk")
            .insert(batch)
            .onConflict(["customer_id", "user_id"])
            .ignore();
        }

        return ctx.send({
          message: "All customers linked to admin successfully.",
        });
      }

      // Prepare mappings for admin linking
      const customerMappings = customerIds.map((customerId, index) => ({
        user_id: user.id,
        customer_id: customerId,
        customer_ord: index + 1,
        user_ord: index + 1,
      }));

      // Batch insert admin-user relationships
      for (let i = 0; i < customerMappings.length; i += BATCH_SIZE) {
        const batch = customerMappings.slice(i, i + BATCH_SIZE);
        await knex("up_users_customers_lnk")
          .insert(batch)
          .onConflict(["customer_id", "user_id"])
          .ignore();
      }

      return ctx.send({
        message: "All customers linked with user successfully.",
      });
    } catch (err) {
      strapi.log.error("Error linking customers:", err);
      ctx.internalServerError("An error occurred while linking customers.");
    }
  };

  plugin.controllers.user.unlinkAllCustomer = async (ctx) => {
    try {
      const { documentId } = ctx.params;
      const knex = strapi.db.connection;

      // Fetch user from 'users-permissions' plugin
      let user = await strapi.query("plugin::users-permissions.user").findOne({
        where: { documentId },
      });

      // If not found, fetch from Strapi admin users
      if (!user) {
        user = await strapi.query("admin::user").findOne({
          where: { documentId },
        });
        if (user) {
          user.isAdmin = true;
        }
      }

      if (!user) {
        return ctx.notFound(
          "User or admin with the provided documentId not found."
        );
      }

      if (user.isAdmin) {
        // Delete mappings for admin users from `customers_admin_users_lnk`
        await knex("customers_admin_users_lnk").where("user_id", user.id).del();
      } else {
        // Disconnect customers for regular users
        await knex("up_users_customers_lnk").where("user_id", user.id).del();
      }

      ctx.send({ message: "All linked customers unlinked successfully." });
    } catch (err) {
      strapi.log.error("Error unlinking customers:", err);
      ctx.internalServerError("An error occurred while unlinking customers.");
    }
  };

  plugin.controllers.user.linkAllSupplier = async (ctx) => {
    try {
      const { documentId } = ctx.params;
      const knex = strapi.db.connection;

      // Fetch user from 'users-permissions' plugin
      let user = await strapi.query("plugin::users-permissions.user").findOne({
        where: { documentId },
      });

      // If not found, fetch from Strapi admin users
      if (!user) {
        user = await strapi.query("admin::user").findOne({
          where: { documentId },
        });
        if (user) {
          user.isAdmin = true;
        }
      }

      if (!user) {
        return ctx.notFound(
          "User or admin with the provided documentId not found."
        );
      }

      // Fetch all supplier IDs
      const suppliers = await strapi.query("api::supplier.supplier").findMany({
        select: ["id"],
        where: {
          $or: [
            {
              business_partner: {
                roles: {
                  bp_role: "FLVN00",
                },
              },
            },
            {
              business_partner: {
                roles: {
                  bp_role: "FLVN01",
                },
              },
            },
          ],
        },
      });

      const supplierIds = [
        ...new Set(suppliers.map((supplier) => supplier.id)),
      ];

      if (supplierIds.length === 0) {
        return ctx.send({ message: "No suppliers found to link." });
      }

      const BATCH_SIZE = 1000;

      if (user.isAdmin) {
        // Prepare mappings for admin linking
        const supplierMappings = supplierIds.map((supplierId, index) => ({
          user_id: user.id,
          supplier_id: supplierId,
          user_ord: index + 1,
        }));

        // Batch insert admin-user relationships
        for (let i = 0; i < supplierMappings.length; i += BATCH_SIZE) {
          const batch = supplierMappings.slice(i, i + BATCH_SIZE);
          await knex("suppliers_admin_users_lnk")
            .insert(batch)
            .onConflict(["supplier_id", "user_id"])
            .ignore();
        }

        return ctx.send({
          message: "All suppliers linked to admin successfully.",
        });
      }

      // Prepare mappings for admin linking
      const supplierMappings = supplierIds.map((supplierId, index) => ({
        user_id: user.id,
        supplier_id: supplierId,
        supplier_ord: index + 1,
        user_ord: index + 1,
      }));

      // Batch insert admin-user relationships
      for (let i = 0; i < supplierMappings.length; i += BATCH_SIZE) {
        const batch = supplierMappings.slice(i, i + BATCH_SIZE);
        await knex("up_users_suppliers_lnk")
          .insert(batch)
          .onConflict(["supplier_id", "user_id"])
          .ignore();
      }

      return ctx.send({
        message: "All suppliers linked with user successfully.",
      });
    } catch (err) {
      strapi.log.error("Error linking suppliers:", err);
      ctx.internalServerError("An error occurred while linking suppliers.");
    }
  };

  plugin.controllers.user.unlinkAllSupplier = async (ctx) => {
    try {
      const { documentId } = ctx.params;
      const knex = strapi.db.connection;

      // Fetch user from 'users-permissions' plugin
      let user = await strapi.query("plugin::users-permissions.user").findOne({
        where: { documentId },
      });

      // If not found, fetch from Strapi admin users
      if (!user) {
        user = await strapi.query("admin::user").findOne({
          where: { documentId },
        });
        if (user) {
          user.isAdmin = true;
        }
      }

      if (!user) {
        return ctx.notFound(
          "User or admin with the provided documentId not found."
        );
      }

      if (user.isAdmin) {
        // Delete mappings for admin users from `suppliers_admin_users_lnk`
        await knex("suppliers_admin_users_lnk").where("user_id", user.id).del();
      } else {
        // Disconnect suppliers for regular users
        await knex("up_users_suppliers_lnk").where("user_id", user.id).del();
      }

      ctx.send({ message: "All linked suppliers unlinked successfully." });
    } catch (err) {
      strapi.log.error("Error unlinking suppliers:", err);
      ctx.internalServerError("An error occurred while unlinking suppliers.");
    }
  };

  // Get logged out user and admin user time.
  plugin.controllers.user.logout = async (ctx) => {
    try {
      const { documentId } = ctx.params;
      const logoutTime = new Date();

      const knex = strapi.db.connection;

      // Check for a normal user with the documentId
      const user = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { documentId },
          select: ["id", "documentId", "email"],
        });

      if (user) {
        // Update last logout time for end user
        await strapi.query("plugin::users-permissions.user").update({
          where: { id: user.id },
          data: { last_logout_at: logoutTime },
        });

        ctx.body = { message: "User logout time updated." };
        return;
      }

      // If no normal user is found, check for an admin user with the documentId
      const admin = await strapi.query("admin::user").findOne({
        where: { documentId },
        select: ["id", "documentId", "email"],
      });

      if (admin) {
        // Fetch the admin_user_extension_id from the link table
        const adminExtnLink = await knex("admin_user_extensions_admin_user_lnk")
          .select("admin_user_extension_id")
          .where("user_id", admin.id)
          .first();

        if (adminExtnLink?.admin_user_extension_id) {
          // Update last logout time in admin_user_extensions table
          await knex("admin_user_extensions")
            .where("id", adminExtnLink.admin_user_extension_id)
            .update({ last_logout_at: logoutTime });
        }

        ctx.body = { message: "Admin logout time updated." };
        return;
      }

      // If neither user nor admin is found, return a Not Found error
      return ctx.notFound(
        "User or admin with the provided documentId not found."
      );
    } catch (err) {
      strapi.log.error("Error updating logout time:", err);
      ctx.internalServerError("An error occurred while updating logout time.");
    }
  };

  // Extend or override user routes
  plugin.routes["content-api"].routes = [
    {
      method: "GET",
      path: "/users/list",
      handler: "user.list",
      config: { prefix: "" },
    },
    {
      method: "GET",
      path: "/users/:documentId/me",
      handler: "user.oneself",
      config: { prefix: "" },
    },
    {
      method: "GET",
      path: "/users/:documentId/customers",
      handler: "user.customers",
      config: { prefix: "" },
    },
    {
      method: "PUT",
      path: "/users/:documentId/link-all-customer",
      handler: "user.linkAllCustomer",
      config: { prefix: "" },
    },
    {
      method: "PUT",
      path: "/users/:documentId/unlink-all-customer",
      handler: "user.unlinkAllCustomer",
      config: { prefix: "" },
    },
    {
      method: "GET",
      path: "/users/:documentId/suppliers",
      handler: "user.suppliers",
      config: { prefix: "" },
    },
    {
      method: "PUT",
      path: "/users/:documentId/link-all-supplier",
      handler: "user.linkAllSupplier",
      config: { prefix: "" },
    },
    {
      method: "PUT",
      path: "/users/:documentId/unlink-all-supplier",
      handler: "user.unlinkAllSupplier",
      config: { prefix: "" },
    },
    {
      method: "GET",
      path: "/users/:documentId/permissions/vendor",
      handler: "user.permissionVendor",
      config: { prefix: "" },
    },
    {
      method: "GET",
      path: "/users/:documentId/permissions/crm",
      handler: "user.permissionCRM",
      config: { prefix: "" },
    },
    {
      method: "GET",
      path: "/users/:documentId/permissions/pim",
      handler: "user.permissionPIM",
      config: { prefix: "" },
    },
    {
      method: "GET",
      path: "/users/:documentId/logout",
      handler: "user.logout",
      config: { prefix: "" },
    },
    ...plugin.routes["content-api"].routes,
  ];

  // Return the modified plugin
  return plugin;
};
