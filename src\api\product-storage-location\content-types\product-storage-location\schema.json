{"kind": "collectionType", "collectionName": "product_storage_locations", "info": {"singularName": "product-storage-location", "pluralName": "product-storage-locations", "displayName": "Product Storage Location"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"storage_location": {"type": "string"}, "warehouse_storage_bin": {"type": "string"}, "maintenance_status": {"type": "string"}, "is_marked_for_deletion": {"type": "boolean"}, "physical_inventory_block_ind": {"type": "boolean"}, "creation_date": {"type": "datetime"}, "date_of_last_posted_cnt_unrstrcd_stk": {"type": "datetime"}, "invtry_restricted_use_stock_ind": {"type": "boolean"}, "invtry_current_year_stock_ind": {"type": "boolean"}, "invtry_qual_insp_current_yr_stk_ind": {"type": "boolean"}, "inventory_block_stock_ind": {"type": "boolean"}, "invtry_rest_stock_prev_period_ind": {"type": "boolean"}, "inventory_stock_prev_period": {"type": "boolean"}, "invtry_stock_qlty_insp_prev_period": {"type": "boolean"}, "has_invtry_block_stock_prev_period": {"type": "boolean"}, "fiscal_year_current_period": {"type": "string"}, "fiscal_year_current_invtry_period": {"type": "string"}, "lean_wrhs_management_picking_area": {"type": "string"}, "plant_id": {"type": "string"}, "plant": {"type": "relation", "relation": "manyToOne", "target": "api::product-plant.product-plant", "inversedBy": "storage_locations"}, "product_id": {"type": "string"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "storage_locations"}}}