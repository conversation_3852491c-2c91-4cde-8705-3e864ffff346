{"kind": "collectionType", "collectionName": "admin_user_extensions", "info": {"singularName": "admin-user-extension", "pluralName": "admin-user-extensions", "displayName": "Admin User Extension"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"address": {"type": "string"}, "last_logout_at": {"type": "datetime"}, "admin_user": {"type": "relation", "relation": "oneToOne", "target": "admin::user", "required": true}}}