export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.customer_id) {
      try {
        const cust = await strapi.query("api::customer.customer").findOne({
          where: { customer_id: params.data.customer_id },
        });

        if (cust) {
          await strapi
            .query("api::customer-sales-area.customer-sales-area")
            .update({
              where: { id: result.id },
              data: {
                customer: {
                  connect: [cust.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.customer_id) {
      try {
        const cust = await strapi.query("api::customer.customer").findOne({
          where: { customer_id: params.data.customer_id },
        });

        if (cust) {
          await strapi
            .query("api::customer-sales-area.customer-sales-area")
            .update({
              where: { id: result.id },
              data: {
                customer: {
                  connect: [cust.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.customer_id && result?.customer?.count === 1) {
          await strapi
            .query("api::customer-sales-area.customer-sales-area")
            .update({
              where: { id: result.id },
              data: {
                customer: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error(
          "Error in afterUpdate when disconnecting:",
          error
        );
      }
    }
  },
};
