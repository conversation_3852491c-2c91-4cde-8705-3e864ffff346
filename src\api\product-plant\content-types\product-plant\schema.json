{"kind": "collectionType", "collectionName": "product_plants", "info": {"singularName": "product-plant", "pluralName": "product-plants", "displayName": "Product Plant"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"product_id": {"type": "string"}, "plant": {"type": "string"}, "base_unit": {"type": "string"}, "base_iso_unit": {"type": "string"}, "purchasing_group": {"type": "string"}, "country_of_origin": {"type": "string"}, "region_of_origin": {"type": "string"}, "production_invtry_managed_loc": {"type": "string"}, "profile_code": {"type": "string"}, "profile_validity_start_date": {"type": "datetime"}, "availability_check_type": {"type": "string"}, "fiscal_year_variant": {"type": "string"}, "period_type": {"type": "string"}, "profit_center": {"type": "string"}, "commodity": {"type": "string"}, "goods_receipt_duration": {"type": "integer"}, "maintenance_status_name": {"type": "string"}, "is_marked_for_deletion": {"type": "boolean"}, "mrp_type": {"type": "string"}, "mrp_responsible": {"type": "string"}, "abc_indicator": {"type": "string"}, "minimum_lot_size_quantity": {"type": "integer"}, "maximum_lot_size_quantity": {"type": "integer"}, "fixed_lot_size_quantity": {"type": "integer"}, "consumption_tax_ctrl_code": {"type": "string"}, "is_co_product": {"type": "boolean"}, "product_is_configurable": {"type": "boolean"}, "stock_determination_group": {"type": "string"}, "stock_in_transfer_quantity": {"type": "integer"}, "stock_in_transit_quantity": {"type": "integer"}, "has_post_to_inspection_stock": {"type": "boolean"}, "is_batch_management_required": {"type": "boolean"}, "serial_number_profile": {"type": "string"}, "is_negative_stock_allowed": {"type": "boolean"}, "goods_receipt_blocked_stock_qty": {"type": "integer"}, "has_consignment_ctrl": {"type": "boolean"}, "fiscal_year_current_period": {"type": "string"}, "fiscal_month_current_period": {"type": "string"}, "is_internal_batch_managed": {"type": "boolean"}, "procurement_type": {"type": "string"}, "product_cfop_category": {"type": "string"}, "product_is_excise_tax_relevant": {"type": "boolean"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "plants"}, "storage_locations": {"type": "relation", "relation": "oneToMany", "target": "api::product-storage-location.product-storage-location", "mappedBy": "plant"}, "plant_procurement": {"type": "relation", "relation": "oneToOne", "target": "api::product-plant-procurement.product-plant-procurement", "mappedBy": "plant"}, "plant_text": {"type": "relation", "relation": "oneToOne", "target": "api::product-plant-text.product-plant-text", "mappedBy": "plant"}, "plant_storage": {"type": "relation", "relation": "oneToOne", "target": "api::product-plant-storage.product-plant-storage", "mappedBy": "plant"}, "plant_sale": {"type": "relation", "relation": "oneToOne", "target": "api::product-plant-sale.product-plant-sale", "mappedBy": "plant"}}}