export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.class_internal_id) {
      try {
        const class_internal = await strapi
          .query("api::product-class-type.product-class-type")
          .findOne({
            where: { class_internal_id: params.data.class_internal_id },
          });

        if (class_internal) {
          await strapi
            .query(
              "api::product-class-type-description.product-class-type-description"
            )
            .update({
              where: { id: result.id },
              data: {
                class_internal: {
                  connect: [class_internal.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.class_internal_id) {
      try {
        const class_internal = await strapi
          .query("api::product-class-type.product-class-type")
          .findOne({
            where: { class_internal_id: params.data.class_internal_id },
          });

        if (class_internal) {
          await strapi
            .query(
              "api::product-class-type-description.product-class-type-description"
            )
            .update({
              where: { id: result.id },
              data: {
                class_internal: {
                  connect: [class_internal.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.class_internal_id && result?.class_internal?.count === 1) {
          await strapi
            .query(
              "api::product-class-type-description.product-class-type-description"
            )
            .update({
              where: { id: result.id },
              data: {
                class_internal: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
