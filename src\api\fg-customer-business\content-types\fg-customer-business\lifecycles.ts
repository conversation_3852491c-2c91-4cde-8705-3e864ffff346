export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.bp_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_id },
          });

        if (bp) {
          await strapi
            .query("api::fg-customer-business.fg-customer-business")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.membership_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.membership_id },
          });

        if (bp) {
          await strapi
            .query("api::fg-customer-business.fg-customer-business")
            .update({
              where: { id: result.id },
              data: {
                membership: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.bp_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_id },
          });

        if (bp) {
          await strapi
            .query("api::fg-customer-business.fg-customer-business")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.bp_id && result?.business_partner?.count === 1) {
          await strapi
            .query("api::fg-customer-business.fg-customer-business")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.membership_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.membership_id },
          });

        if (bp) {
          await strapi
            .query("api::fg-customer-business.fg-customer-business")
            .update({
              where: { id: result.id },
              data: {
                membership: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.membership_id && result?.membership?.count === 1) {
          await strapi
            .query("api::fg-customer-business.fg-customer-business")
            .update({
              where: { id: result.id },
              data: {
                membership: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
  async afterDelete(event) {
    const { result } = event;
    if (result.flex_group_id && result.bp_id) {
      try {
        const toDelete = await strapi.db
          .query("api::fg-customer-internal.fg-customer-internal")
          .findMany({
            where: {
              flex_group_id: result.flex_group_id,
              bp_id: result.bp_id,
              locale: result.locale || "en",
            },
            select: ["id"],
          });

        // Perform bulk deletion using the IDs
        if (toDelete.length > 0) {
          await strapi.db
            .query("api::fg-customer-internal.fg-customer-internal")
            .deleteMany({
              where: { id: { $in: toDelete.map(({ id }) => id) } },
            });
        }
      } catch (error) {
        console.error(
          `Error deleting customer-internal for flex_group_id: ${result.flex_group_id} and bp_id: ${result.bp_id}`,
          error
        );
      }
    }

    if (result.flex_group_id && result.membership_id) {
      try {
        const bpIDs = await strapi.db
          .query(
            "api::business-partner-relationship.business-partner-relationship"
          )
          .findMany({
            where: {
              bp_id2: { $eq: result.membership_id },
            },
            select: ["bp_id1", "locale"],
          });

        const toDelete = await strapi.db
          .query("api::fg-customer-internal.fg-customer-internal")
          .findMany({
            where: {
              flex_group_id: result.flex_group_id,
              bp_id: { $in: bpIDs?.map(({ bp_id1 }) => bp_id1) },
              locale: result.locale || "en",
            },
            select: ["id"],
          });

        // Perform bulk deletion using the IDs
        if (toDelete.length > 0) {
          await strapi.db
            .query("api::fg-customer-internal.fg-customer-internal")
            .deleteMany({
              where: { id: { $in: toDelete.map(({ id }) => id) } },
            });
        }
      } catch (error) {
        console.error(
          `Error deleting customer-internal for flex_group_id: ${result.flex_group_id} and membership_id: ${result.membership_id}`,
          error
        );
      }
    }
  },
};
