# After creating docker file with below code
# Step 1: docker build -f Dockerfile.container -t snjya/node20cfdockercli .
# Step 2: docker login
# Step 3: docker push snjya/node20cfdockercli:latest

# Use Node.js 20 (Debian-based) as the base image
FROM node:20-slim AS build

# Create a non-root user and group
RUN groupadd -r chssnjyauser && useradd --no-log-init -r -g chssnjyauser chssnjyauser

# Install Strapi's recommended dependencies, Docker CLI, and tools
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    autoconf \
    automake \
    zlib1g-dev \
    libpng-dev \
    libvips-dev \
    git \
    curl \
    tar \
    docker.io \
    && rm -rf /var/lib/apt/lists/*

# Install Cloud Foundry CLI (latest version, v8.7.10)
RUN curl -L -o cf-cli.tgz "https://github.com/cloudfoundry/cli/releases/download/v8.7.10/cf8-cli_8.7.10_linux_x86-64.tgz" && \
    tar -xzf cf-cli.tgz -C /usr/local/bin/ && \
    rm cf-cli.tgz && \
    chmod +x /usr/local/bin/cf8 && \
    ln -sf /usr/local/bin/cf8 /usr/local/bin/cf && \
    cf --version

# Set working directory
WORKDIR /app

# Set permissions
RUN chown chssnjyauser:chssnjyauser /app

# Switch to non-root user
USER chssnjyauser

# Set default command
CMD ["node"]