import { uploadFGControlMainTextToAzure } from "../../fg-control-main/helpers";
import { syncCustomerInternal } from "../../fg-customer-internal/controllers/helpers";
import { syncProductInternal } from "../../fg-product-internal/controllers/helpers";
import {
  uploadProductMediaToAzure,
  uploadProductSpecificationToAzure,
  uploadProductWebAttributeToAzure,
} from "../../product/helpers";

const generateCronExpression = (scheduler) => {
  const { schedule_type, day_of_month, weekdays_to_generate, time_of_day } =
    scheduler;
  const [hour, minute] = time_of_day?.split(":") || "00:00";

  if (schedule_type === "DAILY") {
    return `${minute} ${hour} * * *`;
  } else if (schedule_type === "MONTHLY") {
    return `${minute} ${hour} ${day_of_month} * *`;
  } else if (schedule_type === "WEEKLY") {
    const days = weekdays_to_generate
      .split(",")
      .map((day) => day.trim().toUpperCase());
    const dayMap = {
      SUNDAY: 0,
      MONDAY: 1,
      TUESDAY: 2,
      WEDNESDAY: 3,
      THURSDAY: 4,
      FRIDAY: 5,
      SATURDAY: 6,
    };
    const cronDays = days.map((day) => dayMap[day]).join(",");
    return `${minute} ${hour} * * ${cronDays}`;
  }
  throw new Error("Invalid schedule type");
};

const executeSchedulerTask = async (scheduler) => {
  const { operation } = scheduler;
  console.log(`Executing scheduler for operation ${operation}.`);

  let result = null;
  switch (operation) {
    case "FG_CUSTOMER_BUSINESS":
      result = await syncCustomerInternal();
      console.log(result.message);
      break;

    case "FG_PRODUCT_BUSINESS":
      result = await syncProductInternal();
      console.log(result.message);
      break;

    case "FG_CONTROL_MAIN_FEED":
      await uploadFGControlMainTextToAzure();
      break;

    case "PRODUCT_WEB_ATTRIBUTE_FEED":
      await uploadProductWebAttributeToAzure();
      break;

    case "PRODUCT_SPECIFICATION_FEED":
      await uploadProductSpecificationToAzure();
      break;

    case "PRODUCT_MEDIA_FEED":
      await uploadProductMediaToAzure();
      break;

    default:
      console.error(`Unknown operation: ${operation}`);
  }
};

const startCronJob = async (scheduler) => {
  const { operation, is_active, start_date, end_date } = scheduler;

  if (is_active) {
    const cronExpression = generateCronExpression(scheduler);
    strapi.cron.add({
      [operation]: {
        task: async ({ strapi }) => {
          await executeSchedulerTask(scheduler);
        },
        options: {
          rule: cronExpression,
          start: new Date(start_date),
          end: new Date(end_date),
          tz: "America/Chicago",
        },
      },
    });
  } else {
    stopCronJob(scheduler);
  }
};

const stopCronJob = (scheduler) => {
  const { operation } = scheduler;
  strapi.cron.remove(operation);
  console.log(`Cron job removed for scheduler: ${operation}`);
};

export { startCronJob, stopCronJob };
