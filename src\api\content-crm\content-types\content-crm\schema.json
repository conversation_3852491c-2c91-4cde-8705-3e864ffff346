{"kind": "collectionType", "collectionName": "content_crms", "info": {"singularName": "content-crm", "pluralName": "content-crms", "displayName": "Content CRM", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"content_name": {"type": "string"}, "slug": {"type": "string"}, "i18n": {"type": "json"}, "body": {"type": "dynamiczone", "components": ["crm.logo", "crm.media"]}}}