export default {
  async beforeDelete(event) {
    const { where } = event.params;
    const contactId = where.id;

    // Fetch the contact to get bp_person_id and bp_company_id
    const contact = await strapi.db
      .query("api::business-partner-contact.business-partner-contact")
      .findOne({
        where: { id: contactId },
        select: ["bp_person_id", "bp_company_id"],
      });

    if (!contact) return;

    const { bp_person_id, bp_company_id } = contact;

    // Delete BP Roles
    await strapi.db
      .query("api::business-partner-role.business-partner-role")
      .deleteMany({
        where: { bp_id: bp_person_id },
      });

    // Delete Business Partner Relationship
    await strapi.db
      .query("api::business-partner-relationship.business-partner-relationship")
      .deleteMany({
        where: {
          bp_id1: bp_company_id,
          bp_id2: bp_person_id,
          relationship_number: {
            $startsWith: "REL",
          },
        },
      });

    // Delete Department & Function entries
    await strapi.db
      .query("api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept")
      .deleteMany({
        where: {
          bp_person_id,
          bp_company_id,
          relationship_number: {
            $startsWith: "REL",
          },
        },
      });

    // Find related addresses
    const addresses = await strapi.db
      .query("api::business-partner-address.business-partner-address")
      .findMany({
        where: {
          bp_id: bp_person_id,
          bp_address_id: {
            $startsWith: "ADD",
          },
        },
        select: ["id"],
      });

    // Delete addresses one by one (to trigger their hooks)
    for (const address of addresses) {
      await strapi.db
        .query("api::business-partner-address.business-partner-address")
        .delete({
          where: { id: address.id },
        });
    }
  },
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.relationship_number) {
      try {
        const relationship = await strapi
          .query(
            "api::business-partner-relationship.business-partner-relationship"
          )
          .findOne({
            where: { relationship_number: params.data.relationship_number },
          });

        if (relationship) {
          await strapi
            .query("api::business-partner-contact.business-partner-contact")
            .update({
              where: { id: result.id },
              data: {
                relationship: {
                  connect: [relationship.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.bp_person_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_person_id },
          });

        if (bp) {
          await strapi
            .query("api::business-partner-contact.business-partner-contact")
            .update({
              where: { id: result.id },
              data: {
                business_partner_person: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.bp_company_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_company_id },
          });

        if (bp) {
          await strapi
            .query("api::business-partner-contact.business-partner-contact")
            .update({
              where: { id: result.id },
              data: {
                business_partner_company: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.relationship_number) {
      try {
        const relationship = await strapi
          .query(
            "api::business-partner-relationship.business-partner-relationship"
          )
          .findOne({
            where: { relationship_number: params.data.relationship_number },
          });

        if (relationship) {
          await strapi
            .query("api::business-partner-contact.business-partner-contact")
            .update({
              where: { id: result.id },
              data: {
                relationship: {
                  connect: [relationship.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.relationship_number && result?.relationship?.count === 1) {
          await strapi
            .query("api::business-partner-contact.business-partner-contact")
            .update({
              where: { id: result.id },
              data: {
                relationship: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.bp_person_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_person_id },
          });

        if (bp) {
          await strapi
            .query("api::business-partner-contact.business-partner-contact")
            .update({
              where: { id: result.id },
              data: {
                business_partner_person: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.bp_person_id &&
          result?.business_partner_person?.count === 1
        ) {
          await strapi
            .query("api::business-partner-contact.business-partner-contact")
            .update({
              where: { id: result.id },
              data: {
                business_partner_person: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.bp_company_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_company_id },
          });

        if (bp) {
          await strapi
            .query("api::business-partner-contact.business-partner-contact")
            .update({
              where: { id: result.id },
              data: {
                business_partner_company: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.bp_company_id &&
          result?.business_partner_company?.count === 1
        ) {
          await strapi
            .query("api::business-partner-contact.business-partner-contact")
            .update({
              where: { id: result.id },
              data: {
                business_partner_company: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
