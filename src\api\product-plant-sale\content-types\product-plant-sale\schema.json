{"kind": "collectionType", "collectionName": "product_plant_sales", "info": {"singularName": "product-plant-sale", "pluralName": "product-plant-sales", "displayName": "Product Plant Sales"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"loading_group": {"type": "string"}, "replacement_part_type": {"type": "string"}, "cap_planning_quantity_in_base_uom": {"type": "integer"}, "product_shipping_processing_time": {"type": "integer"}, "wrk_centers_shipg_setup_time_in_days": {"type": "integer"}, "plant_id": {"type": "string"}, "plant": {"type": "relation", "relation": "oneToOne", "target": "api::product-plant.product-plant", "inversedBy": "plant_sale"}, "product_id": {"type": "string"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "plant_sale"}}}