import { generateUniqueID } from "../../../../utils/cpi";

export default {
  async beforeCreate(event) {
    const { params } = event;
    if (!params.data.competitor_id) {
      try {
        const customId = `${await generateUniqueID("crm_competitor_id_seq")}`;
        params.data.competitor_id = customId;
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
};
