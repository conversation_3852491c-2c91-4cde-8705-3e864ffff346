{"kind": "collectionType", "collectionName": "schedulers", "info": {"singularName": "scheduler", "pluralName": "schedulers", "displayName": "Scheduler"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"start_date": {"type": "date", "required": true}, "end_date": {"type": "date", "required": true}, "schedule_type": {"type": "enumeration", "enum": ["DAILY", "WEEKLY", "MONTHLY"], "default": "DAILY", "required": true}, "day_of_month": {"type": "integer", "min": 1, "max": 31}, "weekdays_to_generate": {"type": "string", "maxLength": 50, "description": "Comma-separated list of weekdays to run the task (e.g., 'MONDAY, WEDNESDAY')"}, "operation": {"type": "enumeration", "enum": ["FG_CUSTOMER_BUSINESS", "FG_PRODUCT_BUSINESS", "FG_CONTROL_MAIN_FEED", "PRODUCT_WEB_ATTRIBUTE_FEED", "PRODUCT_SPECIFICATION_FEED", "PRODUCT_MEDIA_FEED"], "required": true}, "time_of_day": {"type": "string", "default": "00:00"}, "is_active": {"type": "boolean", "default": false}}}