{"kind": "collectionType", "collectionName": "up_users", "info": {"name": "user", "description": "", "singularName": "user", "pluralName": "users", "displayName": "User"}, "options": {"timestamps": true}, "attributes": {"firstname": {"type": "string", "required": true}, "lastname": {"type": "string", "required": true}, "username": {"type": "string", "unique": true, "column": {"unique": true}}, "email": {"type": "email", "required": true}, "provider": {"type": "string", "configurable": false, "default": "local"}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true, "searchable": false}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmationToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": null, "configurable": false}, "address": {"type": "string"}, "last_logout_at": {"type": "datetime"}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "customers": {"type": "relation", "relation": "manyToMany", "target": "api::customer.customer", "inversedBy": "users"}, "suppliers": {"type": "relation", "relation": "manyToMany", "target": "api::supplier.supplier", "inversedBy": "users"}, "cart": {"type": "relation", "relation": "oneToOne", "target": "api::cart.cart", "inversedBy": "user"}, "cart_reserves": {"type": "relation", "relation": "oneToMany", "target": "api::cart-reserve.cart-reserve", "mappedBy": "user"}, "vendor": {"type": "relation", "relation": "oneToOne", "target": "api::user-vendor.user-vendor", "inversedBy": "user"}}, "plugin": "users-permissions", "globalId": "UsersPermissionsUser"}