import { start<PERSON>ron<PERSON><PERSON>, stop<PERSON><PERSON><PERSON><PERSON> } from "../../helpers";

export default {
  async afterCreate(event) {
    const { result: scheduler } = event;
    await start<PERSON>ronJob(scheduler);
  },

  async afterUpdate(event) {
    const { result: scheduler } = event;
    stopCronJob(scheduler);
    await start<PERSON>ron<PERSON>ob(scheduler);
  },

  async afterDelete(event) {
    const { result: scheduler } = event;
    stopCronJob(scheduler);
  },
};
