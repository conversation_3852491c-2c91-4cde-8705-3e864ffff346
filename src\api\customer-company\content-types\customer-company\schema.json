{"kind": "collectionType", "collectionName": "customer_companies", "info": {"singularName": "customer-company", "pluralName": "customer-companies", "displayName": "Customer Company"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"company_code": {"type": "string"}, "deletion_indicator": {"type": "boolean"}, "customer_account_group": {"type": "string"}, "accounting_clerk": {"type": "string"}, "accounting_clerk_fax_number": {"type": "string"}, "accounting_clerk_internet_address": {"type": "string"}, "accounting_clerk_phone_number": {"type": "string"}, "alternative_payer_account": {"type": "string"}, "apart_tolerance_group": {"type": "string"}, "authorization_group": {"type": "string"}, "cash_planning_group": {"type": "string"}, "collective_invoice_variant": {"type": "string"}, "customer_account_note": {"type": "text"}, "customer_head_office": {"type": "string"}, "customer_supplier_clearing_is_used": {"type": "boolean"}, "house_bank": {"type": "string"}, "interest_calculation_code": {"type": "string"}, "interest_calculation_date": {"type": "datetime"}, "intrst_calc_frequency_in_months": {"type": "integer"}, "is_to_be_locally_processed": {"type": "boolean"}, "item_is_to_be_paid_separately": {"type": "boolean"}, "known_or_negotiated_leave": {"type": "string"}, "last_interest_calc_run_date": {"type": "datetime"}, "layout_sorting_rule": {"type": "string"}, "payment_blocking_reason": {"type": "string"}, "payment_methods_list": {"type": "string"}, "payment_reason": {"type": "string"}, "payment_terms": {"type": "string"}, "payt_advice_is_sent_by_edi": {"type": "boolean"}, "physical_inventory_block_ind": {"type": "boolean"}, "reconciliation_account": {"type": "string"}, "record_payment_history_indicator": {"type": "boolean"}, "user_at_customer": {"type": "string"}, "value_adjustment_key": {"type": "string"}, "account_by_customer": {"type": "string"}, "customer_id": {"type": "string"}, "customer": {"type": "relation", "relation": "manyToOne", "target": "api::customer.customer", "inversedBy": "companies"}, "company_texts": {"type": "relation", "relation": "oneToMany", "target": "api::customer-company-text.customer-company-text", "mappedBy": "company"}}}