{"kind": "collectionType", "collectionName": "supplier_purchasing_org_texts", "info": {"singularName": "supplier-purchasing-org-text", "pluralName": "supplier-purchasing-org-texts", "displayName": "Supplier Purchasing Organization Text"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"language": {"type": "string", "required": true}, "long_text_id": {"type": "string", "required": true}, "supplier_id": {"type": "string", "required": true}, "purchasing_organization": {"type": "string", "required": true}, "long_text": {"type": "text"}, "supplier_purchasing_org": {"type": "relation", "relation": "manyToOne", "target": "api::supplier-purchasing-org.supplier-purchasing-org", "inversedBy": "texts"}}}