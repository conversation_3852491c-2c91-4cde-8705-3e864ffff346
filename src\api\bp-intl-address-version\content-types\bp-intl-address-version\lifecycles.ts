export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.bp_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_id },
          });

        if (bp) {
          await strapi
            .query("api::bp-intl-address-version.bp-intl-address-version")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.bp_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_id },
          });

        if (bp) {
          await strapi
            .query("api::bp-intl-address-version.bp-intl-address-version")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.bp_id && result?.business_partner?.count === 1) {
          await strapi
            .query("api::bp-intl-address-version.bp-intl-address-version")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
