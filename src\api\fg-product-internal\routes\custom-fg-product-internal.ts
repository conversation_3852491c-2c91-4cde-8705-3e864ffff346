/**
 * configuration custom router
 */

export default {
  routes: [
    {
      method: "GET",
      path: "/fg-product-internal/sync",
      handler: "fg-product-internal.sync",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/fg-product-internal/restore",
      handler: "fg-product-internal.restoreProductInternalData",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "POST",
      path: "/fg-product-internal/check-fg-restricted-product",
      handler: "fg-product-internal.checkRestrictedProductInternals",
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
