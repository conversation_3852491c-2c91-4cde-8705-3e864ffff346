{"kind": "collectionType", "collectionName": "product_sales_texts", "info": {"singularName": "product-sales-text", "pluralName": "product-sales-texts", "displayName": "Product Sales Text"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"product_id": {"type": "string"}, "product_sales_org": {"type": "string"}, "sales_status_validity_date": {"type": "datetime"}, "language": {"type": "string"}, "long_text": {"type": "text"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "sales_texts"}, "sales_delivery": {"type": "relation", "relation": "oneToOne", "target": "api::product-sales-delivery.product-sales-delivery", "inversedBy": "sales_text"}}}