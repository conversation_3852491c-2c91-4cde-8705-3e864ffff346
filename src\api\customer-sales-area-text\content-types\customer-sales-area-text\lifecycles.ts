export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.customer_id) {
      try {
        const cust = await strapi.query("api::customer.customer").findOne({
          where: { customer_id: params.data.customer_id },
        });

        if (cust) {
          await strapi
            .query("api::customer-sales-area-text.customer-sales-area-text")
            .update({
              where: { id: result.id },
              data: {
                customer: {
                  connect: [cust.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (
      params.data.customer_id &&
      params.data.sales_organization &&
      params.data.distribution_channel &&
      params.data.division
    ) {
      try {
        const sales_area = await strapi
          .query("api::customer-sales-area.customer-sales-area")
          .findOne({
            where: {
              customer_id: params.data.customer_id,
              sales_organization: params.data.sales_organization,
              distribution_channel: params.data.distribution_channel,
              division: params.data.division,
            },
          });

        if (sales_area) {
          await strapi
            .query("api::customer-sales-area-text.customer-sales-area-text")
            .update({
              where: { id: result.id },
              data: {
                sales_area: {
                  connect: [sales_area.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.customer_id) {
      try {
        const cust = await strapi.query("api::customer.customer").findOne({
          where: { customer_id: params.data.customer_id },
        });

        if (cust) {
          await strapi
            .query("api::customer-sales-area-text.customer-sales-area-text")
            .update({
              where: { id: result.id },
              data: {
                customer: {
                  connect: [cust.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.customer_id && result?.customer?.count === 1) {
          await strapi
            .query("api::customer-sales-area-text.customer-sales-area-text")
            .update({
              where: { id: result.id },
              data: {
                customer: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (
      params.data.customer_id &&
      params.data.sales_organization &&
      params.data.distribution_channel &&
      params.data.division
    ) {
      try {
        const sales_area = await strapi
          .query("api::customer-sales-area.customer-sales-area")
          .findOne({
            where: {
              customer_id: params.data.customer_id,
              sales_organization: params.data.sales_organization,
              distribution_channel: params.data.distribution_channel,
              division: params.data.division,
            },
          });

        if (sales_area) {
          await strapi
            .query("api::customer-sales-area-text.customer-sales-area-text")
            .update({
              where: { id: result.id },
              data: {
                sales_area: {
                  connect: [sales_area.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.customer_id &&
          !result.sales_organization &&
          !result.distribution_channel &&
          !result.division &&
          result?.sales_area?.count === 1
        ) {
          await strapi
            .query("api::customer-sales-area-text.customer-sales-area-text")
            .update({
              where: { id: result.id },
              data: {
                sales_area: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
