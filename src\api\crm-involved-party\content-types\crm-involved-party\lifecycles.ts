export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.party_id },
          });

        if (bp) {
          await strapi.query("api::crm-involved-party.crm-involved-party").update({
            where: { id: result.id },
            data: {
              business_partner: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.activity_id) {
      try {
        const activity = await strapi
          .query("api::crm-activity.crm-activity")
          .findOne({
            where: { activity_id: params.data.activity_id },
          });

        if (activity) {
          await strapi.query("api::crm-involved-party.crm-involved-party").update({
            where: { id: result.id },
            data: {
              activity: {
                connect: [activity.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.party_id },
          });

        if (bp) {
          await strapi.query("api::crm-involved-party.crm-involved-party").update({
            where: { id: result.id },
            data: {
              business_partner: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.party_id && result?.business_partner?.count === 1) {
          await strapi.query("api::crm-involved-party.crm-involved-party").update({
            where: { id: result.id },
            data: {
              business_partner: {
                set: [],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.activity_id) {
      try {
        const activity = await strapi
          .query("api::crm-activity.crm-activity")
          .findOne({
            where: { activity_id: params.data.activity_id },
          });

        if (activity) {
          await strapi.query("api::crm-involved-party.crm-involved-party").update({
            where: { id: result.id },
            data: {
              activity: {
                connect: [activity.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.activity_id && result?.activity?.count === 1) {
          await strapi.query("api::crm-involved-party.crm-involved-party").update({
            where: { id: result.id },
            data: {
              activity: {
                set: [],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
