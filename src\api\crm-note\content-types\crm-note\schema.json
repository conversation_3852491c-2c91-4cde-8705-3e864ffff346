{"kind": "collectionType", "collectionName": "crm_notes", "info": {"singularName": "crm-note", "pluralName": "crm-notes", "displayName": "CRM Note"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"note": {"type": "text"}, "is_global_note": {"type": "boolean", "default": false}, "bp_id": {"type": "string"}, "business_partner": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "notes"}, "activity_id": {"type": "string"}, "activity": {"type": "relation", "relation": "manyToOne", "target": "api::crm-activity.crm-activity", "inversedBy": "notes"}, "opportunity_id": {"type": "string"}, "opportunity": {"type": "relation", "relation": "manyToOne", "target": "api::crm-opportunity.crm-opportunity", "inversedBy": "notes"}}}