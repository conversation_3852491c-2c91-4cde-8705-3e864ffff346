{"kind": "collectionType", "collectionName": "product_class_charc_types", "info": {"singularName": "product-class-charc-type", "pluralName": "product-class-charc-types", "displayName": "Product Class Charc Type", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"charc_internal_id": {"type": "string", "required": true, "unique": true, "column": {"unique": true}}, "characteristic": {"type": "string"}, "charc_status": {"type": "string"}, "charc_status_name": {"type": "string"}, "charc_data_type": {"type": "string"}, "charc_value_unit": {"type": "string"}, "unit_of_measure_iso_code": {"type": "string"}, "charc_value_types": {"type": "relation", "relation": "oneToMany", "target": "api::product-charc-value-type.product-charc-value-type", "mappedBy": "charc_internal"}, "descriptions": {"type": "relation", "relation": "oneToMany", "target": "api::product-class-charc-type-description.product-class-charc-type-description", "mappedBy": "charc_internal"}}}