export default {
  /**
   * Called before a cart is deleted.
   * Deletes the cart items associated with the cart.
   */
  async beforeDelete({ params }) {
    const cartId = params?.where?.id;

    if (cartId) {
      try {
        // Fetch all related cart items
        const toDelete = await strapi.db
          .query("api::guest-user-cart-item.guest-user-cart-item")
          .findMany({
            where: { cart: cartId },
          });

        // Perform bulk deletion using the IDs
        if (toDelete.length > 0) {
          await strapi.db
            .query("api::guest-user-cart-item.guest-user-cart-item")
            .deleteMany({
              where: { id: { $in: toDelete.map(({ id }) => id) } },
            });
        }
      } catch (error) {
        console.error(
          `Error deleting guest user cart item for cartId ID: ${cartId}`,
          error
        );
      }
    }
  },
};
