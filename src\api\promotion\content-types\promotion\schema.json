{"kind": "collectionType", "collectionName": "promotions", "info": {"singularName": "promotion", "pluralName": "promotions", "displayName": "Promotion"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"code": {"type": "string", "required": true, "unique": true, "column": {"unique": true}}, "is_automatic": {"type": "boolean", "default": false}, "type": {"type": "enumeration", "enum": ["CART_DISCOUNT", "BUY_X_GET_Y", "FREE_SHIPPING"], "required": true}, "rules": {"type": "relation", "relation": "manyToMany", "target": "api::promotion-rule.promotion-rule", "mappedBy": "promotions"}, "application_method": {"type": "relation", "relation": "oneToOne", "target": "api::promotion-application-method.promotion-application-method", "mappedBy": "promotion"}}}