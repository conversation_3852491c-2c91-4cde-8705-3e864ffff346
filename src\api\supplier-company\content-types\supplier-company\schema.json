{"kind": "collectionType", "collectionName": "supplier_companies", "info": {"singularName": "supplier-company", "pluralName": "supplier-companies", "displayName": "Supplier Company"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"company_code": {"type": "string", "required": true}, "accounting_clerk_fax_number": {"type": "string"}, "accounting_clerk_phone_number": {"type": "string"}, "alternative_payee": {"type": "string"}, "apar_tolerance_group": {"type": "string"}, "authorization_group": {"type": "string"}, "bill_of_exch_lmt_amt_in_co_code_crcy": {"type": "decimal"}, "cash_planning_group": {"type": "string"}, "check_paid_duration_in_days": {"type": "integer"}, "clear_customer_supplier": {"type": "boolean"}, "company_code_name": {"type": "string"}, "currency": {"type": "string"}, "deletion_indicator": {"type": "boolean"}, "house_bank": {"type": "string"}, "interest_calculation_code": {"type": "string"}, "interest_calculation_date": {"type": "datetime"}, "intrst_calc_frequency_in_months": {"type": "integer"}, "is_to_be_checked_for_duplicates": {"type": "boolean"}, "is_to_be_locally_processed": {"type": "boolean"}, "item_is_to_be_paid_separately": {"type": "boolean"}, "layout_sorting_rule": {"type": "string"}, "last_interest_calc_run_date": {"type": "datetime"}, "minority_group": {"type": "string"}, "payment_blocking_reason": {"type": "string"}, "payment_is_to_be_sent_by_edi": {"type": "boolean"}, "payment_methods_list": {"type": "string"}, "payment_reason": {"type": "string"}, "payment_terms": {"type": "string"}, "reconciliation_account": {"type": "string"}, "supplier_account_group": {"type": "string"}, "supplier_account_note": {"type": "text"}, "supplier_certification_date": {"type": "datetime"}, "supplier_clerk": {"type": "string"}, "supplier_clerk_id_by_supplier": {"type": "string"}, "supplier_clerk_url": {"type": "string"}, "supplier_head_office": {"type": "string"}, "supplier_is_blocked_for_posting": {"type": "boolean"}, "withholding_tax_country": {"type": "string"}, "withholding_tax_country_code": {"type": "string"}, "accounting_clerk": {"type": "string"}, "supplier_id": {"type": "string", "required": true}, "supplier": {"type": "relation", "relation": "manyToOne", "target": "api::supplier.supplier", "inversedBy": "companies"}, "company_texts": {"type": "relation", "relation": "oneToMany", "target": "api::supplier-company-text.supplier-company-text", "mappedBy": "company"}}}