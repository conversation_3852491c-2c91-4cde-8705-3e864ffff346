{"kind": "collectionType", "collectionName": "supplier_partner_funcs", "info": {"singularName": "supplier-partner-func", "pluralName": "supplier-partner-funcs", "displayName": "Supplier Purchasing Partner Functions"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"purchasing_organization": {"type": "string", "required": true}, "supplier_subrange": {"type": "string"}, "plant": {"type": "string"}, "partner_function": {"type": "string", "required": true}, "partner_counter": {"type": "integer", "required": true}, "authorization_group": {"type": "string"}, "default_partner": {"type": "boolean", "default": false}, "creation_date": {"type": "datetime"}, "created_by_user": {"type": "string"}, "reference_supplier": {"type": "string"}, "supplier_id": {"type": "string", "required": true}, "supplier": {"type": "relation", "relation": "manyToOne", "target": "api::supplier.supplier", "inversedBy": "partner_functions"}}}