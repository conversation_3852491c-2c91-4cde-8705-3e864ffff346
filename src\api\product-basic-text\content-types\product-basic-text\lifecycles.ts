export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi
            .query("api::product-basic-text.product-basic-text")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi
            .query("api::product-basic-text.product-basic-text")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.product_id && result?.product?.count === 1) {
          await strapi
            .query("api::product-basic-text.product-basic-text")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
