export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.customer_id) {
      try {
        const cust = await strapi.query("api::customer.customer").findOne({
          where: { customer_id: params.data.customer_id },
        });

        if (cust) {
          await strapi
            .query("api::customer-partner-function.customer-partner-function")
            .update({
              where: { id: result.id },
              data: {
                customer: {
                  connect: [cust.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.bp_customer_number) {
      try {
        const business_partner = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_customer_number },
          });

        if (business_partner) {
          await strapi
            .query("api::customer-partner-function.customer-partner-function")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  connect: [business_partner.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.customer_id) {
      try {
        const cust = await strapi.query("api::customer.customer").findOne({
          where: { customer_id: params.data.customer_id },
        });

        if (cust) {
          await strapi
            .query("api::customer-partner-function.customer-partner-function")
            .update({
              where: { id: result.id },
              data: {
                customer: {
                  connect: [cust.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.customer_id && result?.customer?.count === 1) {
          await strapi
            .query("api::customer-partner-function.customer-partner-function")
            .update({
              where: { id: result.id },
              data: {
                customer: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.bp_customer_number) {
      try {
        const business_partner = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_customer_number },
          });

        if (business_partner) {
          await strapi
            .query("api::customer-partner-function.customer-partner-function")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  connect: [business_partner.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.bp_customer_number &&
          result?.business_partner?.count === 1
        ) {
          await strapi
            .query("api::customer-partner-function.customer-partner-function")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
