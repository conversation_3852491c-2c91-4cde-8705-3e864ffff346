import { PassThrough } from "stream";
import { stringify } from "csv-stringify";
import {
  BlobServiceClient,
  StorageSharedKeyCredential,
} from "@azure/storage-blob";

const createCsvStreamForFGControlMain = () =>
  stringify({
    header: true,
    delimiter: "|",
    columns: ["material", "group", "type", "usage", "name", "icon", "iconText"],
    quote: true,
  });

const processFGControlMainToStream = async (stream: any) => {
  const batchSize = 1000;
  let offset = 0;
  let hasMore = true;

  while (hasMore) {
    const fgCtrlMains = await strapi.db
      .query("api::fg-control-main.fg-control-main")
      .findMany({ offset, limit: batchSize });

    if (!fgCtrlMains.length) {
      hasMore = false;
      break;
    }

    for (const fgCtrlMain of fgCtrlMains) {
      const flexGroupId = fgCtrlMain.flex_group_id;
      let productOffset = 0;
      let hasMoreProducts = true;

      while (hasMoreProducts) {
        const products = await strapi.db
          .query("api::fg-product-internal.fg-product-internal")
          .findMany({
            where: { flex_group_id: flexGroupId },
            offset: productOffset,
            limit: batchSize,
          });

        if (!products.length) {
          hasMoreProducts = false;
          break;
        }

        for (const product of products) {
          const group =
            fgCtrlMain?.flex_group_id?.toString()?.padStart(10, "0") || "";
          stream.write({
            material: product.product_id || "",
            group,
            type: fgCtrlMain.flex_group_type || "",
            usage: fgCtrlMain.indicator_type || "",
            name: fgCtrlMain.description || "",
            icon: fgCtrlMain.icon_image || "",
            iconText: fgCtrlMain.icon_text || "",
          });
        }

        productOffset += batchSize;
      }
    }

    offset += batchSize;
  }

  stream.end();
};

const uploadFGControlMainTextToAzure = async () => {
  try {
    const passThrough = new PassThrough();
    const stream = createCsvStreamForFGControlMain();
    stream.pipe(passThrough);

    const accountName = process.env.AZURE_BLOB_ACCOUNT_NAME;
    const accountKey = process.env.AZURE_BLOB_ACCOUNT_KEY;
    const containerName = process.env.AZURE_BLOB_CONTAINER_NAME;
    const blobBasePath = process.env.AZURE_BLOB_FILE_PATH || "";

    const now = new Date();
    const timestamp = now
      .toISOString()
      .replace(/[-:.TZ]/g, "")
      .slice(0, 14);
    const fileName = `flexible_group-${timestamp}.csv`;
    const fullBlobPath = `${blobBasePath.replace(/\/$/, "")}/${fileName}`;

    const sharedKeyCredential = new StorageSharedKeyCredential(
      accountName,
      accountKey
    );
    const blobServiceClient = new BlobServiceClient(
      `https://${accountName}.blob.core.windows.net`,
      sharedKeyCredential
    );

    const containerClient = blobServiceClient.getContainerClient(containerName);

    const exists = await containerClient.exists();
    if (!exists) {
      strapi.log.info(
        `Container '${containerName}' does not exist. Creating it...`
      );
      await containerClient.create();
      strapi.log.info(`Container '${containerName}' created successfully.`);
    }

    const blockBlobClient = containerClient.getBlockBlobClient(fullBlobPath);

    const azureUploadPromise = blockBlobClient.uploadStream(
      passThrough,
      undefined,
      undefined,
      {
        blobHTTPHeaders: { blobContentType: "text/csv" },
      }
    );

    await Promise.all([
      processFGControlMainToStream(stream),
      azureUploadPromise,
    ]);

    strapi.log.info(
      `FG Control Main export uploaded to Azure Blob Storage at '${fullBlobPath}'.`
    );
  } catch (err) {
    strapi.log.error("Error exporting/uploading FG Control Main:", err);
  }
};

export {
  createCsvStreamForFGControlMain,
  processFGControlMainToStream,
  uploadFGControlMainTextToAzure,
};
