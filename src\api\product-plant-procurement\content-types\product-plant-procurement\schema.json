{"kind": "collectionType", "collectionName": "product_plant_procurements", "info": {"singularName": "product-plant-procurement", "pluralName": "product-plant-procurements", "displayName": "Product Plant Procurement"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"is_auto_pur_ord_creation_allowed": {"type": "boolean"}, "is_source_list_required": {"type": "boolean"}, "source_of_supply_category": {"type": "string"}, "itm_is_rlvt_to_jit_deliv_schedules": {"type": "boolean"}, "plant_id": {"type": "string"}, "plant": {"type": "relation", "relation": "oneToOne", "target": "api::product-plant.product-plant", "inversedBy": "plant_procurement"}, "product_id": {"type": "string"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "plant_procurement"}}}