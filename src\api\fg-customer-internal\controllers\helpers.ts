const chunkArray = (array, size) => {
  return Array.from({ length: Math.ceil(array.length / size) }, (_, index) =>
    array.slice(index * size, index * size + size)
  );
};

const syncBulkCustomerInternal = async (dataBatch) => {
  try {
    if (!dataBatch || dataBatch.length === 0) return;

    const flexGroupIds = dataBatch.map((d) => d.flex_group_id);
    const bpIds = dataBatch.map((d) => d.bp_id);
    const locales = dataBatch.map((d) => d.locale || "en");

    // Step 1: Find existing records
    const existingRecords = await strapi.db
      .query("api::fg-customer-internal.fg-customer-internal")
      .findMany({
        where: {
          flex_group_id: { $in: flexGroupIds },
          bp_id: { $in: bpIds },
          locale: { $in: locales },
        },
        select: ["flex_group_id", "bp_id", "locale"],
      });

    // Step 2: Convert existing records into a Set for quick lookup
    const existingSet = new Set(
      existingRecords.map((r) => `${r.flex_group_id}-${r.bp_id}-${r.locale}`)
    );

    // Step 3: Filter out duplicates before inserting
    const bulkInsertData = dataBatch.filter(
      (d) => !existingSet.has(`${d.flex_group_id}-${d.bp_id}-${d.locale}`)
    );

    if (bulkInsertData.length > 0) {
      await strapi.db
        .query("api::fg-customer-internal.fg-customer-internal")
        .createMany({
          data: bulkInsertData,
        });
    }
  } catch (error) {
    console.error("Bulk insert error in syncBulkCustomerInternal:", error);
  }
};

const syncCustomerInternal = async () => {
  const setting = await strapi.db.query("api::setting.setting").findOne({
    select: ["id", "is_fgci_in_progress"],
  });

  if (!setting) {
    return { success: false, message: "No active sync setting found." };
  }

  if (setting.is_fgci_in_progress) {
    return { success: false, message: "Sync is already in progress." };
  }

  // Set flag to prevent concurrent executions
  await strapi.db.query("api::setting.setting").update({
    where: { id: setting.id },
    data: { is_fgci_in_progress: true },
  });

  try {
    console.log("Backup FG Customer Internal records...");
    await strapi.db.connection.raw(`
        -- Clear existing backup
        TRUNCATE TABLE fg_customer_internal_backups;
        -- Insert backup data
        INSERT INTO fg_customer_internal_backups SELECT * FROM fg_customer_internals;
      `);
    console.log("DONE Backup FG Customer Internal records...");

    console.log("Fetching FG Customer Business records for deletion...");
    let offset = 0;
    const limit = 1000;

    // Delete Records
    while (true) {
      const bps = await strapi.db
        .query("api::fg-customer-business.fg-customer-business")
        .findMany({
          where: {
            flex_group_id: { $ne: null },
            is_all_bp: { $eq: false },
            $or: [{ bp_id: { $ne: null } }, { membership_id: { $ne: null } }],
            operand: "DELETE",
          },
          select: ["id", "flex_group_id", "bp_id", "membership_id", "locale"],
          limit,
          offset,
        });

      if (!bps.length) break;

      console.log(`Processing delete batch of ${bps.length} records...`);

      const chunkedBps = chunkArray(bps, 500);
      for (const chunk of chunkedBps) {
        for (const result of chunk) {
          if (result.flex_group_id && result.bp_id) {
            const toDelete = await strapi.db
              .query("api::fg-customer-internal.fg-customer-internal")
              .findMany({
                where: {
                  flex_group_id: result.flex_group_id,
                  bp_id: result.bp_id,
                  locale: result.locale || "en",
                },
                select: ["id"],
              });

            if (toDelete.length > 0) {
              await strapi.db
                .query("api::fg-customer-internal.fg-customer-internal")
                .deleteMany({
                  where: { id: { $in: toDelete.map(({ id }) => id) } },
                });
            }
          }

          if (result.flex_group_id && result.membership_id) {
            const bpIDs = await strapi.db
              .query(
                "api::business-partner-relationship.business-partner-relationship"
              )
              .findMany({
                where: {
                  bp_id2: { $eq: result.membership_id },
                },
                select: ["bp_id1", "locale"],
              });

            const toDelete = await strapi.db
              .query("api::fg-customer-internal.fg-customer-internal")
              .findMany({
                where: {
                  flex_group_id: result.flex_group_id,
                  bp_id: { $in: bpIDs?.map(({ bp_id1 }) => bp_id1) },
                  locale: result.locale || "en",
                },
                select: ["id"],
              });

            if (toDelete.length > 0) {
              await strapi.db
                .query("api::fg-customer-internal.fg-customer-internal")
                .deleteMany({
                  where: { id: { $in: toDelete.map(({ id }) => id) } },
                });
            }
          }

          if (result?.id) {
            await strapi.db
              .query("api::fg-customer-business.fg-customer-business")
              .delete({
                where: { id: result.id },
              });
          }
        }
      }

      offset += limit;
    }

    console.log(
      "Initial batch deletion completed. Processing batch insert data..."
    );

    offset = 0;

    while (true) {
      const bps = await strapi.db
        .query("api::fg-customer-business.fg-customer-business")
        .findMany({
          where: {
            flex_group_id: { $ne: null },
            bp_id: { $ne: null },
            is_all_bp: { $eq: false },
            $or: [{ operand: "ADD" }, { operand: "UPDATE" }],
          },
          select: ["id", "flex_group_id", "bp_id", "locale"],
          limit,
          offset,
        });

      if (!bps.length) break;

      console.log(`Processing batch of ${bps.length} records...`);

      const chunkedBps = chunkArray(bps, 500);
      for (const chunk of chunkedBps) {
        await syncBulkCustomerInternal(chunk);
      }

      offset += limit;
    }

    console.log(
      "Initial batch insert completed. Processing membership data..."
    );

    offset = 0;

    while (true) {
      const membershipBPs = await strapi.db
        .query("api::fg-customer-business.fg-customer-business")
        .findMany({
          where: {
            flex_group_id: { $ne: null },
            membership_id: { $ne: null },
            is_all_bp: { $eq: false },
            $or: [{ operand: "ADD" }, { operand: "UPDATE" }],
          },
          select: ["id", "flex_group_id", "membership_id", "locale"],
          limit,
          offset,
        });

      if (!membershipBPs.length) break;

      console.log(
        `Processing membership batch of ${membershipBPs.length} records...`
      );

      const bulkInsertData = [];
      for (const data of membershipBPs) {
        try {
          const bpIDs = await strapi.db
            .query(
              "api::business-partner-relationship.business-partner-relationship"
            )
            .findMany({
              where: {
                bp_id2: { $eq: data.membership_id },
              },
              select: ["bp_id1", "locale"],
            });

          console.log(`Processing bp ${bpIDs.length} entries found`);

          for (const bp_id of bpIDs.map((o) => o.bp_id1)) {
            bulkInsertData.push({
              flex_group_id: data.flex_group_id,
              bp_id,
              locale: data.locale || "en",
            });
          }
        } catch (error) {
          console.error("Error processing membership record:", error);
        }
      }

      if (bulkInsertData.length > 0) {
        const chunkedBulkData = chunkArray(bulkInsertData, 500);
        for (const chunk of chunkedBulkData) {
          await syncBulkCustomerInternal(chunk);
        }
      }

      offset += limit;
    }

    return { success: true, message: "Sync completed successfully." };
  } catch (error) {
    console.error("Sync error:", error);
    return { success: false, message: `Sync failed: ${error.message}` };
  } finally {
    // Reset the flag
    await strapi.db.query("api::setting.setting").update({
      where: { id: setting.id },
      data: { is_fgci_in_progress: false },
    });
  }
};

export { chunkArray, syncBulkCustomerInternal, syncCustomerInternal };
