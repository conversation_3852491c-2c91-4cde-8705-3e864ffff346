{"kind": "collectionType", "collectionName": "product_sales", "info": {"singularName": "product-sale", "pluralName": "product-sales", "displayName": "Product Sales"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"product_id": {"type": "string"}, "sales_status": {"type": "string"}, "sales_status_validity_date": {"type": "datetime"}, "tax_classification": {"type": "string"}, "transportation_group": {"type": "string"}, "product": {"type": "relation", "relation": "oneToOne", "target": "api::product.product", "inversedBy": "sale"}}}