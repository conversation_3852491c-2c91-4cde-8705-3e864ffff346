{"kind": "collectionType", "collectionName": "fg_purchase_info_records", "info": {"singularName": "fg-purchase-info-record", "pluralName": "fg-purchase-info-records", "displayName": "Flexible Purchase Info Record"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"pir_id": {"type": "string"}, "vendor_id": {"type": "string"}, "product_id": {"type": "string"}, "valid_from": {"type": "datetime"}, "valid_to": {"type": "datetime"}, "is_deleted": {"type": "boolean"}}}