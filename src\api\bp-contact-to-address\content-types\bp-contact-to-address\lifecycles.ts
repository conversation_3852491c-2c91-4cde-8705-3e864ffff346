export default {
  async beforeCreate(event) {
    const { params } = event;
    if (!params.data.bp_contact_address_id) {
      try {
        params.data.bp_contact_address_id = `ADD${Date.now()}`;
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async beforeDelete(event) {
    const { where } = event.params;
    const id = where.id;

    // Find related emails
    const emails = await strapi.db
      .query("api::bp-email-address.bp-email-address")
      .findMany({
        where: { bp_contact_address: id },
        select: ["id"],
      });

    // Delete emails one by one (to trigger their hooks)
    for (const email of emails) {
      await strapi.db.query("api::bp-email-address.bp-email-address").delete({
        where: { id: email.id },
      });
    }

    // Find related phones
    const phones = await strapi.db
      .query("api::bp-phone-number.bp-phone-number")
      .findMany({
        where: { bp_contact_address: id },
        select: ["id"],
      });

    // Delete phone one by one (to trigger their hooks)
    for (const phone of phones) {
      await strapi.db.query("api::bp-phone-number.bp-phone-number").delete({
        where: { id: phone.id },
      });
    }

    // Find related fax_numbers
    const fax_numbers = await strapi.db
      .query("api::bp-fax-number.bp-fax-number")
      .findMany({
        where: { bp_contact_address: id },
        select: ["id"],
      });

    // Delete fax_numbers one by one (to trigger their hooks)
    for (const fax_number of fax_numbers) {
      await strapi.db.query("api::bp-fax-number.bp-fax-number").delete({
        where: { id: fax_number.id },
      });
    }

    // Find related urls
    const urls = await strapi.db
      .query("api::bp-home-page-url.bp-home-page-url")
      .findMany({
        where: { bp_contact_address: id },
        select: ["id"],
      });

    // Delete urls one by one (to trigger their hooks)
    for (const url of urls) {
      await strapi.db.query("api::bp-home-page-url.bp-home-page-url").delete({
        where: { id: url.id },
      });
    }
  },
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.relationship_number) {
      try {
        const relationship = await strapi
          .query(
            "api::business-partner-relationship.business-partner-relationship"
          )
          .findOne({
            where: { relationship_number: params.data.relationship_number },
          });

        if (relationship) {
          await strapi
            .query("api::bp-contact-to-address.bp-contact-to-address")
            .update({
              where: { id: result.id },
              data: {
                relationship: {
                  connect: [relationship.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.bp_person_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_person_id },
          });

        if (bp) {
          await strapi
            .query("api::bp-contact-to-address.bp-contact-to-address")
            .update({
              where: { id: result.id },
              data: {
                business_partner_person: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.bp_company_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_company_id },
          });

        if (bp) {
          await strapi
            .query("api::bp-contact-to-address.bp-contact-to-address")
            .update({
              where: { id: result.id },
              data: {
                business_partner_company: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.relationship_number && params.data.bp_person_id) {
      try {
        const contact_person = await strapi
          .query("api::business-partner-contact.business-partner-contact")
          .findOne({
            where: {
              relationship_number: params.data.relationship_number,
              bp_person_id: params.data.bp_person_id,
            },
          });

        if (contact_person) {
          await strapi
            .query("api::bp-contact-to-address.bp-contact-to-address")
            .update({
              where: { id: result.id },
              data: {
                contact_person: {
                  contact_person_address: [contact_person.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.relationship_number && params.data.bp_company_id) {
      try {
        const contact_company = await strapi
          .query("api::business-partner-contact.business-partner-contact")
          .findOne({
            where: {
              relationship_number: params.data.relationship_number,
              bp_company_id: params.data.bp_company_id,
            },
          });

        if (contact_company) {
          await strapi
            .query("api::bp-contact-to-address.bp-contact-to-address")
            .update({
              where: { id: result.id },
              data: {
                contact_company_address: {
                  connect: [contact_company.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.relationship_number) {
      try {
        const relationship = await strapi
          .query(
            "api::business-partner-relationship.business-partner-relationship"
          )
          .findOne({
            where: { relationship_number: params.data.relationship_number },
          });

        if (relationship) {
          await strapi
            .query("api::bp-contact-to-address.bp-contact-to-address")
            .update({
              where: { id: result.id },
              data: {
                relationship: {
                  connect: [relationship.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.relationship_number && result?.relationship?.count === 1) {
          await strapi
            .query("api::bp-contact-to-address.bp-contact-to-address")
            .update({
              where: { id: result.id },
              data: {
                relationship: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.bp_person_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_person_id },
          });

        if (bp) {
          await strapi
            .query("api::bp-contact-to-address.bp-contact-to-address")
            .update({
              where: { id: result.id },
              data: {
                business_partner_person: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.bp_person_id &&
          result?.business_partner_person?.count === 1
        ) {
          await strapi
            .query("api::bp-contact-to-address.bp-contact-to-address")
            .update({
              where: { id: result.id },
              data: {
                business_partner_person: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.bp_company_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_company_id },
          });

        if (bp) {
          await strapi
            .query("api::bp-contact-to-address.bp-contact-to-address")
            .update({
              where: { id: result.id },
              data: {
                business_partner_company: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.bp_company_id &&
          result?.business_partner_company?.count === 1
        ) {
          await strapi
            .query("api::bp-contact-to-address.bp-contact-to-address")
            .update({
              where: { id: result.id },
              data: {
                business_partner_company: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
