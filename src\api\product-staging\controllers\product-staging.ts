/**
 * product-staging controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";
import { SyncProduct } from "../sync";

export default factories.createCoreController(
  "api::product-staging.product-staging",
  ({ strapi }) => ({
    async import(ctx: Context) {
      try {
        const { data } = ctx.request.body;

        if (!Array.isArray(data)) {
          return ctx.badRequest("Input must be an array of records.");
        }

        const CHUNK_SIZE = 50;
        const summary = {
          totalRecords: data.length,
          insertedCount: 0,
          failedCount: 0,
          failedLogs: [],
        };

        for (let i = 0; i < data.length; i += CHUNK_SIZE) {
          const chunk = data.slice(i, i + CHUNK_SIZE);
          try {
            // Bulk insert chunk
            await strapi.db
              .query("api::product-staging.product-staging")
              .createMany({
                data: { ...chunk, locale: "en" },
              });

            summary.insertedCount += chunk.length;
          } catch (error) {
            // If bulk insert fails, insert one by one to catch errors
            for (const record of chunk) {
              try {
                await strapi.db
                  .query("api::product-staging.product-staging")
                  .create({
                    data: { ...record, locale: "en" },
                  });
                summary.insertedCount++;
              } catch (err) {
                summary.failedCount++;
                if (summary.failedLogs.length < 50) {
                  summary.failedLogs.push({
                    record,
                    error: err.message || "Unknown error",
                  });
                }
              }
            }
          }
        }

        ctx.send({
          status: "completed",
          totalRecords: summary.totalRecords,
          inserted: summary.insertedCount,
          failed: summary.failedCount,
          failedLogs: summary.failedLogs,
          failedLogsTruncated: summary.failedLogs.length >= 50,
        });
      } catch (error) {
        console.error("Bulk import failed:", error);
        ctx.internalServerError("Unexpected server error during bulk import.");
      }
    },
    async startSync(ctx: Context) {
      try {
        // Check for any IN_PROCESS records
        const inProcess = await strapi.db
          .connection("product_stagings")
          .where({ staging_status: "IN_PROCESS" })
          .first();

        if (inProcess) {
          return ctx.send({
            message: "Sync Already In Process.",
          });
        }

        // Check for any PENDING records
        const pending = await strapi.db
          .connection("product_stagings")
          .where({ staging_status: "PENDING" })
          .count({ count: "*" })
          .first();

        if (pending.count > 0) {
          SyncProduct();
          return ctx.send({ message: "Sync Started" });
        } else {
          return ctx.send({
            message: "No PENDING records found. Sync not started.",
          });
        }
      } catch (error) {
        console.error("Sync start failed:", error);
        return ctx.internalServerError(
          "Unexpected server error during sync start process."
        );
      }
    },
  })
);
