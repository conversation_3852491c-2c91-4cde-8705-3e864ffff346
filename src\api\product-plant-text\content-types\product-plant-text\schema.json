{"kind": "collectionType", "collectionName": "product_plant_texts", "info": {"singularName": "product-plant-text", "pluralName": "product-plant-texts", "displayName": "Product Plant Text"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"long_text": {"type": "text"}, "plant_id": {"type": "string"}, "plant": {"type": "relation", "relation": "oneToOne", "target": "api::product-plant.product-plant", "inversedBy": "plant_text"}, "product_id": {"type": "string"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "plant_text"}}}