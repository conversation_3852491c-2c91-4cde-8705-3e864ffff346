{"kind": "collectionType", "collectionName": "crm_organisational_unit_functions", "info": {"singularName": "crm-organisational-unit-function", "pluralName": "crm-organisational-unit-functions", "displayName": "CRM Organisational Unit Function"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"end_date": {"type": "date"}, "start_date": {"type": "date"}, "service_organisation_indicator": {"type": "boolean", "default": false}, "service_indicator": {"type": "boolean", "default": false}, "sales_organisation_indicator": {"type": "boolean", "default": false}, "sales_office_indicator": {"type": "boolean", "default": false}, "sales_indicator": {"type": "boolean", "default": false}, "sales_group_indicator": {"type": "boolean", "default": false}, "reporting_line_indicator": {"type": "boolean", "default": false}, "currency_code": {"type": "string"}, "company_indicator": {"type": "boolean", "default": false}, "marketing_indicator": {"type": "boolean", "default": false}, "organisational_unit_id": {"type": "string"}, "organisational_unit": {"type": "relation", "relation": "manyToOne", "target": "api::crm-organisational-unit.crm-organisational-unit", "inversedBy": "crm_org_unit_functions"}}}