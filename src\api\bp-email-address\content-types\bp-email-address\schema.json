{"kind": "collectionType", "collectionName": "bp_email_addresses", "info": {"singularName": "bp-email-address", "pluralName": "bp-email-addresses", "displayName": "Business Partner Email Address"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"address_id": {"type": "string", "required": true}, "ordinal_number": {"type": "integer", "required": true}, "person": {"type": "string", "required": true}, "email_address": {"type": "email"}, "is_default_email_address": {"type": "boolean", "default": false}, "search_email_address": {"type": "string"}, "address_communication_remark_text": {"type": "text"}, "business_partner_address": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner-address.business-partner-address", "inversedBy": "emails"}, "bp_contact_address": {"type": "relation", "relation": "manyToOne", "target": "api::bp-contact-to-address.bp-contact-to-address", "inversedBy": "emails"}}}