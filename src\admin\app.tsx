import type { StrapiApp } from "@strapi/strapi/admin";
import { Store } from "@strapi/icons";
import { delTokenAndUserData, getAdminUserData, STORAGE_KEYS } from "./auth";
// @ts-ignore
import Auth<PERSON>ogo from "./images/auth-logo.png";
// @ts-ignore
import Menu<PERSON><PERSON> from "./images/menu-logo.png";

declare const cookieStore: any;

export default {
  config: {
    // Replace the Strapi logo in auth (login) views
    auth: {
      logo: AuthLogo,
    },
    // Replace the Strapi logo in the main navigation
    menu: {
      logo: MenuLogo,
    },
    // replace favicon with a custom icon
    head: {
      favicon: MenuLogo,
    },
    locales: [
      // 'ar',
      // 'fr',
      // 'cs',
      // 'de',
      // 'dk',
      // 'es',
      // 'he',
      // 'id',
      // 'it',
      // 'ja',
      // 'ko',
      // 'ms',
      // 'nl',
      // 'no',
      // 'pl',
      // 'pt-BR',
      // 'pt',
      // 'ru',
      // 'sk',
      // 'sv',
      // 'th',
      // 'tr',
      // 'uk',
      // 'vi',
      // 'zh-Hans',
      // 'zh',
    ],
  },
  bootstrap(app: StrapiApp) {
    // console.log(app);
    cookieStore.addEventListener("change", ({ changed, deleted }: any) => {
      // Cookie Changed Event
      for (const { name, value } of changed) {
        if (name === STORAGE_KEYS.TOKEN) {
          getAdminUserData(value);
        }
      }
      // Cookie Deleted Event
      for (const { name } of deleted) {
        if (name === STORAGE_KEYS.TOKEN) {
          delTokenAndUserData();
        }
      }
    });
  },
  register(app: StrapiApp) {
    // console.log(app);
    // Store front link in menu
    app.addMenuLink({
      to: "/store",
      icon: Store,
      intlLabel: {
        id: "store.plugin.name",
        defaultMessage: "Store",
      },
      Component: async () => {
        return {
          default: () => {
            // Perform any redirection logic if necessary
            window.location.href = "/index.html"; // Redirect to your desired URL
            return null; // Render nothing
          },
        };
      },
      permissions: [],
      position: -1,
    });
  },
};
