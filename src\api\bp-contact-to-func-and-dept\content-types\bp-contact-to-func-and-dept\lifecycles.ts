export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.relationship_number) {
      try {
        const relationship = await strapi
          .query(
            "api::business-partner-relationship.business-partner-relationship"
          )
          .findOne({
            where: { relationship_number: params.data.relationship_number },
          });

        if (relationship) {
          await strapi
            .query(
              "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
            )
            .update({
              where: { id: result.id },
              data: {
                relationship: {
                  connect: [relationship.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.bp_person_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_person_id },
          });

        if (bp) {
          await strapi
            .query(
              "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner_person: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.bp_company_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_company_id },
          });

        if (bp) {
          await strapi
            .query(
              "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner_company: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (
      params.data.relationship_number &&
      params.data.bp_person_id &&
      params.data.bp_company_id
    ) {
      try {
        const contact_person = await strapi
          .query("api::business-partner-contact.business-partner-contact")
          .findOne({
            where: {
              relationship_number: params.data.relationship_number,
              bp_person_id: params.data.bp_person_id,
              bp_company_id: params.data.bp_company_id,
            },
          });

        if (contact_person) {
          await strapi
            .query(
              "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
            )
            .update({
              where: { id: result.id },
              data: {
                contact_person: {
                  connect: [contact_person.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.relationship_number) {
      try {
        const relationship = await strapi
          .query(
            "api::business-partner-relationship.business-partner-relationship"
          )
          .findOne({
            where: { relationship_number: params.data.relationship_number },
          });

        if (relationship) {
          await strapi
            .query(
              "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
            )
            .update({
              where: { id: result.id },
              data: {
                relationship: {
                  connect: [relationship.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.relationship_number && result?.relationship?.count === 1) {
          await strapi
            .query(
              "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
            )
            .update({
              where: { id: result.id },
              data: {
                relationship: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.bp_person_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_person_id },
          });

        if (bp) {
          await strapi
            .query(
              "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner_person: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.bp_person_id &&
          result?.business_partner_person?.count === 1
        ) {
          await strapi
            .query(
              "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner_person: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.bp_company_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_company_id },
          });

        if (bp) {
          await strapi
            .query(
              "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner_company: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.bp_company_id &&
          result?.business_partner_company?.count === 1
        ) {
          await strapi
            .query(
              "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner_company: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
