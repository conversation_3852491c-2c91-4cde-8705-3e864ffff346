/**
 * configuration custom router
 */

export default {
  routes: [
    {
      method: "POST",
      path: "/business-partner-address/registration",
      handler: "business-partner-address.registration",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "PUT",
      path: "/business-partner-address/:documentId/save",
      handler: "business-partner-address.save",
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
