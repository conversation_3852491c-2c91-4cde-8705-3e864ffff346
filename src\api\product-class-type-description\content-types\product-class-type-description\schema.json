{"kind": "collectionType", "collectionName": "product_class_type_descriptions", "info": {"singularName": "product-class-type-description", "pluralName": "product-class-type-descriptions", "displayName": "Product Class Type Description"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"class_internal_id": {"type": "string"}, "language": {"type": "string"}, "description": {"type": "string"}, "class_internal": {"type": "relation", "relation": "manyToOne", "target": "api::product-class-type.product-class-type", "inversedBy": "descriptions"}}}