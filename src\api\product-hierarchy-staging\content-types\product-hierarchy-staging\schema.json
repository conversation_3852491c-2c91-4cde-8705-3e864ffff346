{"kind": "collectionType", "collectionName": "product_hierarchy_stagings", "info": {"singularName": "product-hierarchy-staging", "pluralName": "product-hierarchy-stagings", "displayName": "Product Hierarchy Staging"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"data": {"type": "json", "required": true}, "staging_status": {"type": "enumeration", "enum": ["PENDING", "IN_PROCESS", "FAILED"], "default": "PENDING"}, "error_message": {"type": "text"}}}