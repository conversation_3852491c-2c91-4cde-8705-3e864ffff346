import { ProductHierarchy } from "./helpers";

const SyncProductHierarchy = async (result) => {
  const { id, documentId, locale, data } = result;

  try {
    const isInProcess = await strapi
      .query("api::product-hierarchy-staging.product-hierarchy-staging")
      .findOne({ where: { staging_status: "IN_PROCESS" } });

    if (isInProcess) {
      // Skip further processing if an IN_PROCESS record exists
      return;
    }

    await strapi.db
      .connection("product_hierarchy_stagings")
      .where({ id })
      .update({
        staging_status: "IN_PROCESS",
      });

    await strapi.db.transaction(async ({ onCommit }) => {
      // Sync Product Hierarchy Data
      if (Array.isArray(data?.product_hierarchies)) {
        for (const ph of data.product_hierarchies) {
          if (!ph?.locale) {
            ph.locale = locale;
          }
          await ProductHierarchy(ph);
        }
      }

      // Runs only if transaction commits successfully
      onCommit(async () => {
        await strapi.db
          .connection("product_hierarchy_stagings")
          .where({ staging_status: "IN_PROCESS" })
          .del();
        const pending = await strapi
          .query("api::product-hierarchy-staging.product-hierarchy-staging")
          .findOne({ where: { staging_status: "PENDING" } });
        if (pending) {
          await SyncProductHierarchy(pending);
        }
      });
    });
  } catch (error) {
    console.error("Transaction failed: ", error);
    await strapi.db
      .connection("product_hierarchy_stagings")
      .insert({
        id,
        document_id: documentId,
        data,
        locale,
        staging_status: "FAILED",
        error_message: error.stack,
        created_at: new Date(),
        updated_at: new Date(),
        published_at: new Date(),
      })
      .onConflict("id") // If id exists, update it
      .merge(["staging_status", "error_message", "updated_at"]); // Update only these fields

    const pending = await strapi
      .query("api::product-hierarchy-staging.product-hierarchy-staging")
      .findOne({ where: { staging_status: "PENDING" } });
    if (pending) {
      await SyncProductHierarchy(pending);
    }
  }
};

const ResetProductHierarchyStagingState = async () => {
  try {
    await strapi.db
      .connection("product_hierarchy_stagings")
      .where({ staging_status: "IN_PROCESS" })
      .update({ staging_status: "PENDING" });

    strapi.log.info("Staging state reset: IN_PROCESS → PENDING");
  } catch (error) {
    strapi.log.error("Failed to reset staging state:", error);
  }
};

export { SyncProductHierarchy, ResetProductHierarchyStagingState };
