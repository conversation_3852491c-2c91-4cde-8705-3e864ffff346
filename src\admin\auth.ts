import axios from "axios";

export const STORAGE_KEYS = {
  TOKEN: "jwtToken",
  USER: "userInfo",
};

const setTokenAndUserData = (token: string, data: any) => {
  localStorage.setItem(STORAGE_KEYS.TOKEN, JSON.stringify(token));
  localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(data));
};

export const delTokenAndUserData = () => {
  localStorage.removeItem(STORAGE_KEYS.TOKEN);
  localStorage.removeItem(STORAGE_KEYS.USER);
};

export const getAdminUserData = async (token: string) => {
  try {
    const response = await axios.get("/admin/users/me", {
      baseURL: "/",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    const { data } = response?.data || null;
    if (data) {
      data.isAdmin = true;
      setTokenAndUserData(token, data);
    }
  } catch (error: any) {
    console.error(
      "Error fetching user data:",
      error.response?.data || error.message
    );
  }
};
