module.exports = (_config, { strapi }) => {
  // const redirects = [
  //   {
  //     method: "GET",
  //     path: "/",
  //     handler: (ctx) => ctx.redirect("/index.html"),
  //     config: { auth: false },
  //   },
  // ];

  // strapi.server.routes(redirects);

  return async (ctx, next) => {
    if (ctx.request.path === "/") {
      // Rewrite the URL internally to serve /index.html content
      ctx.url = "/index.html";
    }
    await next();
  };
};
