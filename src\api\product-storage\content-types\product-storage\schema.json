{"kind": "collectionType", "collectionName": "product_storages", "info": {"singularName": "product-storage", "pluralName": "product-storages", "displayName": "Product Storage"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"product_id": {"type": "string"}, "storage_conditions": {"type": "string"}, "temperature_condition_ind": {"type": "string"}, "hazardous_material_number": {"type": "string"}, "nmbr_of_gr_or_gi_slips_to_print_qty": {"type": "integer"}, "label_type": {"type": "string"}, "label_form": {"type": "string"}, "min_remaining_shelf_life": {"type": "integer"}, "expiration_date": {"type": "datetime"}, "product": {"type": "relation", "relation": "oneToOne", "target": "api::product.product", "inversedBy": "storage"}}}