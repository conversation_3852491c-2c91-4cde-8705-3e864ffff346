{"kind": "collectionType", "collectionName": "fg_customer_businesses", "info": {"singularName": "fg-customer-business", "pluralName": "fg-customer-businesses", "displayName": "Flexible Group Customer Business"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"flex_group_id": {"type": "string"}, "is_all_bp": {"type": "boolean", "default": false}, "membership_id": {"type": "string"}, "membership": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "cb_memberships"}, "bp_id": {"type": "string"}, "business_partner": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "customer_businesses"}, "operand": {"type": "enumeration", "enum": ["ADD", "UPDATE", "DELETE"], "default": "ADD"}}}