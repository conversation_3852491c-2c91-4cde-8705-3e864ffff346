{"kind": "collectionType", "collectionName": "product_sales_deliveries", "info": {"singularName": "product-sales-delivery", "pluralName": "product-sales-deliveries", "displayName": "Product Sales Delivery"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"product_id": {"type": "string"}, "product_sales_org": {"type": "string"}, "product_distribution_chnl": {"type": "string"}, "minimum_order_quantity": {"type": "string"}, "supplying_plant": {"type": "string"}, "price_specification_product_group": {"type": "string"}, "account_detn_product_group": {"type": "string"}, "delivery_note_proc_min_deliv_qty": {"type": "string"}, "item_category_group": {"type": "string"}, "delivery_quantity_unit": {"type": "string"}, "delivery_quantity": {"type": "string"}, "product_sales_status": {"type": "string"}, "product_sales_status_validity_date": {"type": "datetime"}, "sales_measure_unit": {"type": "string"}, "is_marked_for_deletion": {"type": "boolean"}, "product_hierarchy": {"type": "string"}, "first_sales_spec_product_group": {"type": "string"}, "second_sales_spec_product_group": {"type": "string"}, "third_sales_spec_product_group": {"type": "string"}, "fourth_sales_spec_product_group": {"type": "string"}, "fifth_sales_spec_product_group": {"type": "string"}, "minimum_make_to_order_order_qty": {"type": "string"}, "volume_rebate_group": {"type": "string"}, "product_unit_group": {"type": "string"}, "pricing_reference_product": {"type": "string"}, "product_has_attribute_id_01": {"type": "boolean"}, "product_has_attribute_id_02": {"type": "boolean"}, "product_has_attribute_id_03": {"type": "boolean"}, "product_has_attribute_id_04": {"type": "boolean"}, "product_has_attribute_id_05": {"type": "boolean"}, "product_has_attribute_id_06": {"type": "boolean"}, "product_has_attribute_id_07": {"type": "boolean"}, "product_has_attribute_id_08": {"type": "boolean"}, "product_has_attribute_id_09": {"type": "boolean"}, "product_has_attribute_id_10": {"type": "boolean"}, "product_commission_group": {"type": "string"}, "rounding_profile": {"type": "string"}, "cash_discount_is_deductible": {"type": "boolean"}, "variable_sales_unit_is_not_allowed": {"type": "boolean"}, "logistics_statistics_group": {"type": "string"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "sales_deliveries"}, "sales_text": {"type": "relation", "relation": "oneToOne", "target": "api::product-sales-text.product-sales-text", "mappedBy": "sales_delivery"}}}