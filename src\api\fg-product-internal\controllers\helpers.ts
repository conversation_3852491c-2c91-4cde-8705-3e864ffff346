const chunkArray = (array, size) => {
  return Array.from({ length: Math.ceil(array.length / size) }, (_, index) =>
    array.slice(index * size, index * size + size)
  );
};

const getHierarchy = async (node) => {
  const hierarchy = await strapi.entityService.findMany(
    "api::product-hierarchy.product-hierarchy",
    {
      filters: {
        $or: [{ parent_node: node }, { child_node: node }],
      },
      populate: {
        children: {
          populate: "children",
        },
      },
    }
  );

  return hierarchy;
};

const extractProductIds = (node, ids = []) => {
  ids.push(node.product_id);

  if (node.children && node.children.length > 0) {
    node.children.forEach((child) => {
      extractProductIds(child, ids);
    });
  }

  return ids;
};

const fetchProductHierarchy = async (node) => {
  const rootNode = await getHierarchy(node);

  if (!rootNode || rootNode.length === 0) {
    return [];
  }

  let allProductIds = [];
  rootNode.forEach((node) => {
    allProductIds = allProductIds.concat(extractProductIds(node));
  });

  return allProductIds;
};

const syncBulkProductInternal = async (dataBatch) => {
  try {
    if (!dataBatch || dataBatch.length === 0) return;

    const flexGroupIds = dataBatch.map((d) => d.flex_group_id);
    const productIds = dataBatch.map((d) => d.product_id);
    const locales = dataBatch.map((d) => d.locale || "en");

    // Step 1: Find existing records
    const existingRecords = await strapi.db
      .query("api::fg-product-internal.fg-product-internal")
      .findMany({
        where: {
          flex_group_id: { $in: flexGroupIds },
          product_id: { $in: productIds },
          locale: { $in: locales },
        },
        select: ["flex_group_id", "product_id", "locale"],
      });

    // Step 2: Convert existing records into a Set for quick lookup
    const existingSet = new Set(
      existingRecords.map(
        (r) => `${r.flex_group_id}-${r.product_id}-${r.locale}`
      )
    );

    // Step 3: Filter out duplicates before inserting
    const bulkInsertData = dataBatch.filter(
      (d) => !existingSet.has(`${d.flex_group_id}-${d.product_id}-${d.locale}`)
    );

    if (bulkInsertData.length > 0) {
      await strapi.db
        .query("api::fg-product-internal.fg-product-internal")
        .createMany({
          data: bulkInsertData,
        });
    }
  } catch (error) {
    console.error("Bulk insert error in syncBulkProductInternal:", error);
  }
};

const syncProductInternal = async () => {
  const setting = await strapi.db.query("api::setting.setting").findOne({
    select: ["id", "is_fgpi_in_progress"],
  });

  if (!setting) {
    return { success: false, message: "No active sync setting found." };
  }

  if (setting.is_fgpi_in_progress) {
    return { success: false, message: "Sync is already in progress." };
  }

  // Set flag to prevent multiple requests
  await strapi.db.query("api::setting.setting").update({
    where: { id: setting.id },
    data: { is_fgpi_in_progress: true },
  });

  setImmediate(async () => {
    try {
      console.log("Backup FG Product Internal records...");

      await strapi.db.connection.raw(`
            -- Clear existing backup
            TRUNCATE TABLE fg_product_internal_backups;
            -- Insert backup data
            INSERT INTO fg_product_internal_backups SELECT * FROM fg_product_internals;
          `);

      console.log("DONE Backup FG Product Internal records...");

      console.log("Fetching FG Product Business records...");

      let offset = 0;
      const limit = 1000;

      // Delete Records
      while (true) {
        const products = await strapi.db
          .query("api::fg-product-business.fg-product-business")
          .findMany({
            where: {
              flex_group_id: { $ne: null },
              is_all_product: { $eq: false },
              $or: [
                { product_id: { $ne: null } },
                { product_hierarchy: { $ne: null } },
                { vendor_id: { $ne: null } },
              ],
              operand: "DELETE",
            },
            select: [
              "id",
              "flex_group_id",
              "product_id",
              "product_hierarchy",
              "vendor_id",
              "locale",
            ],
            limit,
            offset,
          });

        if (!products.length) break;

        console.log(`Processing batch of ${products.length} records...`);

        const chunkedProducts = chunkArray(products, 500);
        for (const chunk of chunkedProducts) {
          for (const result of chunk) {
            if (result.flex_group_id && result.product_id) {
              const toDelete = await strapi.db
                .query("api::fg-product-internal.fg-product-internal")
                .findMany({
                  where: {
                    flex_group_id: result.flex_group_id,
                    product_id: result.product_id,
                    locale: result.locale || "en",
                  },
                  select: ["id"],
                });

              // Perform bulk deletion using the IDs
              if (toDelete.length > 0) {
                await strapi.db
                  .query("api::fg-product-internal.fg-product-internal")
                  .deleteMany({
                    where: { id: { $in: toDelete.map(({ id }) => id) } },
                  });
              }
            }

            // Remove Product Hierarchy
            if (result.flex_group_id && result.product_hierarchy) {
              const productIds = await fetchProductHierarchy(
                result.product_hierarchy
              );

              const toDelete = await strapi.db
                .query("api::fg-product-internal.fg-product-internal")
                .findMany({
                  where: {
                    flex_group_id: result.flex_group_id,
                    product_id: { $in: productIds },
                    locale: result.locale || "en",
                  },
                  select: ["id"],
                });

              // Perform bulk deletion using the IDs
              if (toDelete.length > 0) {
                await strapi.db
                  .query("api::fg-product-internal.fg-product-internal")
                  .deleteMany({
                    where: { id: { $in: toDelete.map(({ id }) => id) } },
                  });
              }
            }

            // Remove Flexible Purchase Info Record
            if (result.flex_group_id && result.vendor_id) {
              const productIds = await strapi.db
                .query("api::fg-purchase-info-record.fg-purchase-info-record")
                .findMany({
                  where: {
                    vendor_id: result.vendor_id,
                    locale: result.locale || "en",
                  },
                  select: ["product_id"],
                });

              const toDelete = await strapi.db
                .query("api::fg-product-internal.fg-product-internal")
                .findMany({
                  where: {
                    flex_group_id: result.flex_group_id,
                    product_id: {
                      $in: productIds.map((p) => p.product_id),
                    },
                    locale: result.locale || "en",
                  },
                  select: ["id"],
                });

              // Perform bulk deletion using the IDs
              if (toDelete.length > 0) {
                await strapi.db
                  .query("api::fg-product-internal.fg-product-internal")
                  .deleteMany({
                    where: { id: { $in: toDelete.map(({ id }) => id) } },
                  });
              }
            }

            if (result?.id) {
              await strapi.db
                .query("api::fg-product-business.fg-product-business")
                .delete({
                  where: { id: result.id },
                });
            }
          }
        }

        offset += limit;
      }

      console.log(
        "Initial batch deletion completed. Processing batch insert data..."
      );

      offset = 0;

      while (true) {
        const products = await strapi.db
          .query("api::fg-product-business.fg-product-business")
          .findMany({
            where: {
              flex_group_id: { $ne: null },
              product_id: { $ne: null },
              is_all_product: { $eq: false },
              $or: [{ operand: "ADD" }, { operand: "UPDATE" }],
            },
            select: ["id", "flex_group_id", "product_id", "locale"],
            limit,
            offset,
          });

        if (!products.length) break;

        console.log(`Processing batch of ${products.length} records...`);

        const chunkedProducts = chunkArray(products, 500);
        for (const chunk of chunkedProducts) {
          await syncBulkProductInternal(chunk).catch((error) =>
            console.error("Bulk sync error:", error)
          );
        }

        offset += limit;
      }

      console.log(
        "Initial batch insert completed. Processing hierarchical data..."
      );

      offset = 0;

      while (true) {
        const hierarchicalProducts = await strapi.db
          .query("api::fg-product-business.fg-product-business")
          .findMany({
            where: {
              flex_group_id: { $ne: null },
              product_hierarchy: { $ne: null },
              is_all_product: { $eq: false },
              $or: [{ operand: "ADD" }, { operand: "UPDATE" }],
            },
            select: ["id", "flex_group_id", "product_hierarchy", "locale"],
            limit,
            offset,
          });

        if (!hierarchicalProducts.length) break;

        console.log(
          `Processing hierarchical batch of ${hierarchicalProducts.length} records...`
        );

        const bulkInsertData = [];
        for (const data of hierarchicalProducts) {
          try {
            const productIds = await fetchProductHierarchy(
              data.product_hierarchy
            );
            console.log(
              `Processing hierarchical ${productIds.length} entries found`
            );
            for (const product_id of productIds) {
              bulkInsertData.push({
                flex_group_id: data.flex_group_id,
                product_id,
                locale: data.locale || "en",
              });
            }
          } catch (error) {
            console.error("Error processing product hierarchy record:", error);
          }
        }

        if (bulkInsertData.length > 0) {
          const chunkedBulkData = chunkArray(bulkInsertData, 500);
          for (const chunk of chunkedBulkData) {
            await syncBulkProductInternal(chunk).catch((error) =>
              console.error("Bulk insert error:", error)
            );
          }
        }

        offset += limit;
      }

      console.log(
        "Initial batch insert completed for product hierarchical data. Processing Flexible Purchase Info Record data..."
      );

      offset = 0;

      while (true) {
        const vendorFGs = await strapi.db
          .query("api::fg-product-business.fg-product-business")
          .findMany({
            where: {
              flex_group_id: { $ne: null },
              vendor_id: { $ne: null },
              is_all_product: { $eq: false },
              $or: [{ operand: "ADD" }, { operand: "UPDATE" }],
            },
            select: ["id", "flex_group_id", "vendor_id", "locale"],
            limit,
            offset,
          });

        if (!vendorFGs.length) break;

        console.log(
          `Processing Flexible Vendor batch of ${vendorFGs.length} records...`
        );

        const bulkInsertData = [];
        for (const data of vendorFGs) {
          try {
            const productIds = await strapi.db
              .query("api::fg-purchase-info-record.fg-purchase-info-record")
              .findMany({
                where: {
                  vendor_id: data.vendor_id,
                  locale: data.locale || "en",
                },
                select: ["product_id"],
              });
            console.log(
              `Processing Flexible Purchase Info Record ${productIds.length} entries found`
            );
            for (const product of productIds) {
              bulkInsertData.push({
                flex_group_id: data.flex_group_id,
                product_id: product.product_id,
                locale: data.locale || "en",
              });
            }
          } catch (error) {
            console.error(
              "Error processing Flexible Purchase Info Record:",
              error
            );
          }
        }

        if (bulkInsertData.length > 0) {
          const chunkedBulkData = chunkArray(bulkInsertData, 500);
          for (const chunk of chunkedBulkData) {
            await syncBulkProductInternal(chunk).catch((error) =>
              console.error("Bulk insert error:", error)
            );
          }
        }

        offset += limit;
      }
    } catch (error) {
      console.error("Sync error:", error);
    } finally {
      // Reset flag to allow next sync
      await strapi.db.query("api::setting.setting").update({
        where: { id: setting.id },
        data: { is_fgpi_in_progress: false },
      });

      // Insert Data into Customer Product Internal Table
      await strapi.db.connection.raw(`
            -- Clear existing data
            TRUNCATE TABLE fg_customer_product_internals;
            -- Insert new data
            INSERT INTO fg_customer_product_internals (flex_group_id, product_id, locale, bp_id)
            SELECT 
              fpi.flex_group_id,
              fpi.product_id,
              fpi.locale,
              fci.bp_id
            FROM fg_product_internals fpi
            JOIN LATERAL (
              SELECT fci.bp_id
              FROM fg_customer_internals fci
              WHERE fci.flex_group_id = fpi.flex_group_id
              ORDER BY fci.id
              LIMIT 1
            ) fci ON true;
          `);
    }
  });

  return { success: true, message: "Sync completed successfully." };
};

export {
  chunkArray,
  fetchProductHierarchy,
  syncBulkProductInternal,
  syncProductInternal,
};
