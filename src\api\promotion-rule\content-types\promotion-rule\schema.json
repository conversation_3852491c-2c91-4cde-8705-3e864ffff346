{"kind": "collectionType", "collectionName": "promotion_rules", "info": {"singularName": "promotion-rule", "pluralName": "promotion-rules", "displayName": "Promotion Rule"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"attribute": {"type": "string", "required": true}, "operator": {"type": "enumeration", "enum": ["GTE", "LTE", "GT", "LT", "EQ", "NE", "IN"], "required": true}, "description": {"type": "text"}, "values": {"type": "relation", "relation": "oneToMany", "target": "api::promotion-rule-value.promotion-rule-value", "mappedBy": "promotion_rule"}, "promotions": {"type": "relation", "relation": "manyToMany", "target": "api::promotion.promotion", "inversedBy": "rules"}, "method_target_rules": {"type": "relation", "relation": "manyToMany", "target": "api::promotion-application-method.promotion-application-method", "inversedBy": "target_rules"}, "method_buy_rules": {"type": "relation", "relation": "manyToMany", "target": "api::promotion-application-method.promotion-application-method", "inversedBy": "buy_rules"}}}