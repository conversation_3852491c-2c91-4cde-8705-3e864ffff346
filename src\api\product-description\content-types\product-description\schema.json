{"kind": "collectionType", "collectionName": "product_descriptions", "info": {"singularName": "product-description", "pluralName": "product-descriptions", "displayName": "Product Description"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"product_id": {"type": "string"}, "language": {"type": "string"}, "description": {"type": "string"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "descriptions"}}}