{"kind": "collectionType", "collectionName": "product_suggestion_types", "info": {"singularName": "product-suggestion-type", "pluralName": "product-suggestion-types", "displayName": "Product Suggestion Type"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"code": {"type": "string", "unique": true, "column": {"unique": true}}, "description": {"type": "string"}}}