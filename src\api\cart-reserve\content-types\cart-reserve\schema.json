{"kind": "collectionType", "collectionName": "cart_reserves", "info": {"singularName": "cart-reserve", "pluralName": "cart-reserves", "displayName": "Cart Reserve"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "required": true}, "description": {"type": "string"}, "customer": {"type": "relation", "relation": "manyToOne", "target": "api::customer.customer", "inversedBy": "cart_reserves"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "cart_reserves"}, "admin_user": {"type": "relation", "relation": "manyToOne", "target": "admin::user"}, "cart_items": {"type": "relation", "relation": "oneToMany", "target": "api::cart-reserve-item.cart-reserve-item", "mappedBy": "cart"}}}