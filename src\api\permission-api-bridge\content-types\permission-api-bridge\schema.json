{"kind": "collectionType", "collectionName": "permission_api_bridges", "info": {"singularName": "permission-api-bridge", "pluralName": "permission-api-bridges", "displayName": "Permission API Bridge"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"role": {"type": "relation", "relation": "manyToOne", "target": "admin::role"}, "moduleUID": {"type": "string", "required": true}, "module": {"type": "string"}, "canCreate": {"type": "boolean", "default": false}, "canRead": {"type": "boolean", "default": false}, "canUpdate": {"type": "boolean", "default": false}, "canDelete": {"type": "boolean", "default": false}}}