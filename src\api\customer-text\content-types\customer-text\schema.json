{"kind": "collectionType", "collectionName": "customer_texts", "info": {"singularName": "customer-text", "pluralName": "customer-texts", "displayName": "Customer Text", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"long_text_id": {"type": "string", "required": true}, "language": {"type": "string", "required": true}, "long_text": {"type": "text"}, "customer_id": {"type": "string", "required": true}, "customer": {"type": "relation", "relation": "manyToOne", "target": "api::customer.customer", "inversedBy": "customer_texts"}}}