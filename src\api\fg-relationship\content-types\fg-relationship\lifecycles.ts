export default {
  async afterCreate(event) {
    const { result } = event;
    if (result.operand === "DELETE") {
      try {
        await strapi.db.query("api::fg-relationship.fg-relationship").delete({
          where: { id: result.id },
        });
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result } = event;
    if (result.operand === "DELETE") {
      try {
        await strapi.db.query("api::fg-relationship.fg-relationship").delete({
          where: { id: result.id },
        });
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    }
  },
};
