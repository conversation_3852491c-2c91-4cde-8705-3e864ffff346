export default {
  async afterCreate(event) {
    const { result, params } = event;

    if (params.data.party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.party_id },
          });

        if (bp) {
          await strapi
            .query("api::crm-opportunity-party.crm-opportunity-party")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.opportunity_id) {
      try {
        const opportunity = await strapi
          .query("api::crm-opportunity.crm-opportunity")
          .findOne({
            where: { opportunity_id: params.data.opportunity_id },
          });

        if (opportunity) {
          await strapi
            .query("api::crm-opportunity-party.crm-opportunity-party")
            .update({
              where: { id: result.id },
              data: {
                opportunity: {
                  connect: [opportunity.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.party_id },
          });

        if (bp) {
          await strapi
            .query("api::crm-opportunity-party.crm-opportunity-party")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.party_id && result?.business_partner?.count === 1) {
          await strapi
            .query("api::crm-opportunity-party.crm-opportunity-party")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.opportunity_id) {
      try {
        const opportunity = await strapi
          .query("api::crm-opportunity.crm-opportunity")
          .findOne({
            where: { opportunity_id: params.data.opportunity_id },
          });

        if (opportunity) {
          await strapi
            .query("api::crm-opportunity-party.crm-opportunity-party")
            .update({
              where: { id: result.id },
              data: {
                opportunity: {
                  connect: [opportunity.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.opportunity_id && result?.opportunity?.count === 1) {
          await strapi
            .query("api::crm-opportunity-party.crm-opportunity-party")
            .update({
              where: { id: result.id },
              data: {
                opportunity: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
