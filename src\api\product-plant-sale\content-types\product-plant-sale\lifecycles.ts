export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi
            .query("api::product-plant-sale.product-plant-sale")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.plant_id && params.data.product_id) {
      try {
        const plant = await strapi
          .query("api::product-plant.product-plant")
          .findOne({
            where: {
              product_id: params.data.product_id,
              plant: params.data.plant_id,
            },
          });

        if (plant) {
          await strapi
            .query("api::product-plant-sale.product-plant-sale")
            .update({
              where: { id: result.id },
              data: {
                plant: {
                  connect: [plant.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi
            .query("api::product-plant-sale.product-plant-sale")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.product_id && result?.product?.count === 1) {
          await strapi
            .query("api::product-plant-sale.product-plant-sale")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error(
          "Error in afterUpdate when disconnecting business partner:",
          error
        );
      }
    }

    if (params.data.plant_id && params.data.product_id) {
      try {
        const plant = await strapi
          .query("api::product-plant.product-plant")
          .findOne({
            where: {
              product_id: params.data.product_id,
              plant: params.data.plant_id,
            },
          });

        if (plant) {
          await strapi
            .query("api::product-plant-sale.product-plant-sale")
            .update({
              where: { id: result.id },
              data: {
                plant: {
                  connect: [plant.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.product_id &&
          !result.plant_id &&
          result?.plant?.count === 1
        ) {
          await strapi
            .query("api::product-plant-sale.product-plant-sale")
            .update({
              where: { id: result.id },
              data: {
                plant: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error(
          "Error in afterUpdate when disconnecting business partner:",
          error
        );
      }
    }
  },
};
