{"kind": "collectionType", "collectionName": "permission_pims", "info": {"singularName": "permission-pim", "pluralName": "permission-pims", "displayName": "Permission PIM"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "code": {"type": "string", "required": true, "unique": true, "column": {"unique": true}}, "user_role": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.role"}, "admin_user_role": {"type": "relation", "relation": "oneToMany", "target": "admin::role"}}}