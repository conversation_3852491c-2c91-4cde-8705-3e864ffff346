{"kind": "collectionType", "collectionName": "import_file_logs", "info": {"singularName": "import-file-log", "pluralName": "import-file-logs", "displayName": "Import File Log"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"import_file_id": {"type": "relation", "relation": "manyToOne", "target": "api::import-file-state.import-file-state", "inversedBy": "import_file_logs"}, "row_number": {"type": "integer"}, "log_status": {"type": "enumeration", "enum": ["SUCCESS", "FAILED"]}, "message": {"type": "string"}, "data": {"type": "json"}}}