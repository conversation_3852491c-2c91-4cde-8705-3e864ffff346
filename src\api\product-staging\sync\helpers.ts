const Product = async (data) => {
  const { product_id, locale } = data;

  if (!product_id) throw new Error("product_id is required");

  try {
    // Check if the product already exists by product_id with locale
    const existingProduct = await strapi.db
      .query("api::product.product")
      .findOne({
        where: { product_id, locale },
      });

    if (existingProduct) {
      // Update the existing product
      await strapi.db.query("api::product.product").update({
        where: { id: existingProduct.id },
        data,
      });
    } else {
      // Create a new product
      await strapi.db.query("api::product.product").create({
        data,
      });
    }
  } catch (error) {
    throw new Error(`Unable to synchronize Product ::: ${error.message}`);
  }
};

const ProductClassCharcType = async (data) => {
  const { charc_internal_id, locale } = data;

  if (!charc_internal_id) throw new Error("charc_internal_id is required");

  try {
    // Check if a product class charc type record already exists for the given charc_internal_id
    const existingRecord = await strapi.db
      .query("api::product-class-charc-type.product-class-charc-type")
      .findOne({
        where: { charc_internal_id, locale },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db
        .query("api::product-class-charc-type.product-class-charc-type")
        .update({
          where: { id: existingRecord.id },
          data,
        });
    } else {
      // Create a new record
      await strapi.db
        .query("api::product-class-charc-type.product-class-charc-type")
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Class Charc Type ::: ${error.message}`
    );
  }
};

const ProductClassCharcTypeDescription = async (data) => {
  const { charc_internal_id, language, locale } = data;

  if (!charc_internal_id || !language) {
    throw new Error("charc_internal_id and language are required");
  }

  try {
    // Check if the record already exists, considering the locale (language)
    const existingRecord = await strapi.db
      .query(
        "api::product-class-charc-type-description.product-class-charc-type-description"
      )
      .findOne({
        where: { charc_internal_id, language, locale },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db
        .query(
          "api::product-class-charc-type-description.product-class-charc-type-description"
        )
        .update({
          where: { id: existingRecord.id },
          data,
        });
    } else {
      // Create a new record
      await strapi.db
        .query(
          "api::product-class-charc-type-description.product-class-charc-type-description"
        )
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Class Charc Type Description ::: ${error.message}`
    );
  }
};

const ProductClassType = async (data) => {
  const { class_internal_id, locale } = data;

  if (!class_internal_id) {
    throw new Error("class_internal_id is required");
  }

  try {
    // Check if the product class type record already exists for the given class_internal_id
    const existingRecord = await strapi.db
      .query("api::product-class-type.product-class-type")
      .findOne({
        where: { class_internal_id, locale },
      });

    if (existingRecord) {
      // Update the existing product class type record
      await strapi.db
        .query("api::product-class-type.product-class-type")
        .update({
          where: { id: existingRecord.id },
          data,
        });
    } else {
      // Create a new product class type record
      await strapi.db
        .query("api::product-class-type.product-class-type")
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Class Type ::: ${error.message}`
    );
  }
};

const ProductClassTypeDescription = async (data) => {
  const { class_internal_id, language, locale } = data;

  if (!class_internal_id || !language) {
    throw new Error("class_internal_id and language are required");
  }

  try {
    // Check if the record already exists, considering the locale
    const existingRecord = await strapi.db
      .query(
        "api::product-class-type-description.product-class-type-description"
      )
      .findOne({
        where: { class_internal_id, language, locale },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db
        .query(
          "api::product-class-type-description.product-class-type-description"
        )
        .update({
          where: { id: existingRecord.id },
          data,
        });
    } else {
      // Create a new record with the specified locale
      await strapi.db
        .query(
          "api::product-class-type-description.product-class-type-description"
        )
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Class Type Description ::: ${error.message}`
    );
  }
};

const ProductCharcValueType = async (data) => {
  const {
    product_id,
    class_internal_id,
    charc_internal_id,
    charc_value_position_number,
    locale,
  } = data;

  if (
    !product_id ||
    !class_internal_id ||
    !charc_internal_id ||
    !charc_value_position_number
  ) {
    throw new Error(
      "product_id, class_internal_id, charc_internal_id, and charc_value_position_number are required"
    );
  }

  try {
    // Check if a product charc value type record already exists
    const existingRecord = await strapi.db
      .query("api::product-charc-value-type.product-charc-value-type")
      .findOne({
        where: {
          product_id,
          class_internal_id,
          charc_internal_id,
          charc_value_position_number,
          locale,
        },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db
        .query("api::product-charc-value-type.product-charc-value-type")
        .update({
          where: { id: existingRecord.id },
          data,
        });
    } else {
      // Create a new record
      await strapi.db
        .query("api::product-charc-value-type.product-charc-value-type")
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Charc Value Type ::: ${error.message}`
    );
  }
};

const ProductDescription = async (data) => {
  const { product_id, language, locale } = data;

  if (!product_id || !language) {
    throw new Error("product_id and language are required");
  }

  try {
    // Check if a product description already exists
    const existingRecord = await strapi.db
      .query("api::product-description.product-description")
      .findOne({
        where: { product_id, language, locale },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db
        .query("api::product-description.product-description")
        .update({
          where: { id: existingRecord.id },
          data,
        });
    } else {
      // Create a new record
      await strapi.db
        .query("api::product-description.product-description")
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Description ::: ${error.message}`
    );
  }
};

const ProductPlant = async (data) => {
  const { product_id, plant, locale } = data;

  if (!product_id || !plant) {
    throw new Error("product_id and plant are required");
  }

  try {
    // Check if a product plant record already exists
    const existingRecord = await strapi.db
      .query("api::product-plant.product-plant")
      .findOne({
        where: { product_id, plant, locale },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db.query("api::product-plant.product-plant").update({
        where: { id: existingRecord.id },
        data,
      });
    } else {
      // Create a new record
      await strapi.db.query("api::product-plant.product-plant").create({
        data,
      });
    }
  } catch (error) {
    throw new Error(`Unable to synchronize Product Plant ::: ${error.message}`);
  }
};

const ProductSalesDelivery = async (data) => {
  const { product_id, product_sales_org, product_distribution_chnl, locale } =
    data;

  if (!product_id || !product_sales_org || !product_distribution_chnl) {
    throw new Error(
      "product_id, product_sales_org and product_distribution_chnl are required"
    );
  }

  try {
    // Check if the product sales delivery record already exists
    const existingDelivery = await strapi.db
      .query("api::product-sales-delivery.product-sales-delivery")
      .findOne({
        where: {
          product_id,
          product_sales_org,
          product_distribution_chnl,
          locale,
        },
      });

    if (existingDelivery) {
      // Update the existing product sales delivery record
      return await strapi.db
        .query("api::product-sales-delivery.product-sales-delivery")
        .update({
          where: { id: existingDelivery.id },
          data,
        });
    } else {
      // Create a new product sales delivery record
      return await strapi.db
        .query("api::product-sales-delivery.product-sales-delivery")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Sales Delivery ::: ${error.message}`
    );
  }
};

const ProductStorage = async (data) => {
  const { product_id, locale } = data;

  if (!product_id) {
    throw new Error("product_id is required");
  }

  try {
    // Check if the product storage record already exists
    const existingStorage = await strapi.db
      .query("api::product-storage.product-storage")
      .findOne({
        where: { product_id, locale },
      });

    if (existingStorage) {
      // Update the existing product storage record
      return await strapi.db
        .query("api::product-storage.product-storage")
        .update({
          where: { id: existingStorage.id },
          data,
        });
    } else {
      // Create a new product storage record
      return await strapi.db
        .query("api::product-storage.product-storage")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Storage ::: ${error.message}`
    );
  }
};

const ProductStorageLocation = async (data) => {
  const { product_id, plant_id, storage_location, locale } = data;

  if (!product_id || !plant_id || !storage_location) {
    throw new Error("product_id, plant_id, and storage_location are required");
  }

  try {
    // Check if the product storage location record already exists
    const existingStorage = await strapi.db
      .query("api::product-storage-location.product-storage-location")
      .findOne({
        where: { product_id, plant_id, storage_location, locale },
      });

    if (existingStorage) {
      // Update the existing product storage location record
      return await strapi.db
        .query("api::product-storage-location.product-storage-location")
        .update({
          where: { id: existingStorage.id },
          data,
        });
    } else {
      // Create a new product storage location record
      return await strapi.db
        .query("api::product-storage-location.product-storage-location")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Storage Location ::: ${error.message}`
    );
  }
};

const ProductSalesTax = async (data) => {
  const { product_id, country, tax_category, tax_classification, locale } =
    data;

  if (!product_id || !country || !tax_category || !tax_classification) {
    throw new Error(
      "product_id, country, tax_classification, and tax_category are required"
    );
  }

  try {
    // Check if the product sales tax record already exists
    const existingTax = await strapi.db
      .query("api::product-sales-tax.product-sales-tax")
      .findOne({
        where: {
          product_id,
          country,
          tax_category,
          tax_classification,
          locale,
        },
      });

    if (existingTax) {
      // Update the existing product sales tax record
      return await strapi.db
        .query("api::product-sales-tax.product-sales-tax")
        .update({
          where: { id: existingTax.id },
          data,
        });
    } else {
      // Create a new product sales tax record
      return await strapi.db
        .query("api::product-sales-tax.product-sales-tax")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Sales Tax ::: ${error.message}`
    );
  }
};

const ProductPlantProcurement = async (data) => {
  const { product_id, plant_id, locale } = data;

  if (!product_id || !plant_id) {
    throw new Error("product_id and plant_id are required");
  }

  try {
    // Check if the product plant procurement record already exists
    const existingProcurement = await strapi.db
      .query("api::product-plant-procurement.product-plant-procurement")
      .findOne({
        where: { product_id, plant_id, locale },
      });

    if (existingProcurement) {
      // Update the existing product plant procurement record
      return await strapi.db
        .query("api::product-plant-procurement.product-plant-procurement")
        .update({
          where: { id: existingProcurement.id },
          data,
        });
    } else {
      // Create a new product plant procurement record
      return await strapi.db
        .query("api::product-plant-procurement.product-plant-procurement")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Plant Procurement ::: ${error.message}`
    );
  }
};

const ProductPlantSales = async (data) => {
  const { product_id, plant_id, locale } = data;

  if (!product_id || !plant_id) {
    throw new Error("product_id and plant_id are required");
  }

  try {
    // Check if the product plant sale record already exists
    const existingSale = await strapi.db
      .query("api::product-plant-sale.product-plant-sale")
      .findOne({
        where: { product_id, plant_id, locale },
      });

    if (existingSale) {
      // Update the existing product plant sale record
      return await strapi.db
        .query("api::product-plant-sale.product-plant-sale")
        .update({
          where: { id: existingSale.id },
          data,
        });
    } else {
      // Create a new product plant sale record
      return await strapi.db
        .query("api::product-plant-sale.product-plant-sale")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Plant Sale ::: ${error.message}`
    );
  }
};

const ProductSales = async (data) => {
  const { product_id, locale } = data;

  if (!product_id) {
    throw new Error("product_id are required");
  }

  try {
    // Check if the product sale record already exists
    const existingSale = await strapi.db
      .query("api::product-sale.product-sale")
      .findOne({
        where: { product_id, locale },
      });

    if (existingSale) {
      // Update the existing product sale record
      return await strapi.db.query("api::product-sale.product-sale").update({
        where: { id: existingSale.id },
        data,
      });
    } else {
      // Create a new product sale record
      return await strapi.db
        .query("api::product-sale.product-sale")
        .create({ data });
    }
  } catch (error) {
    throw new Error(`Unable to synchronize Product Sales ::: ${error.message}`);
  }
};

const ProductSalesText = async (data) => {
  const { product_id, product_sales_org, language, locale } = data;

  if (!product_id || !product_sales_org || !language) {
    throw new Error("product_id, product_sales_org, and language are required");
  }

  try {
    // Check if the product sales text record already exists
    const existingSalesText = await strapi.db
      .query("api::product-sales-text.product-sales-text")
      .findOne({
        where: { product_id, product_sales_org, language, locale },
      });

    if (existingSalesText) {
      // Update existing product sales text
      return await strapi.db
        .query("api::product-sales-text.product-sales-text")
        .update({
          where: { id: existingSalesText.id },
          data,
        });
    } else {
      // Create a new product sales text record
      return await strapi.db
        .query("api::product-sales-text.product-sales-text")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Sales Text ::: ${error.message}`
    );
  }
};

const ProductBasicText = async (data) => {
  const { product_id, language, locale } = data;

  if (!product_id || !language) {
    throw new Error("product_id and language are required");
  }

  try {
    // Check if the product basic text record already exists
    const existingBasicText = await strapi.db
      .query("api::product-basic-text.product-basic-text")
      .findOne({
        where: { product_id, language, locale },
      });

    if (existingBasicText) {
      // Update existing product basic text
      return await strapi.db
        .query("api::product-basic-text.product-basic-text")
        .update({
          where: { id: existingBasicText.id },
          data,
        });
    } else {
      // Create a new product basic text record
      return await strapi.db
        .query("api::product-basic-text.product-basic-text")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Basic Text ::: ${error.message}`
    );
  }
};

const ProductPlantText = async (data) => {
  const { product_id, plant_id, locale } = data;

  if (!product_id || !plant_id) {
    throw new Error("product_id and plant_id are required");
  }

  try {
    // Check if the product plant text already exists
    const existingText = await strapi.db
      .query("api::product-plant-text.product-plant-text")
      .findOne({
        where: { product_id, plant_id, locale },
      });

    if (existingText) {
      // Update existing text
      return await strapi.db
        .query("api::product-plant-text.product-plant-text")
        .update({
          where: { id: existingText.id },
          data,
        });
    } else {
      // Create new product plant text
      return await strapi.db
        .query("api::product-plant-text.product-plant-text")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Plant Text ::: ${error.message}`
    );
  }
};

const ProductPlantStorage = async (data) => {
  const { product_id, plant_id } = data;

  if (!product_id || !plant_id) {
    throw new Error("product_id and plant_id are required");
  }

  try {
    // Check if the product plant storage already exists
    const existingStorage = await strapi.db
      .query("api::product-plant-storage.product-plant-storage")
      .findOne({
        where: { product_id, plant_id },
      });

    if (existingStorage) {
      // Update existing storage
      return await strapi.db
        .query("api::product-plant-storage.product-plant-storage")
        .update({
          where: { id: existingStorage.id },
          data,
        });
    } else {
      // Create new product plant storage
      return await strapi.db
        .query("api::product-plant-storage.product-plant-storage")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Plant Storage ::: ${error.message}`
    );
  }
};

const ProductUnitsOfMeasure = async (data) => {
  const { product_id, alternative_unit } = data;

  if (!product_id || !alternative_unit) {
    throw new Error("product_id and alternative_unit are required");
  }

  try {
    // Check if the product units of measure already exists
    const existingUnits = await strapi.db
      .query("api::product-units-of-measure.product-units-of-measure")
      .findOne({
        where: { product_id, alternative_unit },
      });

    if (existingUnits) {
      // Update existing units of measure record
      return await strapi.db
        .query("api::product-units-of-measure.product-units-of-measure")
        .update({
          where: { id: existingUnits.id },
          data,
        });
    } else {
      // Create new product units of measure record
      return await strapi.db
        .query("api::product-units-of-measure.product-units-of-measure")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Units Of Measure ::: ${error.message}`
    );
  }
};

const ProductUnitsOfMeasureEAN = async (data) => {
  const { product_id, alternative_unit, consecutive_number } = data;

  if (!product_id || !alternative_unit || !consecutive_number) {
    throw new Error(
      "product_id, alternative_unit and consecutive_number are required"
    );
  }

  try {
    // Check if the product units of measure EAN already exists
    const existingEAN = await strapi.db
      .query("api::product-units-of-measure-ean.product-units-of-measure-ean")
      .findOne({
        where: { product_id, alternative_unit, consecutive_number },
      });

    if (existingEAN) {
      // Update existing EAN record
      return await strapi.db
        .query("api::product-units-of-measure-ean.product-units-of-measure-ean")
        .update({
          where: { id: existingEAN.id },
          data,
        });
    } else {
      // Create new product units of measure EAN record
      return await strapi.db
        .query("api::product-units-of-measure-ean.product-units-of-measure-ean")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Units Of Measure EAN ::: ${error.message}`
    );
  }
};

export {
  Product,
  ProductClassCharcType,
  ProductClassCharcTypeDescription,
  ProductClassType,
  ProductClassTypeDescription,
  ProductCharcValueType,
  ProductDescription,
  ProductPlant,
  ProductSalesDelivery,
  ProductStorage,
  ProductStorageLocation,
  ProductSalesTax,
  ProductSales,
  ProductSalesText,
  ProductBasicText,
  ProductPlantText,
  ProductPlantStorage,
  ProductPlantProcurement,
  ProductPlantSales,
  ProductUnitsOfMeasure,
  ProductUnitsOfMeasureEAN,
};
