{"kind": "collectionType", "collectionName": "crm_organisational_unit_managers", "info": {"singularName": "crm-organisational-unit-manager", "pluralName": "crm-organisational-unit-managers", "displayName": "CRM Organisational Unit Manager"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"functional_manager_indicator": {"type": "boolean", "default": false}, "reporting_line_manager_indicator": {"type": "boolean", "default": false}, "end_date": {"type": "date"}, "start_date": {"type": "date"}, "organisational_unit_id": {"type": "string"}, "organisational_unit": {"type": "relation", "relation": "manyToOne", "target": "api::crm-organisational-unit.crm-organisational-unit", "inversedBy": "crm_org_unit_managers"}, "business_partner_internal_id": {"type": "string"}, "business_partner": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "crm_org_unit_managers"}}}