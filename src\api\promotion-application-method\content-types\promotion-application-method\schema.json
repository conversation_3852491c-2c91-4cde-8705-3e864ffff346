{"kind": "collectionType", "collectionName": "promotion_application_methods", "info": {"singularName": "promotion-application-method", "pluralName": "promotion-application-methods", "displayName": "Promotion Application Method"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"type": {"type": "enumeration", "enum": ["FIXED", "PERCENTAGE"], "required": true, "default": "FIXED"}, "target_type": {"type": "enumeration", "enum": ["CART_ITEMS", "SHIPPING_METHODS", "ORDER"], "required": true}, "value": {"type": "decimal"}, "currency_code": {"type": "string"}, "max_quantity": {"type": "integer"}, "apply_to_quantity": {"type": "integer"}, "buy_rules_min_quantity": {"type": "integer"}, "allocation": {"type": "enumeration", "enum": ["EACH", "ACROSS"]}, "promotion": {"type": "relation", "relation": "manyToOne", "target": "api::promotion.promotion", "inversedBy": "application_method"}, "target_rules": {"type": "relation", "relation": "manyToMany", "target": "api::promotion-rule.promotion-rule", "mappedBy": "method_target_rules"}, "buy_rules": {"type": "relation", "relation": "manyToMany", "target": "api::promotion-rule.promotion-rule", "mappedBy": "method_buy_rules"}}}