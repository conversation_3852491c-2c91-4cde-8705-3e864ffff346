{"kind": "collectionType", "collectionName": "partner_function_configs", "info": {"singularName": "partner-function-config", "pluralName": "partner-function-configs", "displayName": "Partner Function Config"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"pf_code": {"type": "string"}, "type": {"type": "enumeration", "enum": ["ACTIVITY", "SALES_ORDER", "SALES_QUOTE", "ACCOUNT", "CONTACT", "PROSPECT", "OPPORTUNITY", "SERVICE_TICKET"]}, "category": {"type": "enumeration", "enum": ["APPOINTMENT", "TASK", "PHONE_CALL", "EMAIL"]}}}