export default {
  /**
   * Called before a import file state is deleted.
   * Deletes the import file logs associated with the import file state.
   */
  async beforeDelete({ params }) {
    const importFileStateId = params?.where?.id;

    if (importFileStateId) {
      try {
        // Fetch all related logs
        const toDelete = await strapi.db
          .query("api::import-file-log.import-file-log")
          .findMany({
            where: { import_file_id: importFileStateId },
          });

        // Perform bulk deletion using the IDs
        if (toDelete.length > 0) {
          await strapi.db
            .query("api::import-file-log.import-file-log")
            .deleteMany({
              where: { id: { $in: toDelete.map(({ id }) => id) } },
            });
        }
      } catch (error) {
        console.error(
          `Error deleting logs for importFileState ID: ${importFileStateId}`,
          error
        );
      }
    }
  },
};
