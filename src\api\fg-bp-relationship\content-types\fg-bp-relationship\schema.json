{"kind": "collectionType", "collectionName": "fg_bp_relationships", "info": {"singularName": "fg-bp-relationship", "pluralName": "fg-bp-relationships", "displayName": "Flexible Group Business Partner Relationship"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"locationbusinesspartner": {"type": "string"}, "relationshipbelongsto": {"type": "string"}, "membershipparentbp": {"type": "string"}, "validityfrom": {"type": "date"}, "validityto": {"type": "date"}, "locationbusinesspartnername": {"type": "string"}, "membershipparentbpname": {"type": "string"}}}