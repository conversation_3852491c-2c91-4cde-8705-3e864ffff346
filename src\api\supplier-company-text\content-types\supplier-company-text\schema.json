{"kind": "collectionType", "collectionName": "supplier_company_texts", "info": {"singularName": "supplier-company-text", "pluralName": "supplier-company-texts", "displayName": "Supplier Company Text"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"long_text_id": {"type": "string", "required": true}, "long_text": {"type": "text"}, "language": {"type": "string", "required": true}, "company_code": {"type": "string", "required": true}, "company": {"type": "relation", "relation": "manyToOne", "target": "api::supplier-company.supplier-company", "inversedBy": "company_texts"}, "supplier_id": {"type": "string", "required": true}, "supplier": {"type": "relation", "relation": "manyToOne", "target": "api::supplier.supplier", "inversedBy": "company_texts"}}}