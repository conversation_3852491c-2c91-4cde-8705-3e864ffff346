{"kind": "collectionType", "collectionName": "fg_control_mains", "info": {"singularName": "fg-control-main", "pluralName": "fg-control-mains", "displayName": "Flexible Group Control Main"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"flex_group_id": {"type": "string"}, "description": {"type": "text"}, "flex_group_type": {"type": "string"}, "icon_image": {"type": "text"}, "icon_text": {"type": "text"}, "indicator_type": {"type": "string"}, "valid_from": {"type": "date"}, "valid_to": {"type": "date"}, "operand": {"type": "enumeration", "enum": ["ADD", "UPDATE", "DELETE"], "default": "ADD"}}}