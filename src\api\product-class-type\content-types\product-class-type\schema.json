{"kind": "collectionType", "collectionName": "product_class_types", "info": {"singularName": "product-class-type", "pluralName": "product-class-types", "displayName": "Product Class Type"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"class_internal_id": {"type": "string", "required": true, "unique": true, "column": {"unique": true}}, "class_type": {"type": "string"}, "class_type_name": {"type": "string"}, "class_status": {"type": "string"}, "class_status_name": {"type": "string"}, "class": {"type": "string"}, "charc_value_types": {"type": "relation", "relation": "oneToMany", "target": "api::product-charc-value-type.product-charc-value-type", "mappedBy": "class_internal"}, "descriptions": {"type": "relation", "relation": "oneToMany", "target": "api::product-class-type-description.product-class-type-description", "mappedBy": "class_internal"}}}