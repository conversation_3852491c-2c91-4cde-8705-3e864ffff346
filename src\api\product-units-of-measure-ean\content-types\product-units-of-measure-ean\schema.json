{"kind": "collectionType", "collectionName": "product_units_of_measure_eans", "info": {"singularName": "product-units-of-measure-ean", "pluralName": "product-units-of-measure-eans", "displayName": "Product Units Of Measure EAN"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"alternative_unit": {"type": "string"}, "consecutive_number": {"type": "integer"}, "product_standard_id": {"type": "string"}, "international_article_number_cat": {"type": "string"}, "is_main_global_trade_item_number": {"type": "boolean"}, "product_id": {"type": "string"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "units_of_measure_ean"}, "units_of_measure": {"type": "relation", "relation": "oneToOne", "target": "api::product-units-of-measure.product-units-of-measure", "inversedBy": "units_of_measure_ean"}}}