{"kind": "collectionType", "collectionName": "crm_competitor_products", "info": {"singularName": "crm-competitor-product", "pluralName": "crm-competitor-products", "displayName": "CRM Competitor Product"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"competitor_product_id": {"type": "string"}, "competitor_product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "crm_competitor_products"}, "competitor_product_uuid": {"type": "string"}, "competitor_id": {"type": "string"}, "competitor": {"type": "relation", "relation": "manyToOne", "target": "api::crm-competitor.crm-competitor", "inversedBy": "crm_competitor_products"}, "competitor_product_name": {"type": "string"}, "best_seller_indicator": {"type": "boolean"}, "list_price": {"type": "decimal"}, "currency": {"type": "string"}, "own_product_id": {"type": "string"}, "business_partner": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "crm_competitor_products"}, "product_comparison": {"type": "string"}, "own_product_category_id": {"type": "string"}, "base_uom": {"type": "string"}, "status_code": {"type": "string"}}}