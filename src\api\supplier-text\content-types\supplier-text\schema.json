{"kind": "collectionType", "collectionName": "supplier_texts", "info": {"singularName": "supplier-text", "pluralName": "supplier-texts", "displayName": "Supplier Text"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"long_text_id": {"type": "string", "required": true}, "long_text": {"type": "text"}, "language": {"type": "string", "required": true}, "supplier_id": {"type": "string", "required": true}, "supplier": {"type": "relation", "relation": "manyToOne", "target": "api::supplier.supplier", "inversedBy": "texts"}}}