{"kind": "collectionType", "collectionName": "product_plant_storages", "info": {"singularName": "product-plant-storage", "pluralName": "product-plant-storages", "displayName": "Product Plant Storage"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"inventory_for_cycle_count_ind": {"type": "boolean", "default": false}, "cycle_counting_indicator_is_fixed": {"type": "boolean", "default": false}, "prod_maximum_storage_period_unit": {"type": "string"}, "wrhs_mgmt_ptwy_and_stk_removal_strgy": {"type": "string"}, "provisioning_service_level": {"type": "string"}, "plant_id": {"type": "string"}, "plant": {"type": "relation", "relation": "oneToOne", "target": "api::product-plant.product-plant", "inversedBy": "plant_storage"}, "product_id": {"type": "string"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "plant_storage"}}}