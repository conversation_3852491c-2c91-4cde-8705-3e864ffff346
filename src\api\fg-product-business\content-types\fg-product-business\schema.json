{"kind": "collectionType", "collectionName": "fg_product_businesses", "info": {"singularName": "fg-product-business", "pluralName": "fg-product-businesses", "displayName": "Flexible Group Product Business"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"flex_group_id": {"type": "string"}, "is_all_product": {"type": "boolean", "default": false}, "product_id": {"type": "string"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "product_businesses"}, "vendor_id": {"type": "string"}, "vendor": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "product_businesses"}, "product_hierarchy": {"type": "string"}, "operand": {"type": "enumeration", "enum": ["ADD", "UPDATE", "DELETE"], "default": "ADD"}}}