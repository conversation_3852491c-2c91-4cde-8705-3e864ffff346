/**
 * crm-opportunity-preceding-and-follow-up-document controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";
import { generateUniqueID } from "../../../utils/cpi";

export default factories.createCoreController(
  "api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document",
  ({ strapi }) => ({
    async opportunityRegistration(ctx: Context) {
      try {
        const {
          name,
          prospect_party_id,
          primary_contact_party_id,
          origin_type_code,
          expected_revenue_amount,
          expected_revenue_start_date,
          expected_revenue_end_date,
          life_cycle_status_code,
          probability_percent,
          group_code,
          main_employee_responsible_party_id,
          note,
          type_code,
          business_transaction_document_relationship_role_code,
          activity_id,
        } = ctx.request.body;

        if (
          !name ||
          !prospect_party_id ||
          !main_employee_responsible_party_id
        ) {
          return ctx.throw(
            400,
            "Missing required fields: name, prospect_party_id, main_employee_responsible_party_id"
          );
        }

        const locale = "en";

        const newOpportunity = await strapi.db.transaction(async () => {
          const opportunity_id = `${await generateUniqueID("crm_opportunity_id_seq")}`;
          let data: any = {
            opportunity_id,
            name,
            prospect_party_id,
            primary_contact_party_id,
            origin_type_code,
            expected_revenue_amount,
            expected_revenue_start_date,
            expected_revenue_end_date,
            life_cycle_status_code,
            probability_percent,
            group_code,
            main_employee_responsible_party_id,
            locale,
          };
          const opportunity = await strapi
            .query("api::crm-opportunity.crm-opportunity")
            .create({
              data,
            });

          data = {
            opportunity_id: opportunity.opportunity_id,
            note,
            is_global_note: true,
            locale,
          };

          await strapi.query("api::crm-note.crm-note").create({
            data,
          });

          data = {
            type_code,
            business_transaction_document_relationship_role_code,
            activity_id,
            opportunity_id: opportunity.opportunity_id,
            locale,
          };

          await strapi
            .query(
              "api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document"
            )
            .create({
              data,
            });

          return opportunity;
        });

        return ctx.send({
          message: "Opportunity follow up registered successfully",
          data: newOpportunity,
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to register Opportunity follow up: ${error.message}`
        );
      }
    },
    async activityRegistration(ctx: Context) {
      try {
        const {
          document_type,
          subject,
          phone_call_category,
          disposition_code,
          start_date,
          end_date,
          initiator_code,
          owner_party_id,
          main_account_party_id,
          main_contact_party_id,
          note,
          involved_parties,
          type_code,
          business_transaction_document_relationship_role_code,
          opportunity_id,
        } = ctx.request.body;

        if (
          !document_type ||
          !subject ||
          !main_account_party_id ||
          !main_contact_party_id ||
          !opportunity_id
        ) {
          return ctx.throw(
            400,
            "Missing required fields: document_type, subject, main_account_party_id, main_contact_party_id, opportunity_id"
          );
        }

        const locale = "en";

        const newOpportunity = await strapi.db.transaction(async () => {
          let data: any = {
            document_type,
            subject,
            phone_call_category,
            disposition_code,
            start_date,
            end_date,
            initiator_code,
            owner_party_id,
            main_account_party_id,
            main_contact_party_id,
            locale,
          };
          const activity = await strapi
            .query("api::crm-activity.crm-activity")
            .create({
              data,
            });

          data = {
            activity_id: activity.activity_id,
            note,
            is_global_note: true,
            locale,
          };

          await strapi.query("api::crm-note.crm-note").create({
            data,
          });

          if (Array.isArray(involved_parties)) {
            // ✅ Insert related involved_parties
            for (const involved_party of involved_parties) {
              data = {
                party_uuid: involved_party.party_uuid,
                party_type_code: involved_party.party_type_code,
                role_category_code: involved_party.role_category_code,
                role_code: involved_party.role_code,
                party_id: involved_party.party_id,
                activity_id: activity.activity_id,
                locale,
              };

              await strapi
                .query("api::crm-involved-party.crm-involved-party")
                .create({
                  data,
                });
            }
          }

          data = {
            type_code,
            business_transaction_document_relationship_role_code,
            activity_id: activity.activity_id,
            opportunity_id,
            locale,
          };

          await strapi
            .query(
              "api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document"
            )
            .create({
              data,
            });

          return activity;
        });

        return ctx.send({
          message: "Opportunity follow up registered successfully",
          data: newOpportunity,
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to register Opportunity follow up: ${error.message}`
        );
      }
    },
  })
);
