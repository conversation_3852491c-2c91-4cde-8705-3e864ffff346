export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi
            .query("api::product-sales-text.product-sales-text")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.product_id && params.data.product_sales_org) {
      try {
        const salesOrg = await strapi
          .query("api::product-sales-delivery.product-sales-delivery")
          .findOne({
            where: {
              product_id: params.data.product_id,
              product_sales_org: params.data.product_sales_org,
            },
          });

        if (salesOrg) {
          await strapi
            .query("api::product-sales-text.product-sales-text")
            .update({
              where: { id: result.id },
              data: {
                sales_delivery: {
                  connect: [salesOrg.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi
            .query("api::product-sales-text.product-sales-text")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.product_id && result?.product?.count === 1) {
          await strapi
            .query("api::product-sales-text.product-sales-text")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.product_id && params.data.product_sales_org) {
      try {
        const salesOrg = await strapi
          .query("api::product-sales-delivery.product-sales-delivery")
          .findOne({
            where: {
              product_id: params.data.product_id,
              product_sales_org: params.data.product_sales_org,
            },
          });

        if (salesOrg) {
          await strapi
            .query("api::product-sales-text.product-sales-text")
            .update({
              where: { id: result.id },
              data: {
                sales_delivery: {
                  connect: [salesOrg.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.product_id &&
          !result.product_sales_org &&
          result?.sales_delivery?.count === 1
        ) {
          await strapi
            .query("api::product-sales-text.product-sales-text")
            .update({
              where: { id: result.id },
              data: {
                sales_delivery: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
