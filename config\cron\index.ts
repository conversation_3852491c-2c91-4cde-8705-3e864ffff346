export default {
  /**
   * Delete Expired Guest User Carts.
   * Runs every day at midnight
   */

  deleteExpiredCarts: {
    task: async ({ strapi }) => {
      const expiredCarts = await strapi
        .query("api::guest-user-cart.guest-user-cart")
        .findMany({
          where: {
            expire_at: { $lt: new Date() },
          },
        });

      // Delete expired carts and their items
      await Promise.all(
        expiredCarts.map(async (cart) => {
          await strapi
            .documents("api::guest-user-cart.guest-user-cart")
            .delete({
              documentId: cart.documentId,
            });
        })
      );
    },
    options: {
      rule: "0 0 * * *",
      tz: "America/Chicago",
    },
  },

  /**
   * Delete Expired Staging Data.
   * Runs every day at midnight
   */
  deleteExpiredStagingData: {
    task: async ({ strapi }) => {
      const cutoffDate = new Date();
      cutoffDate.setMonth(cutoffDate.getMonth() - 1);

      const bpDeletedCount = await strapi.db
        .connection("business_partner_stagings")
        .where("created_at", "<", cutoffDate)
        .del();

      strapi.log.info(
        `[CRON] Deleted ${bpDeletedCount} expired business partner stagings records older than 1 month.`
      );

      const bpContactDeletedCount = await strapi.db
        .connection("bp_contact_stagings")
        .where("created_at", "<", cutoffDate)
        .del();

      strapi.log.info(
        `[CRON] Deleted ${bpContactDeletedCount} expired business partner contact stagings records older than 1 month.`
      );

      const productDeletedCount = await strapi.db
        .connection("product_stagings")
        .where("created_at", "<", cutoffDate)
        .del();

      strapi.log.info(
        `[CRON] Deleted ${productDeletedCount} expired product stagings records older than 1 month.`
      );

      const productHierarchyDeletedCount = await strapi.db
        .connection("product_hierarchy_stagings")
        .where("created_at", "<", cutoffDate)
        .del();

      strapi.log.info(
        `[CRON] Deleted ${productHierarchyDeletedCount} expired product hierarchy stagings records older than 1 month.`
      );
    },
    options: {
      rule: "0 0 * * *", // Every day at midnight
      tz: "America/Chicago",
    },
  },
};
