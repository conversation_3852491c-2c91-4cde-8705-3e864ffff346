{"kind": "collectionType", "collectionName": "product_medias", "info": {"singularName": "product-media", "pluralName": "product-medias", "displayName": "Product Media", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"product_id": {"type": "string"}, "media_name": {"type": "string"}, "dimension": {"type": "string"}, "media_type": {"type": "enumeration", "enum": ["PDF", "IMAGE", "VIDEO", "SPECIFICATION"]}, "url": {"type": "string"}, "is_cover_image": {"type": "boolean"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "medias"}, "file_path": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "code": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}}}