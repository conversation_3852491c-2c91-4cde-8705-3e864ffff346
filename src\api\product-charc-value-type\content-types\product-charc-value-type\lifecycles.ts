export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi
            .query("api::product-charc-value-type.product-charc-value-type")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.charc_internal_id) {
      try {
        const charc_internal = await strapi
          .query("api::product-class-charc-type.product-class-charc-type")
          .findOne({
            where: { charc_internal_id: params.data.charc_internal_id },
          });

        if (charc_internal) {
          await strapi
            .query("api::product-charc-value-type.product-charc-value-type")
            .update({
              where: { id: result.id },
              data: {
                charc_internal: {
                  connect: [charc_internal.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.class_internal_id) {
      try {
        const class_internal = await strapi
          .query("api::product-class-type.product-class-type")
          .findOne({
            where: { class_internal_id: params.data.class_internal_id },
          });

        if (class_internal) {
          await strapi
            .query("api::product-charc-value-type.product-charc-value-type")
            .update({
              where: { id: result.id },
              data: {
                class_internal: {
                  connect: [class_internal.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi
            .query("api::product-charc-value-type.product-charc-value-type")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.product_id && result?.product?.count === 1) {
          await strapi
            .query("api::product-charc-value-type.product-charc-value-type")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.charc_internal_id) {
      try {
        const charc_internal = await strapi
          .query("api::product-class-charc-type.product-class-charc-type")
          .findOne({
            where: { charc_internal_id: params.data.charc_internal_id },
          });

        if (charc_internal) {
          await strapi
            .query("api::product-charc-value-type.product-charc-value-type")
            .update({
              where: { id: result.id },
              data: {
                charc_internal: {
                  connect: [charc_internal.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.charc_internal_id && result?.charc_internal?.count === 1) {
          await strapi
            .query("api::product-charc-value-type.product-charc-value-type")
            .update({
              where: { id: result.id },
              data: {
                charc_internal: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.class_internal_id) {
      try {
        const class_internal = await strapi
          .query("api::product-class-type.product-class-type")
          .findOne({
            where: { class_internal_id: params.data.class_internal_id },
          });

        if (class_internal) {
          await strapi
            .query("api::product-charc-value-type.product-charc-value-type")
            .update({
              where: { id: result.id },
              data: {
                class_internal: {
                  connect: [class_internal.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.class_internal_id && result?.class_internal?.count === 1) {
          await strapi
            .query("api::product-charc-value-type.product-charc-value-type")
            .update({
              where: { id: result.id },
              data: {
                class_internal: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
