{"kind": "collectionType", "collectionName": "crm_opportunity_preceding_and_follow_up_documents", "info": {"singularName": "crm-opportunity-preceding-and-follow-up-document", "pluralName": "crm-opportunity-preceding-and-follow-up-documents", "displayName": "CRM Opportunity Preceding And Follow-Up Document"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"object_id": {"type": "string"}, "parent_object_id": {"type": "string"}, "opportunity_id": {"type": "string"}, "opportunity": {"type": "relation", "relation": "manyToOne", "target": "api::crm-opportunity.crm-opportunity", "inversedBy": "opportunity_followups"}, "opportunity_transaction_id": {"type": "string"}, "opportunity_transaction": {"type": "relation", "relation": "manyToOne", "target": "api::crm-opportunity.crm-opportunity", "inversedBy": "opportunity_followup_transactions"}, "activity_id": {"type": "string"}, "activity": {"type": "relation", "relation": "manyToOne", "target": "api::crm-activity.crm-activity", "inversedBy": "opportunity_followups"}, "type_code": {"type": "string"}, "item_id": {"type": "string"}, "item_type_code": {"type": "string"}, "sales_cycle_code": {"type": "string"}, "sales_cycle_phase_code": {"type": "string"}, "sales_cycle_phase_step_code": {"type": "string"}, "main_indicator": {"type": "boolean"}, "business_transaction_document_relationship_role_code": {"type": "string"}}}