{"kind": "collectionType", "collectionName": "cust_addr_depdnt_informations", "info": {"singularName": "cust-addr-depdnt-information", "pluralName": "cust-addr-depdnt-informations", "displayName": "Customer Address Dependent Information"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"address_id": {"type": "string", "required": true, "unique": true, "column": {"unique": true}}, "city_code": {"type": "string"}, "county": {"type": "string"}, "express_train_station_name": {"type": "string"}, "train_station_name": {"type": "string"}, "customer_id": {"type": "string"}, "customer": {"type": "relation", "relation": "manyToOne", "target": "api::customer.customer", "inversedBy": "cust_addr_depdnt_informations"}}}