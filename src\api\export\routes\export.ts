module.exports = {
  routes: [
    {
      method: "GET",
      path: "/export/fg-control-mains",
      handler: "export.fgControlMains",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/fg-customer-businesses",
      handler: "export.fgCustomerBusinesses",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/fg-customer-internal",
      handler: "export.fgCustomerInternals",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/fg-product-businesses",
      handler: "export.fgProductBusinesses",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/fg-product-internal",
      handler: "export.fgProductInternals",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/fg-relationships",
      handler: "export.fgRelationships",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/accounts",
      handler: "export.accounts",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/prospects",
      handler: "export.prospects",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/contacts",
      handler: "export.contacts",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/phone-call-activity",
      handler: "export.phoneCallActivity",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/task-activity",
      handler: "export.taskActivity",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/opportunity",
      handler: "export.opportunity",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/users",
      handler: "export.users",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/import-file-logs/:fileStateDocId",
      handler: "export.importFileLogs",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/product-web-attribute",
      handler: "export.productWebAttribute",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/product-specifications",
      handler: "export.productSpecifications",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/export/fg-control-mains-text",
      handler: "export.fgControlMainText",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "POST",
      path: "/export/cart",
      handler: "export.cart",
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
