{"kind": "collectionType", "collectionName": "content_vendors", "info": {"singularName": "content-vendor", "pluralName": "content-vendors", "displayName": "Content Vendor", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"content_name": {"type": "string", "required": true}, "slug": {"type": "string", "unique": true, "required": true}, "i18n": {"type": "json", "pluginOptions": {"i18n": {"localized": true}}}, "body": {"type": "dynamiczone", "components": ["vendor.logo", "vendor.media", "vendor.resource-section"]}}}