{"kind": "collectionType", "collectionName": "configurations", "info": {"singularName": "configuration", "pluralName": "configurations", "displayName": "Configuration"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"code": {"type": "string"}, "description": {"type": "string"}, "is_active": {"type": "boolean", "default": false}, "type": {"type": "enumeration", "enum": ["CUSTOMER_TEXT_DESC", "INVOICE_FORM_TYPE", "INVOICE_STATUS", "INVOICE_TYPE", "NOTIFICATION", "ORDER_STATUS", "ORDER_TYPE", "QUOTE_STATUS", "RETURN_REASON", "RETURN_REFUND_PROGRESS", "RETURN_STATUS", "TICKET_STATUS", "PAYMENT_TERMS", "INCOTERMS", "PAYMENT_METHOD", "FUNCTION_CP", "VIP", "CP_AUTHORITY", "CP_DEPARTMENTS", "PARTNER_FUNCTIONS", "CRM_ACTIVITY_STATUS", "CRM_ACTIVITY_PRIORITY", "CRM_ACTIVITY_INITIATOR_CODE", "CRM_ACTIVITY_DOCUMENT_TYPE", "CRM_ACTIVITY_DOC_TYPE_PHONE_CALL", "CRM_ACTIVITY_DOC_TYPE_FOLLOWUP_RELATED_ITEM", "CRM_ACTIVITY_PHONE_CALL_CATEGORY", "CRM_ACTIVITY_TASK_CATEGORY", "CRM_ACTIVITY_APPOINTMENT_CATEGORY", "CRM_ACTIVITY_DISPOSITION_CODE", "CRM_OPPORTUNITY_STATUS", "CRM_OPPORTUNITY_ORIGIN_TYPE", "CRM_OPPORTUNITY_GROUP", "CRM_PURCHASING_CTRL", "CRM_NATIVE_LANG", "BPMA_STR_CHAIN_SCALE", "BPMA_SIZE_UNIT", "PRFRD_COMM_MEDIUM_TYPE", "FLEX_GROUP_TYPE"]}, "usage": {"type": "enumeration", "enum": ["CUSTOMER", "VENDOR", "CONTACT_PERSON", "EMPLOYEE", "CRM"]}}}