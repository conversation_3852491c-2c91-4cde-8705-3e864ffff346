import { SyncProduct } from "../../sync";

async function queueSafeSync() {
  // Delay execution slightly to avoid DB overload
  await new Promise((resolve) => setTimeout(resolve, 250));
  const inProcess = await strapi.db
    .connection("product_stagings")
    .where({ staging_status: "IN_PROCESS" })
    .first();

  if (!inProcess) {
    SyncProduct();
  }
}

export default {
  async afterCreate(event) {
    setImmediate(() => {
      queueSafeSync();
    });
  },
  async afterUpdate(event) {
    setImmediate(() => {
      queueSafeSync();
    });
  },
};
