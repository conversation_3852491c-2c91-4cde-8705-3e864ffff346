{"kind": "collectionType", "collectionName": "user_vendors", "info": {"singularName": "user-vendor", "pluralName": "user-vendors", "displayName": "User <PERSON>"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"address": {"type": "string"}, "country": {"type": "string"}, "city": {"type": "string"}, "zipcode": {"type": "string"}, "invoice_ref": {"type": "string"}, "purchase_order": {"type": "string"}, "department": {"type": "string"}, "phone": {"type": "string"}, "website": {"type": "text"}, "security_que_1": {"type": "relation", "relation": "manyToOne", "target": "api::security-question.security-question"}, "security_que_1_ans": {"type": "string"}, "security_que_2": {"type": "relation", "relation": "manyToOne", "target": "api::security-question.security-question"}, "security_que_2_ans": {"type": "string"}, "vendor_id": {"type": "string"}, "vendor": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "user_vendors"}, "customer": {"type": "relation", "relation": "manyToOne", "target": "api::customer.customer", "inversedBy": "user_vendors"}, "supplier": {"type": "relation", "relation": "manyToOne", "target": "api::supplier.supplier", "inversedBy": "user_vendors"}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "mappedBy": "vendor"}, "admin_user": {"type": "relation", "relation": "oneToOne", "target": "admin::user"}}}