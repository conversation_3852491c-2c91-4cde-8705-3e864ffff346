import { getCustomerId, getSetting } from "../helper";

module.exports = {
  async Connect(ctx) {
    // Read session ID from cookies
    let sessionId = ctx.cookies.get("gucsid");
    if (!sessionId) {
      // Generate session ID if not present
      sessionId = Date.now().toString();
      // Set gucsid in cookie with 7 days expiry
      ctx.cookies.set("gucsid", sessionId, {
        maxAge: 7 * 24 * 60 * 60 * 1000,
      });
    }

    // If cart doesn't exist, create a new one
    const expireAt = new Date();
    // Set expireAt for 10 days
    expireAt.setDate(expireAt.getDate() + 10);

    const populateCust = {
      customer: {
        populate: {
          partner_functions: {
            filters: { partner_function: "SP" },
          },
        },
      },
    };

    // Check if the cart for this session already exists
    let cart = await strapi
      .documents("api::guest-user-cart.guest-user-cart")
      .findFirst({
        filters: {
          session_id: { $eq: sessionId },
        },
        populate: { ...populateCust, cart_items: true },
      });

    // If cart and customer both exist but in settings customer updated
    if (cart && cart?.customer?.customer_id) {
      const setting = await getSetting();
      if (cart?.customer?.customer_id !== setting?.guest_user_customer) {
        const customerId = await getCustomerId(setting?.guest_user_customer);
        cart = await strapi
          .documents("api::guest-user-cart.guest-user-cart")
          .update({
            documentId: cart.documentId,
            data: {
              customer: customerId,
            },
            populate: { ...populateCust, cart_items: true },
          });
      }
    } else if (cart && !cart?.customer?.customer_id) {
      // If cart exist, but customer not available
      const setting = await getSetting();
      const customerId = await getCustomerId(setting?.guest_user_customer);

      cart = await strapi
        .documents("api::guest-user-cart.guest-user-cart")
        .update({
          documentId: cart.documentId,
          data: {
            customer: customerId,
          },
          populate: { ...populateCust, cart_items: true },
        });
    } else if (!cart) {
      const setting = await getSetting();
      const customerId = await getCustomerId(setting?.guest_user_customer);

      cart = await strapi
        .documents("api::guest-user-cart.guest-user-cart")
        .create({
          data: {
            session_id: sessionId,
            expire_at: expireAt,
            customer: customerId,
          },
          populate: { ...populateCust, cart_items: true },
        });
    }

    // Return the cart to the client
    ctx.send({ cart });
  },
};
