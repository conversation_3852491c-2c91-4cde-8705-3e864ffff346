{"kind": "collectionType", "collectionName": "product_catalogs", "info": {"singularName": "product-catalog", "pluralName": "product-catalogs", "displayName": "Product Catalog"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "required": true}, "catalog_status": {"type": "enumeration", "enum": ["ACTIVE", "INACTIVE"], "default": "INACTIVE"}, "web_store": {"type": "string"}, "language": {"type": "string"}, "products": {"type": "relation", "relation": "manyToMany", "target": "api::product.product", "inversedBy": "catalogs"}, "categories": {"type": "relation", "relation": "manyToMany", "target": "api::product-category.product-category", "inversedBy": "catalogs"}}}