# syntax=docker/dockerfile:1.4
# Use Node.js 20 as base image
FROM node:20-slim

# Create a non-root user and group
RUN groupadd -r chssnjyauser && useradd --no-log-init -r -g chssnjyauser chssnjyauser

# Set working directory and assign ownership to non-root user
WORKDIR /app

# Copy files with proper ownership
COPY dist ./dist
COPY types ./types
COPY public ./public
COPY node_modules ./node_modules
COPY package.json ./package.json
COPY package-lock.json ./package-lock.json
COPY tsconfig.json ./tsconfig.json

# Set permissions
RUN chown chssnjyauser:chssnjyauser /app

# Switch to non-root user
USER chssnjyauser

# Expose Strapi default port
EXPOSE 1337

# Start Strapi
CMD ["npm", "start"]