{"kind": "collectionType", "collectionName": "product_charc_value_types", "info": {"singularName": "product-charc-value-type", "pluralName": "product-charc-value-types", "displayName": "Product Charc Value Type", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"product_id": {"type": "string"}, "class_internal_id": {"type": "string"}, "charc_internal_id": {"type": "string"}, "class_type": {"type": "string"}, "charc_value_position_number": {"type": "string"}, "key_date": {"type": "string"}, "change_number": {"type": "string"}, "charc_value_dependency": {"type": "string"}, "charc_value": {"type": "string"}, "charc_from_numeric_value": {"type": "string"}, "charc_from_numeric_value_unit": {"type": "string"}, "charc_to_numeric_value": {"type": "string"}, "charc_to_numeric_value_unit": {"type": "string"}, "charc_from_decimal_value": {"type": "string"}, "charc_to_decimal_value": {"type": "string"}, "charc_from_amount": {"type": "string"}, "charc_to_amount": {"type": "string"}, "currency": {"type": "string"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "charc_value_types"}, "charc_internal": {"type": "relation", "relation": "manyToOne", "target": "api::product-class-charc-type.product-class-charc-type", "inversedBy": "charc_value_types"}, "class_internal": {"type": "relation", "relation": "manyToOne", "target": "api::product-class-type.product-class-type", "inversedBy": "charc_value_types"}}}