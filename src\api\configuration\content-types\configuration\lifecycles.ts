export default {
  async beforeCreate(event) {
    const { code, type } = event.params.data;

    const existingConfig = await strapi.db
      .query("api::configuration.configuration")
      .findOne({
        where: { code, type },
      });

    if (existingConfig) {
      throw new Error(
        `A configuration with code "${code}" and type "${type}" already exists.`
      );
    }
  },
  async beforeUpdate(event) {
    const { code, type } = event.params.data;

    const existingConfig = await strapi.db
      .query("api::configuration.configuration")
      .findOne({
        where: { code, type },
      });

    if (existingConfig && existingConfig.id !== event.params.where.id) {
      throw new Error(
        `A configuration with code "${code}" and type "${type}" already exists.`
      );
    }
  },
};
