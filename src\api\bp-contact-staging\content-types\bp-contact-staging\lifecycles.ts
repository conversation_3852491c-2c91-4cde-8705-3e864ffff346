import { SyncBusinessPartnerContact } from "../../sync";

const payload = async (data: any) => {
  const { business_partner_contact } = data;

  if (!data?.business_partner_role) {
    data.business_partner_role = {
      valid_from: new Date(),
      valid_to: new Date("9999-12-29T23:59:59.000Z"),
      bp_id: data?.business_partner?.bp_id,
      bp_role: "BUP001",
    };
  }

  // **Find or Create Business Partner Relationship**
  let bp_relationship = await strapi
    .query("api::business-partner-relationship.business-partner-relationship")
    .findOne({
      where: {
        bp_id1: business_partner_contact?.bp_company_id,
        bp_id2: business_partner_contact?.bp_person_id,
      },
    });

  if (!bp_relationship) {
    const payload = {
      relationship_number: `HY${Date.now()}`,
      bp_id1: business_partner_contact?.bp_company_id,
      bp_id2: business_partner_contact?.bp_person_id,
      validity_start_date: new Date(),
      validity_end_date: new Date("9999-12-29T23:59:59.000Z"),
      locale: "en",
    };

    bp_relationship = await strapi
      .query("api::business-partner-relationship.business-partner-relationship")
      .create({ data: payload });
  }

  if (data?.business_partner_contact) {
    data.business_partner_contact.relationship_number =
      bp_relationship.relationship_number;
    data.business_partner_contact.relationship = bp_relationship.id;
    data.business_partner_contact.validity_start_date = new Date();
    data.business_partner_contact.validity_end_date = new Date(
      "9999-12-29T23:59:59.000Z"
    );
  }

  if (data?.bp_contact_to_func_and_depts) {
    data.bp_contact_to_func_and_depts.relationship_number =
      bp_relationship.relationship_number;
    data.bp_contact_to_func_and_depts.relationship = bp_relationship.id;
    data.bp_contact_to_func_and_depts.validity_start_date = new Date();
    data.bp_contact_to_func_and_depts.validity_end_date = new Date(
      "9999-12-29T23:59:59.000Z"
    );
  }

  if (data?.business_partner_address) {
    const addressid = [
      business_partner_contact?.bp_company_id,
      business_partner_contact?.bp_person_id,
    ]
      .filter(Boolean)
      .join("")
      .trim();
    data.business_partner_address.bp_address_id = `HY${addressid}`;
    data.business_partner_address.validity_start_date = new Date();
    data.business_partner_address.validity_end_date = new Date(
      "9999-12-29T23:59:59.000Z"
    );
  }

  return data;
};

export default {
  async beforeCreate(event) {
    const { params } = event;
    try {
      params.data.data = await payload(params.data.data);
    } catch (error) {
      console.error("Error in afterCreate:", error);
    }
  },
  async beforeUpdate(event) {
    const { params } = event;
    try {
      params.data.data = await payload(params.data.data);
    } catch (error) {
      console.error("Error in afterCreate:", error);
    }
  },
  async afterCreate(event) {
    const { result } = event;
    setImmediate(async () => {
      await SyncBusinessPartnerContact(result);
    });
  },
  async afterUpdate(event) {
    const { result } = event;
    setImmediate(async () => {
      await SyncBusinessPartnerContact(result);
    });
  },
};
