export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.customer_id) {
      try {
        const cust = await strapi.query("api::customer.customer").findOne({
          where: { customer_id: params.data.customer_id },
        });

        if (cust) {
          await strapi
            .query("api::customer-company-text.customer-company-text")
            .update({
              where: { id: result.id },
              data: {
                customer: {
                  connect: [cust.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.customer_id && params.data.company_code) {
      try {
        const company = await strapi
          .query("api::customer-company.customer-company")
          .findOne({
            where: {
              customer_id: params.data.customer_id,
              company_code: params.data.company_code,
            },
          });

        if (company) {
          await strapi
            .query("api::customer-company-text.customer-company-text")
            .update({
              where: { id: result.id },
              data: {
                company: {
                  connect: [company.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.customer_id) {
      try {
        const cust = await strapi.query("api::customer.customer").findOne({
          where: { customer_id: params.data.customer_id },
        });

        if (cust) {
          await strapi
            .query("api::customer-company-text.customer-company-text")
            .update({
              where: { id: result.id },
              data: {
                customer: {
                  connect: [cust.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.customer_id && result?.customer?.count === 1) {
          await strapi
            .query("api::customer-company-text.customer-company-text")
            .update({
              where: { id: result.id },
              data: {
                customer: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.customer_id && params.data.company_code) {
      try {
        const company = await strapi
          .query("api::customer-company.customer-company")
          .findOne({
            where: {
              customer_id: params.data.customer_id,
              company_code: params.data.company_code,
            },
          });

        if (company) {
          await strapi
            .query("api::customer-company-text.customer-company-text")
            .update({
              where: { id: result.id },
              data: {
                company: {
                  connect: [company.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.customer_id &&
          !result.company_code &&
          result?.company?.count === 1
        ) {
          await strapi
            .query("api::customer-company-text.customer-company-text")
            .update({
              where: { id: result.id },
              data: {
                company: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
