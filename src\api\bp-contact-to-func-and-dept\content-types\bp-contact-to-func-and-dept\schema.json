{"kind": "collectionType", "collectionName": "bp_contact_to_func_and_depts", "info": {"singularName": "bp-contact-to-func-and-dept", "pluralName": "bp-contact-to-func-and-depts", "displayName": "Business Partner Contact Person Function and Department"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"validity_end_date": {"type": "datetime", "required": true}, "contact_person_department": {"type": "string"}, "contact_person_authority_type": {"type": "string"}, "contact_person_function": {"type": "string"}, "contact_person_function_name": {"type": "string"}, "contact_person_remark_text": {"type": "string"}, "contact_person_vip_type": {"type": "boolean", "default": false}, "email_address": {"type": "string"}, "fax_number": {"type": "string"}, "fax_number_extension": {"type": "string"}, "phone_number": {"type": "string"}, "phone_number_extension": {"type": "string"}, "relationship_category": {"type": "string"}, "contact_person_department_name": {"type": "string"}, "bp_person_id": {"type": "string", "required": true}, "business_partner_person": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "contact_person_func_and_depts"}, "bp_company_id": {"type": "string", "required": true}, "business_partner_company": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "contact_company_func_and_depts"}, "relationship_number": {"type": "string", "required": true}, "relationship": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner-relationship.business-partner-relationship", "inversedBy": "contact_func_and_dept_relationships"}, "contact_person": {"type": "relation", "relation": "oneToOne", "target": "api::business-partner-contact.business-partner-contact", "inversedBy": "person_func_and_dept"}, "contact_company": {"type": "relation", "relation": "oneToOne", "target": "api::business-partner-contact.business-partner-contact", "inversedBy": "company_func_and_dept"}}}