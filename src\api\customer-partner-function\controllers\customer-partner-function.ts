/**
 * customer-partner-function controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";

export default factories.createCoreController(
  "api::customer-partner-function.customer-partner-function",
  ({ strapi }) => ({
    async registration(ctx: Context) {
      try {
        const { customer_id, partner_function, bp_customer_number } =
          ctx.request.body;

        if (!customer_id || !partner_function || !bp_customer_number) {
          return ctx.throw(
            400,
            "Missing required fields customer_id, partner_function and bp_customer_number"
          );
        }

        const locale = "en";

        let data = {
          sales_organization: "1000",
          distribution_channel: "10",
          division: "00",
          partner_function: partner_function,
          bp_customer_number: bp_customer_number,
          customer_id,
          locale,
        };

        const pf = await strapi
          .query("api::customer-partner-function.customer-partner-function")
          .create({ data });

        return ctx.send({
          message: "Customer partner function registered successfully",
          data: pf,
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to register Customer partner function: ${error.message}`
        );
      }
    },
    async save(ctx: Context) {
      try {
        const { documentId } = ctx.params;
        const { partner_function, bp_customer_number } = ctx.request.body;

        if (!documentId) {
          return ctx.throw(
            400,
            "Customer partner function document Id is missing in URL param"
          );
        }

        const data: any = { partner_function, bp_customer_number };
        const pf = await strapi
          .query("api::customer-partner-function.customer-partner-function")
          .update({ where: { documentId }, data });

        return ctx.send({
          message: "Customer partner function save successfully",
          data: pf,
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to save Customer partner function: ${error.message}`
        );
      }
    },
  })
);
