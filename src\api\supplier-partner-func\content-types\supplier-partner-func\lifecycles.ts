export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.supplier_id) {
      try {
        const supplier = await strapi.query("api::supplier.supplier").findOne({
          where: { supplier_id: params.data.supplier_id },
        });

        if (supplier) {
          await strapi
            .query("api::supplier-partner-func.supplier-partner-func")
            .update({
              where: { id: result.id },
              data: {
                supplier: {
                  connect: [supplier.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.supplier_id) {
      try {
        const supplier = await strapi.query("api::supplier.supplier").findOne({
          where: { supplier_id: params.data.supplier_id },
        });

        if (supplier) {
          await strapi
            .query("api::supplier-partner-func.supplier-partner-func")
            .update({
              where: { id: result.id },
              data: {
                supplier: {
                  connect: [supplier.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.supplier_id && result?.supplier?.count === 1) {
          await strapi
            .query("api::supplier-partner-func.supplier-partner-func")
            .update({
              where: { id: result.id },
              data: {
                supplier: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
