export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.bp_id1) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_id1 },
          });

        if (bp) {
          await strapi
            .query(
              "api::business-partner-relationship.business-partner-relationship"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner1: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.bp_id2) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_id2 },
          });

        if (bp) {
          await strapi
            .query(
              "api::business-partner-relationship.business-partner-relationship"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner2: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.bp_id1) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_id1 },
          });

        if (bp) {
          await strapi
            .query(
              "api::business-partner-relationship.business-partner-relationship"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner1: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.bp_id1 && result?.business_partner1?.count === 1) {
          await strapi
            .query(
              "api::business-partner-relationship.business-partner-relationship"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner1: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.bp_id2) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_id2 },
          });

        if (bp) {
          await strapi
            .query(
              "api::business-partner-relationship.business-partner-relationship"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner2: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.bp_id2 && result?.business_partner2?.count === 1) {
          await strapi
            .query(
              "api::business-partner-relationship.business-partner-relationship"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner2: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
