{"kind": "collectionType", "collectionName": "permission_crms", "info": {"singularName": "permission-crm", "pluralName": "permission-crms", "displayName": "Permission CRM"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "code": {"type": "string", "required": true, "unique": true, "column": {"unique": true}}, "user_role": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.role"}, "admin_user_role": {"type": "relation", "relation": "oneToMany", "target": "admin::role"}}}