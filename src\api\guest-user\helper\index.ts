const getSetting = async () => {
  const knex = strapi.db.connection;
  return await knex("settings").select("*").first();
};

const getCustomerId = async (guest_user_customer: string) => {
  if (guest_user_customer) {
    const customer = await strapi
      .documents("api::customer.customer")
      .findFirst({
        filters: {
          customer_id: { $eq: guest_user_customer },
        },
      });
    return customer?.id || null;
  }
  return null;
};

export { getSetting, getCustomerId };
