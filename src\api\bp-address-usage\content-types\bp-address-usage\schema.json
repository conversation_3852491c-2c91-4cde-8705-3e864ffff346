{"kind": "collectionType", "collectionName": "bp_address_usages", "info": {"singularName": "bp-address-usage", "pluralName": "bp-address-usages", "displayName": "Business Partner Address Usage"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"address_usage": {"type": "string", "required": true}, "validity_end_date": {"type": "datetime", "required": true}, "authorization_group": {"type": "string"}, "standard_usage": {"type": "boolean", "default": false}, "validity_start_date": {"type": "datetime"}, "bp_address_id": {"type": "string", "required": true}, "business_partner_address": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner-address.business-partner-address", "inversedBy": "address_usages"}, "bp_id": {"type": "string", "required": true}, "business_partner": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "address_usages"}}}