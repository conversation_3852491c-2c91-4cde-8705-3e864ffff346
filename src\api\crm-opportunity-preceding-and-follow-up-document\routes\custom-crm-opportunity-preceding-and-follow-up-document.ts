/**
 * configuration custom router
 */

export default {
  routes: [
    {
      method: "POST",
      path: "/crm-opportunity-preceding-and-follow-up-document/opportunity-registration",
      handler:
        "crm-opportunity-preceding-and-follow-up-document.opportunityRegistration",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "POST",
      path: "/crm-opportunity-preceding-and-follow-up-document/activity-registration",
      handler:
        "crm-opportunity-preceding-and-follow-up-document.activityRegistration",
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
