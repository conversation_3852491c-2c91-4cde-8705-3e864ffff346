import {
  Product,
  ProductBasicText,
  ProductCharcValueType,
  ProductClassCharcType,
  ProductClassCharcTypeDescription,
  ProductClassType,
  ProductClassTypeDescription,
  ProductDescription,
  ProductPlant,
  ProductPlantProcurement,
  ProductPlantSales,
  ProductPlantStorage,
  ProductPlantText,
  ProductSales,
  ProductSalesDelivery,
  ProductSalesTax,
  ProductSalesText,
  ProductStorage,
  ProductStorageLocation,
  ProductUnitsOfMeasure,
  ProductUnitsOfMeasureEAN,
} from "./helpers";

const SyncProduct = async () => {
  const pending = await strapi
    .query("api::product-staging.product-staging")
    .findOne({ where: { staging_status: "PENDING" } });

  if (!pending) {
    // Skip further processing if no PENDING record exists
    return;
  }

  const { id, documentId, locale, data } = pending;

  try {
    await strapi.db.connection("product_stagings").where({ id }).update({
      staging_status: "IN_PROCESS",
    });

    await strapi.db.transaction(async ({ onCommit }) => {
      // Sync Product Data
      if (data?.product) {
        if (!data.product.locale) {
          data.product.locale = locale;
        }
        await Product(data.product);
      }

      // Sync Product Description Data
      if (Array.isArray(data?.product_description)) {
        for (const desc of data.product_description) {
          if (!desc?.locale) {
            desc.locale = locale;
          }
          await ProductDescription(desc);
        }
      }

      // Sync Product Class Charc Type Data
      if (Array.isArray(data?.product_class_charc_types)) {
        for (const type of data.product_class_charc_types) {
          if (!type?.locale) {
            type.locale = locale;
          }
          await ProductClassCharcType(type);
        }
      }

      // Sync Product Class Charc Type Description Data
      if (Array.isArray(data?.product_class_charc_type_descriptions)) {
        for (const desc of data.product_class_charc_type_descriptions) {
          if (!desc?.locale) {
            desc.locale = locale;
          }
          await ProductClassCharcTypeDescription(desc);
        }
      }

      // Sync Product Class Type Data
      if (Array.isArray(data?.product_class_types)) {
        for (const type of data.product_class_types) {
          if (!type?.locale) {
            type.locale = locale;
          }
          await ProductClassType(type);
        }
      }

      // Sync Product Class Type Description Data
      if (Array.isArray(data?.product_class_type_descriptions)) {
        for (const desc of data.product_class_type_descriptions) {
          if (!desc?.locale) {
            desc.locale = locale;
          }
          await ProductClassTypeDescription(desc);
        }
      }

      // Sync Product Charc Value Type Data
      if (Array.isArray(data?.product_charc_value_types)) {
        for (const type of data.product_charc_value_types) {
          if (!type?.locale) {
            type.locale = locale;
          }
          await ProductCharcValueType(type);
        }
      }

      // Sync Product Storage Data
      if (data?.product_storage) {
        if (!data.product_storage.locale) {
          data.product_storage.locale = locale;
        }
        await ProductStorage(data.product_storage);
      }

      // Sync Product Storage Location Data
      if (Array.isArray(data?.product_storage_locations)) {
        for (const sl of data.product_storage_locations) {
          if (!sl?.locale) {
            sl.locale = locale;
          }
          await ProductStorageLocation(sl);
        }
      }

      // Sync Product Sales Data
      if (data?.product_sale) {
        if (!data.product_sale.locale) {
          data.product_sale.locale = locale;
        }
        await ProductSales(data.product_sale);
      }

      // Sync Product Sales Delivery Data
      if (Array.isArray(data?.product_sales_deliveries)) {
        for (const sd of data.product_sales_deliveries) {
          if (!sd?.locale) {
            sd.locale = locale;
          }
          await ProductSalesDelivery(sd);
        }
      }

      // Sync Product Sales Tax Data
      if (Array.isArray(data?.product_sales_taxes)) {
        for (const st of data.product_sales_taxes) {
          if (!st?.locale) {
            st.locale = locale;
          }
          await ProductSalesTax(st);
        }
      }

      // Sync Product Sales Text Data
      if (Array.isArray(data?.product_sales_texts)) {
        for (const st of data.product_sales_texts) {
          if (!st?.locale) {
            st.locale = locale;
          }
          await ProductSalesText(st);
        }
      }

      // Sync Product Basic Text Data
      if (Array.isArray(data?.product_basic_texts)) {
        for (const bt of data.product_basic_texts) {
          if (!bt?.locale) {
            bt.locale = locale;
          }
          await ProductBasicText(bt);
        }
      }

      // Sync Product Plant Data
      if (Array.isArray(data?.product_plant)) {
        for (const plant of data.product_plant) {
          if (!plant?.locale) {
            plant.locale = locale;
          }
          await ProductPlant(plant);
        }
      }

      // Sync Product Plant Text Data
      if (Array.isArray(data?.product_plant_texts)) {
        for (const pt of data.product_plant_texts) {
          if (!pt?.locale) {
            pt.locale = locale;
          }
          await ProductPlantText(pt);
        }
      }

      // Sync Product Plant Storage Data
      if (Array.isArray(data?.product_plant_storage)) {
        for (const ps of data.product_plant_storage) {
          if (!ps?.locale) {
            ps.locale = locale;
          }
          await ProductPlantStorage(ps);
        }
      }

      // Sync Product Plant Procurement Data
      if (Array.isArray(data?.product_plant_procurement)) {
        for (const pp of data.product_plant_procurement) {
          if (!pp?.locale) {
            pp.locale = locale;
          }
          await ProductPlantProcurement(pp);
        }
      }

      // Sync Product Plant Procurement Data
      if (Array.isArray(data?.product_plant_sales)) {
        for (const ps of data.product_plant_sales) {
          if (!ps?.locale) {
            ps.locale = locale;
          }
          await ProductPlantSales(ps);
        }
      }

      // Sync Product Units Of Measure Data
      if (Array.isArray(data?.product_units_of_measures)) {
        for (const um of data.product_units_of_measures) {
          if (!um?.locale) {
            um.locale = locale;
          }
          await ProductUnitsOfMeasure(um);
        }
      }

      // Sync Product Units Of Measure Data
      if (Array.isArray(data?.product_units_of_measure_ean)) {
        for (const ume of data.product_units_of_measure_ean) {
          if (!ume?.locale) {
            ume.locale = locale;
          }
          await ProductUnitsOfMeasureEAN(ume);
        }
      }

      // Runs only if transaction commits successfully
      onCommit(async () => {
        await strapi.db
          .connection("product_stagings")
          .where({ staging_status: "IN_PROCESS" })
          .del();
      });
    });
  } catch (error) {
    console.error("Transaction failed: ", error);
    await strapi.db
      .connection("product_stagings")
      .insert({
        id,
        document_id: documentId,
        data,
        locale,
        staging_status: "FAILED",
        error_message: error.stack,
        created_at: new Date(),
        updated_at: new Date(),
        published_at: new Date(),
      })
      .onConflict("id") // If id exists, update it
      .merge(["staging_status", "error_message", "updated_at"]); // Update only these fields
  } finally {
    // Throttle recursion slightly to avoid pool saturation
    setTimeout(async () => {
      const inProcess = await strapi.db
        .connection("product_stagings")
        .where({ staging_status: "IN_PROCESS" })
        .first();
      if (!inProcess) {
        SyncProduct();
      }
    }, 250); // 250ms delay to prevent pool overload
  }
};

const ResetProductStagingState = async () => {
  try {
    await strapi.db
      .connection("product_stagings")
      .where({ staging_status: "IN_PROCESS" })
      .update({ staging_status: "PENDING" });

    strapi.log.info("Staging state reset: IN_PROCESS → PENDING");
  } catch (error) {
    strapi.log.error("Failed to reset staging state:", error);
  }
};

export { SyncProduct, ResetProductStagingState };
