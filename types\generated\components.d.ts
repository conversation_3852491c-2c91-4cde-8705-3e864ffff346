import type { Schema, Struct } from '@strapi/strapi';

export interface CrmLogo extends Struct.ComponentSchema {
  collectionName: 'components_crm_logos';
  info: {
    displayName: 'Logo';
  };
  attributes: {
    Logo: Schema.Attribute.Media<'images'>;
  };
}

export interface CrmMedia extends Struct.ComponentSchema {
  collectionName: 'components_crm_media';
  info: {
    displayName: 'Media';
  };
  attributes: {
    Images: Schema.Attribute.Media<'images', true>;
    Timer: Schema.Attribute.String;
  };
}

export interface EcomBanner extends Struct.ComponentSchema {
  collectionName: 'components_ecom_banners';
  info: {
    description: '';
    displayName: 'Banner';
  };
  attributes: {
    Banner_Item: Schema.Attribute.Component<'ecom.banner-item', true>;
    Banner_Timer: Schema.Attribute.String;
  };
}

export interface EcomBannerItem extends Struct.ComponentSchema {
  collectionName: 'components_ecom_banner_items';
  info: {
    displayName: 'Banner_Item';
  };
  attributes: {
    Image: Schema.Attribute.Media<'images'>;
    Link: Schema.Attribute.String;
  };
}

export interface EcomCatalogs extends Struct.ComponentSchema {
  collectionName: 'components_ecom_catalogs';
  info: {
    displayName: 'catalogs';
  };
  attributes: {
    catalogue: Schema.Attribute.Component<'ecom.catalogue', true>;
  };
}

export interface EcomCatalogue extends Struct.ComponentSchema {
  collectionName: 'components_ecom_catalogues';
  info: {
    displayName: 'catalogue';
  };
  attributes: {
    Banner_Image: Schema.Attribute.Media<'images'>;
    Media_To_Download: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    Title: Schema.Attribute.String;
  };
}

export interface EcomContact extends Struct.ComponentSchema {
  collectionName: 'components_ecom_contacts';
  info: {
    displayName: 'Contact';
  };
  attributes: {
    Detail: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    Logo: Schema.Attribute.Media<'images'>;
    Title: Schema.Attribute.String;
  };
}

export interface EcomContactsDetail extends Struct.ComponentSchema {
  collectionName: 'components_ecom_contacts_details';
  info: {
    displayName: 'Contacts_detail';
  };
  attributes: {
    Contact: Schema.Attribute.Component<'ecom.contact', true>;
  };
}

export interface EcomCustomerServiceCategories extends Struct.ComponentSchema {
  collectionName: 'components_ecom_customer_service_categories';
  info: {
    displayName: 'Customer service categories';
  };
  attributes: {
    customer_service_categories: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-service-category.customer-service-category'
    >;
  };
}

export interface EcomDedicatedDelivery extends Struct.ComponentSchema {
  collectionName: 'components_ecom_dedicated_deliveries';
  info: {
    displayName: 'Dedicated_Delivery';
  };
  attributes: {
    Banner: Schema.Attribute.Media<'images'>;
    Description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface EcomFeaturedBrands extends Struct.ComponentSchema {
  collectionName: 'components_ecom_featured_brands';
  info: {
    displayName: 'Featured_Brands';
  };
  attributes: {
    Brand_Logos: Schema.Attribute.Media<'images', true>;
  };
}

export interface EcomFooter extends Struct.ComponentSchema {
  collectionName: 'components_ecom_footers';
  info: {
    description: '';
    displayName: 'Footer';
  };
  attributes: {
    Copyright_Text: Schema.Attribute.String;
    Footer_Section: Schema.Attribute.Component<'ecom.footer-section', true>;
    Newsletter_Title: Schema.Attribute.String;
    Privacy_Policy: Schema.Attribute.Component<'ecom.link-with-title', false>;
    Social_Links: Schema.Attribute.Component<'ecom.social-links', true>;
    Tearms_And_Conditions: Schema.Attribute.Component<
      'ecom.link-with-title',
      false
    >;
  };
}

export interface EcomFooterSection extends Struct.ComponentSchema {
  collectionName: 'components_ecom_footer_sections';
  info: {
    displayName: 'Footer_Section';
  };
  attributes: {
    Link_With_Title: Schema.Attribute.Component<'ecom.link-with-title', true>;
  };
}

export interface EcomHeaderImportantNote extends Struct.ComponentSchema {
  collectionName: 'components_ecom_header_important_notes';
  info: {
    displayName: 'Header_Important_note';
  };
  attributes: {
    Learn_More_Link: Schema.Attribute.String;
    Text: Schema.Attribute.String;
  };
}

export interface EcomHospitality extends Struct.ComponentSchema {
  collectionName: 'components_ecom_hospitalities';
  info: {
    displayName: 'Hospitality';
  };
  attributes: {
    Description: Schema.Attribute.Blocks;
  };
}

export interface EcomImageWithTitleDescLink extends Struct.ComponentSchema {
  collectionName: 'components_ecom_image_with_title_desc_links';
  info: {
    displayName: 'Image_With_Title_Desc_Link';
  };
  attributes: {
    Description: Schema.Attribute.String;
    Image: Schema.Attribute.Media<'images'>;
    Title: Schema.Attribute.String;
  };
}

export interface EcomLinkWithTitle extends Struct.ComponentSchema {
  collectionName: 'components_ecom_link_with_titles';
  info: {
    displayName: 'Link_With_Title';
  };
  attributes: {
    Link: Schema.Attribute.String;
    Title: Schema.Attribute.String;
  };
}

export interface EcomMediaCenter extends Struct.ComponentSchema {
  collectionName: 'components_ecom_media_centers';
  info: {
    displayName: 'Media_Center';
  };
  attributes: {
    Press_Release: Schema.Attribute.Component<'ecom.press-release', true>;
  };
}

export interface EcomMenu extends Struct.ComponentSchema {
  collectionName: 'components_ecom_menus';
  info: {
    displayName: 'Menu';
  };
  attributes: {
    Link: Schema.Attribute.String;
    Menu_Item: Schema.Attribute.Component<'ecom.menu-item', true>;
    Target: Schema.Attribute.Enumeration<['_self', '_blank']>;
    Title: Schema.Attribute.String;
  };
}

export interface EcomMenuItem extends Struct.ComponentSchema {
  collectionName: 'components_ecom_menu_items';
  info: {
    displayName: 'Menu_Item';
  };
  attributes: {
    Link: Schema.Attribute.String;
    Sub_Menu_Item: Schema.Attribute.Component<'ecom.sub-menu-item', true>;
    Target: Schema.Attribute.Enumeration<['_self', '_blank']>;
    Title: Schema.Attribute.String;
  };
}

export interface EcomOffer extends Struct.ComponentSchema {
  collectionName: 'components_ecom_offers';
  info: {
    displayName: 'Offer';
  };
  attributes: {
    Description: Schema.Attribute.Blocks;
    Image: Schema.Attribute.Media<'images'>;
  };
}

export interface EcomPopularCategories extends Struct.ComponentSchema {
  collectionName: 'components_ecom_popular_categories';
  info: {
    displayName: 'Popular Categories';
  };
  attributes: {
    product_categories: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-category.product-category'
    >;
  };
}

export interface EcomPopularQuestions extends Struct.ComponentSchema {
  collectionName: 'components_ecom_popular_questions';
  info: {
    displayName: 'Popular Questions';
  };
  attributes: {
    customer_service_questions: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-service-question.customer-service-question'
    >;
  };
}

export interface EcomPortalFeatures extends Struct.ComponentSchema {
  collectionName: 'components_ecom_portal_features';
  info: {
    displayName: 'Portal_Features';
  };
  attributes: {
    Portal_Feature: Schema.Attribute.Component<'ecom.protal-feature', true>;
  };
}

export interface EcomPressRelease extends Struct.ComponentSchema {
  collectionName: 'components_ecom_press_releases';
  info: {
    displayName: 'Press_Release';
  };
  attributes: {
    Description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    Logo: Schema.Attribute.Media<'images'>;
    Title: Schema.Attribute.String;
  };
}

export interface EcomPrivacyAndSecurity extends Struct.ComponentSchema {
  collectionName: 'components_ecom_privacy_and_securities';
  info: {
    displayName: 'Privacy_And_Security';
  };
  attributes: {
    Description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface EcomProjects extends Struct.ComponentSchema {
  collectionName: 'components_ecom_projects';
  info: {
    displayName: 'Projects';
  };
  attributes: {
    Banner: Schema.Attribute.Media<'images'>;
    Description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface EcomProtalFeature extends Struct.ComponentSchema {
  collectionName: 'components_ecom_protal_features';
  info: {
    description: '';
    displayName: 'Protal_Feature';
  };
  attributes: {
    Description: Schema.Attribute.String;
    Icon: Schema.Attribute.Media<'images'>;
    Link: Schema.Attribute.String;
    Title: Schema.Attribute.String;
  };
}

export interface EcomShopByCategoried extends Struct.ComponentSchema {
  collectionName: 'components_ecom_shop_by_categorieds';
  info: {
    displayName: 'Shop_By_Categoried';
  };
  attributes: {
    product_categories: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-category.product-category'
    >;
  };
}

export interface EcomSocialLinks extends Struct.ComponentSchema {
  collectionName: 'components_ecom_social_links';
  info: {
    displayName: 'Social_Links';
  };
  attributes: {
    Icon: Schema.Attribute.Media<'images'>;
    Link: Schema.Attribute.String;
  };
}

export interface EcomSubMenuItem extends Struct.ComponentSchema {
  collectionName: 'components_ecom_sub_menu_items';
  info: {
    displayName: 'Sub_Menu_Item';
  };
  attributes: {
    Link: Schema.Attribute.String;
    Target: Schema.Attribute.Enumeration<['_self', '_blank']>;
    Title: Schema.Attribute.String;
  };
}

export interface EcomSustainability extends Struct.ComponentSchema {
  collectionName: 'components_ecom_sustainabilities';
  info: {
    description: '';
    displayName: 'Sustainability';
  };
  attributes: {
    Banner: Schema.Attribute.Media<'images'>;
    Description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    Sustainability_Certificate: Schema.Attribute.Component<
      'ecom.sustainability-certificates',
      true
    >;
  };
}

export interface EcomSustainabilityCertificates extends Struct.ComponentSchema {
  collectionName: 'components_ecom_sustainability_certificates';
  info: {
    displayName: 'Sustainability_Certificates';
  };
  attributes: {
    Description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    Title: Schema.Attribute.String;
  };
}

export interface EcomTermsAndConditions extends Struct.ComponentSchema {
  collectionName: 'components_ecom_terms_and_conditions';
  info: {
    displayName: 'Terms_And_Conditions';
  };
  attributes: {
    Description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface EcomVideo extends Struct.ComponentSchema {
  collectionName: 'components_ecom_videos';
  info: {
    displayName: 'Video';
  };
  attributes: {
    Video: Schema.Attribute.Media<'videos'>;
  };
}

export interface EcomWhoWeAre extends Struct.ComponentSchema {
  collectionName: 'components_ecom_who_we_ares';
  info: {
    displayName: 'Who_We_Are';
  };
  attributes: {
    Banner: Schema.Attribute.Media<'images'>;
    Description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface PimLogo extends Struct.ComponentSchema {
  collectionName: 'components_pim_logos';
  info: {
    displayName: 'Logo';
  };
  attributes: {
    Logo: Schema.Attribute.Media<'images'>;
  };
}

export interface PimMedia extends Struct.ComponentSchema {
  collectionName: 'components_pim_media';
  info: {
    displayName: 'Media';
  };
  attributes: {
    Images: Schema.Attribute.Media<'images', true>;
    Timer: Schema.Attribute.String;
  };
}

export interface VendorLogo extends Struct.ComponentSchema {
  collectionName: 'components_vendor_logos';
  info: {
    displayName: 'Logo';
  };
  attributes: {
    logo: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios',
      true
    >;
  };
}

export interface VendorMedia extends Struct.ComponentSchema {
  collectionName: 'components_vendor_media';
  info: {
    description: '';
    displayName: 'Media';
  };
  attributes: {
    Images: Schema.Attribute.Media<'images', true>;
    timer: Schema.Attribute.String;
  };
}

export interface VendorResourceDetail extends Struct.ComponentSchema {
  collectionName: 'components_vendor_resource_details';
  info: {
    description: '';
    displayName: 'Resource Detail';
  };
  attributes: {
    Description: Schema.Attribute.Text;
    File: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    Title: Schema.Attribute.String;
  };
}

export interface VendorResourceSection extends Struct.ComponentSchema {
  collectionName: 'components_vendor_resource_sections';
  info: {
    description: '';
    displayName: 'Resource Section';
  };
  attributes: {
    Resource_detail: Schema.Attribute.Component<'vendor.resource-detail', true>;
    Title: Schema.Attribute.String;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ComponentSchemas {
      'crm.logo': CrmLogo;
      'crm.media': CrmMedia;
      'ecom.banner': EcomBanner;
      'ecom.banner-item': EcomBannerItem;
      'ecom.catalogs': EcomCatalogs;
      'ecom.catalogue': EcomCatalogue;
      'ecom.contact': EcomContact;
      'ecom.contacts-detail': EcomContactsDetail;
      'ecom.customer-service-categories': EcomCustomerServiceCategories;
      'ecom.dedicated-delivery': EcomDedicatedDelivery;
      'ecom.featured-brands': EcomFeaturedBrands;
      'ecom.footer': EcomFooter;
      'ecom.footer-section': EcomFooterSection;
      'ecom.header-important-note': EcomHeaderImportantNote;
      'ecom.hospitality': EcomHospitality;
      'ecom.image-with-title-desc-link': EcomImageWithTitleDescLink;
      'ecom.link-with-title': EcomLinkWithTitle;
      'ecom.media-center': EcomMediaCenter;
      'ecom.menu': EcomMenu;
      'ecom.menu-item': EcomMenuItem;
      'ecom.offer': EcomOffer;
      'ecom.popular-categories': EcomPopularCategories;
      'ecom.popular-questions': EcomPopularQuestions;
      'ecom.portal-features': EcomPortalFeatures;
      'ecom.press-release': EcomPressRelease;
      'ecom.privacy-and-security': EcomPrivacyAndSecurity;
      'ecom.projects': EcomProjects;
      'ecom.protal-feature': EcomProtalFeature;
      'ecom.shop-by-categoried': EcomShopByCategoried;
      'ecom.social-links': EcomSocialLinks;
      'ecom.sub-menu-item': EcomSubMenuItem;
      'ecom.sustainability': EcomSustainability;
      'ecom.sustainability-certificates': EcomSustainabilityCertificates;
      'ecom.terms-and-conditions': EcomTermsAndConditions;
      'ecom.video': EcomVideo;
      'ecom.who-we-are': EcomWhoWeAre;
      'pim.logo': PimLogo;
      'pim.media': PimMedia;
      'vendor.logo': VendorLogo;
      'vendor.media': VendorMedia;
      'vendor.resource-detail': VendorResourceDetail;
      'vendor.resource-section': VendorResourceSection;
    }
  }
}
