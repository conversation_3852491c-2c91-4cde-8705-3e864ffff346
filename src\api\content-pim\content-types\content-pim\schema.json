{"kind": "collectionType", "collectionName": "content_pims", "info": {"singularName": "content-pim", "pluralName": "content-pims", "displayName": "Content PIM", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"content_name": {"type": "string", "unique": true}, "slug": {"type": "string"}, "i18n": {"type": "json"}, "body": {"type": "dynamiczone", "components": ["pim.logo", "pim.media"]}}}