/**
 * fg-customer-internal controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";
import { syncCustomerInternal } from "./helpers";

export default factories.createCoreController(
  "api::fg-customer-internal.fg-customer-internal",
  ({ strapi }) => ({
    async sync(ctx: Context) {
      const result = await syncCustomerInternal();

      if (!result.success) {
        return ctx.badRequest(result.message);
      }

      return ctx.send({ message: result.message });
    },
    async exportCustomerInternals(ctx: Context) {
      const { customerId } = ctx.params;

      if (!customerId) {
        return ctx.throw(400, "Customer Id required");
      }

      const batchSize = 500;
      let isFirstItem = true;

      ctx.set("Content-Type", "application/json");
      ctx.set("Transfer-Encoding", "chunked");
      ctx.status = 200;

      const { PassThrough } = require("stream");
      const stream = new PassThrough();
      ctx.body = stream;

      const processChunks = async (queryName: string, whereCondition: any) => {
        let offset = 0;
        let hasMore = true;

        while (hasMore) {
          const results = await strapi.db.query(queryName).findMany({
            where: whereCondition,
            offset,
            limit: batchSize,
          });

          if (!results.length) {
            hasMore = false;
            break;
          }

          for (const result of results) {
            const flexGroupId = result.flex_group_id;

            const fgCtrlMain = await strapi.db
              .query("api::fg-control-main.fg-control-main")
              .findOne({
                where: { flex_group_id: flexGroupId },
              });

            const row = {
              flexiblegroupid: fgCtrlMain.flex_group_id
                ? fgCtrlMain.flex_group_id.toString().padStart(10, "0")
                : "",
              flexiblegroupname: fgCtrlMain?.description || "",
              flexiblegrouptype: fgCtrlMain?.flex_group_type || "",
            };

            if (!isFirstItem) stream.write(",");
            isFirstItem = false;

            stream.write(JSON.stringify(row));
          }

          offset += batchSize;
        }
      };

      (async () => {
        stream.write('{"flexiblegroups": [');

        await processChunks("api::fg-customer-internal.fg-customer-internal", {
          bp_id: customerId,
        });

        await processChunks("api::fg-customer-business.fg-customer-business", {
          is_all_bp: true,
        });

        stream.write("]}");
        stream.end();
      })().catch((err) => {
        console.error("Streaming error:", err);
        stream.end();
      });
    },
    async restoreCustomerInternalData(ctx: Context) {
      try {
        console.log("Restore FG Customer Internal records...");

        await strapi.db.connection.raw(`
            -- Clear existing backup
            TRUNCATE TABLE fg_customer_internals;
            -- Insert backup data
            INSERT INTO fg_customer_internals SELECT * FROM fg_customer_internal_backups;
          `);

        console.log("DONE Restore FG Customer Internal records...");
        return ctx.send({ message: "Restore done." });
      } catch (error) {
        console.log("Error Restore FG Customer Internal records...", error);
      }
    },
  })
);
