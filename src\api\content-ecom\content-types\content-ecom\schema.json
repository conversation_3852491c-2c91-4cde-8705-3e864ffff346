{"kind": "collectionType", "collectionName": "content_ecoms", "info": {"singularName": "content-ecom", "pluralName": "content-ecoms", "displayName": "Content ECOM", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"content_name": {"type": "string"}, "slug": {"type": "string"}, "i18n": {"type": "json"}, "body": {"type": "dynamiczone", "components": ["ecom.customer-service-categories", "ecom.popular-questions", "vendor.logo", "ecom.menu", "ecom.header-important-note", "ecom.banner", "ecom.popular-categories", "ecom.offer", "ecom.hospitality", "ecom.featured-brands", "ecom.footer", "ecom.shop-by-categoried", "ecom.portal-features", "ecom.image-with-title-desc-link", "ecom.video", "ecom.media-center", "ecom.contacts-detail", "ecom.sustainability", "ecom.who-we-are", "ecom.dedicated-delivery", "ecom.terms-and-conditions", "ecom.privacy-and-security", "ecom.catalogs", "ecom.projects"]}}}