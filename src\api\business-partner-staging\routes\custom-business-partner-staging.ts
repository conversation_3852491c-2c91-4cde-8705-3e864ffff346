/**
 * configuration custom router
 */

export default {
  routes: [
    {
      method: "POST",
      path: "/business-partner-staging/import",
      handler: "business-partner-staging.import",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "POST",
      path: "/business-partner-staging/start-sync",
      handler: "business-partner-staging.startSync",
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
