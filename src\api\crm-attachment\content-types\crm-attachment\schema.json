{"kind": "collectionType", "collectionName": "crm_attachments", "info": {"singularName": "crm-attachment", "pluralName": "crm-attachments", "displayName": "CRM Attachment", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"uuid": {"type": "uid"}, "type_code": {"type": "string"}, "mime_type": {"type": "string"}, "document_link": {"type": "string"}, "name": {"type": "string"}, "category_code": {"type": "string"}, "link_web_uri": {"type": "string"}, "bp_id": {"type": "string"}, "business_partner": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "bp_attachments"}, "activity_id": {"type": "string"}, "activity": {"type": "relation", "relation": "manyToOne", "target": "api::crm-activity.crm-activity", "inversedBy": "attachments"}, "opportunity_id": {"type": "string"}, "opportunity": {"type": "relation", "relation": "manyToOne", "target": "api::crm-opportunity.crm-opportunity", "inversedBy": "attachments"}}}