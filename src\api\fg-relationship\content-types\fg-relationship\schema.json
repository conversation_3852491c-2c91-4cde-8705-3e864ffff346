{"kind": "collectionType", "collectionName": "fg_relationships", "info": {"singularName": "fg-relationship", "pluralName": "fg-relationships", "displayName": "Flexible Group Relationship"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"flex_group_id": {"type": "string"}, "child_id": {"type": "string"}, "relationship_type": {"type": "enumeration", "enum": ["ADD", "SUBTRACT"]}, "is_product_based": {"type": "boolean"}, "is_customer_based": {"type": "boolean"}, "operand": {"type": "enumeration", "enum": ["ADD", "UPDATE", "DELETE"], "default": "ADD"}}}