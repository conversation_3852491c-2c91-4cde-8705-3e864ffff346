const getHierarchy = async (node) => {
  const hierarchy = await strapi.entityService.findMany(
    "api::product-hierarchy.product-hierarchy",
    {
      filters: {
        $or: [{ parent_node: node }, { child_node: node }],
      },
      populate: {
        children: {
          populate: "children",
        },
      },
    }
  );

  return hierarchy;
};

const extractProductIds = (node, ids = []) => {
  ids.push(node.product_id);

  if (node.children && node.children.length > 0) {
    node.children.forEach((child) => {
      extractProductIds(child, ids);
    });
  }

  return ids;
};

const fetchProductHierarchy = async (node) => {
  const rootNode = await getHierarchy(node);

  if (!rootNode || rootNode.length === 0) {
    return [];
  }

  let allProductIds = [];
  rootNode.forEach((node) => {
    allProductIds = allProductIds.concat(extractProductIds(node));
  });

  return allProductIds;
};

export default {
  async afterCreate(event) {
    const { result, params } = event;

    if (params.data.vendor_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.vendor_id },
          });

        if (bp) {
          await strapi
            .query("api::fg-product-business.fg-product-business")
            .update({
              where: { id: result.id },
              data: {
                vendor: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi
            .query("api::fg-product-business.fg-product-business")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.vendor_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.vendor_id },
          });

        if (bp) {
          await strapi
            .query("api::fg-product-business.fg-product-business")
            .update({
              where: { id: result.id },
              data: {
                vendor: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.vendor_id && result?.vendor?.count === 1) {
          await strapi
            .query("api::fg-product-business.fg-product-business")
            .update({
              where: { id: result.id },
              data: {
                vendor: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi
            .query("api::fg-product-business.fg-product-business")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.product_id && result?.product?.count === 1) {
          await strapi
            .query("api::fg-product-business.fg-product-business")
            .update({
              where: { id: result.id },
              data: {
                product: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
  async afterDelete(event) {
    const { result } = event;
    if (result.flex_group_id && result.product_id) {
      try {
        const toDelete = await strapi.db
          .query("api::fg-product-internal.fg-product-internal")
          .findMany({
            where: {
              flex_group_id: result.flex_group_id,
              product_id: result.product_id,
              locale: result.locale || "en",
            },
            select: ["id"],
          });

        // Perform bulk deletion using the IDs
        if (toDelete.length > 0) {
          await strapi.db
            .query("api::fg-product-internal.fg-product-internal")
            .deleteMany({
              where: { id: { $in: toDelete.map(({ id }) => id) } },
            });
        }
      } catch (error) {
        console.error(
          `Error deleting product-internal for flex_group_id: ${result.flex_group_id} and product_id: ${result.product_id}`,
          error
        );
      }
    }

    if (result.flex_group_id && result.product_hierarchy) {
      try {
        const productIds = await fetchProductHierarchy(
          result.product_hierarchy
        );

        const toDelete = await strapi.db
          .query("api::fg-product-internal.fg-product-internal")
          .findMany({
            where: {
              flex_group_id: result.flex_group_id,
              product_id: { $in: productIds },
              locale: result.locale || "en",
            },
            select: ["id"],
          });

        // Perform bulk deletion using the IDs
        if (toDelete.length > 0) {
          await strapi.db
            .query("api::fg-product-internal.fg-product-internal")
            .deleteMany({
              where: { id: { $in: toDelete.map(({ id }) => id) } },
            });
        }
      } catch (error) {
        console.error(
          `Error deleting product-internal for flex_group_id: ${result.flex_group_id} and product_hierarchy: ${result.product_hierarchy}`,
          error
        );
      }
    }
  },
};
