{"kind": "collectionType", "collectionName": "bp_fax_numbers", "info": {"singularName": "bp-fax-number", "pluralName": "bp-fax-numbers", "displayName": "Business Partner Fax Number"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"address_id": {"type": "string", "required": true}, "ordinal_number": {"type": "integer", "required": true}, "person": {"type": "string", "required": true}, "fax_country": {"type": "string"}, "fax_number": {"type": "string"}, "fax_number_extension": {"type": "string"}, "international_fax_number": {"type": "string"}, "is_default_fax_number": {"type": "boolean", "default": false}, "address_communication_remark_text": {"type": "text"}, "business_partner_address": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner-address.business-partner-address", "inversedBy": "fax_numbers"}, "bp_contact_address": {"type": "relation", "relation": "manyToOne", "target": "api::bp-contact-to-address.bp-contact-to-address", "inversedBy": "fax_numbers"}}}