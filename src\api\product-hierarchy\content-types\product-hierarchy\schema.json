{"kind": "collectionType", "collectionName": "product_hierarchies", "info": {"singularName": "product-hierarchy", "pluralName": "product-hierarchies", "displayName": "Product Hierarchy"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"product_id": {"type": "string", "unique": true, "column": {"unique": true}}, "root_node": {"type": "string"}, "parent_node": {"type": "string"}, "child_node": {"type": "string"}, "parent": {"type": "relation", "relation": "manyToOne", "target": "api::product-hierarchy.product-hierarchy", "inversedBy": "children"}, "children": {"type": "relation", "relation": "oneToMany", "target": "api::product-hierarchy.product-hierarchy", "mappedBy": "parent"}}}