{"kind": "collectionType", "collectionName": "cart_items", "info": {"singularName": "cart-item", "pluralName": "cart-items", "displayName": "<PERSON><PERSON>em"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"material": {"type": "string", "required": true}, "requested_quantity": {"type": "integer", "required": true, "default": 1}, "cart": {"type": "relation", "relation": "manyToOne", "target": "api::cart.cart", "inversedBy": "cart_items"}}}