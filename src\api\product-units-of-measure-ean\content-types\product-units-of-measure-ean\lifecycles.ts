export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi
            .query(
              "api::product-units-of-measure-ean.product-units-of-measure-ean"
            )
            .update({
              where: { id: result.id },
              data: {
                product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.product_id && params.data.alternative_unit) {
      try {
        const units_of_measure = await strapi
          .query(
            "api::product-units-of-measure-ean.product-units-of-measure-ean"
          )
          .findOne({
            where: {
              product_id: params.data.product_id,
              alternative_unit: params.data.alternative_unit,
            },
          });

        if (units_of_measure) {
          await strapi
            .query(
              "api::product-units-of-measure-ean.product-units-of-measure-ean"
            )
            .update({
              where: { id: result.id },
              data: {
                units_of_measure: {
                  connect: [units_of_measure.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.product_id },
        });

        if (product) {
          await strapi
            .query(
              "api::product-units-of-measure-ean.product-units-of-measure-ean"
            )
            .update({
              where: { id: result.id },
              data: {
                product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.product_id && result?.product?.count === 1) {
          await strapi
            .query(
              "api::product-units-of-measure-ean.product-units-of-measure-ean"
            )
            .update({
              where: { id: result.id },
              data: {
                product: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error(
          "Error in afterUpdate when disconnecting product:",
          error
        );
      }
    }

    if (params.data.product_id && params.data.alternative_unit) {
      try {
        const units_of_measure = await strapi
          .query(
            "api::product-units-of-measure-ean.product-units-of-measure-ean"
          )
          .findOne({
            where: {
              product_id: params.data.product_id,
              alternative_unit: params.data.alternative_unit,
            },
          });

        if (units_of_measure) {
          await strapi
            .query(
              "api::product-units-of-measure-ean.product-units-of-measure-ean"
            )
            .update({
              where: { id: result.id },
              data: {
                units_of_measure: {
                  connect: [units_of_measure.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.product_id &&
          !result.alternative_unit &&
          result?.units_of_measure?.count === 1
        ) {
          await strapi
            .query(
              "api::product-units-of-measure-ean.product-units-of-measure-ean"
            )
            .update({
              where: { id: result.id },
              data: {
                units_of_measure: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error(
          "Error in afterUpdate when disconnecting units_of_measure:",
          error
        );
      }
    }
  },
};
