{"kind": "collectionType", "collectionName": "crm_competitors", "info": {"singularName": "crm-competitor", "pluralName": "crm-competitors", "displayName": "CRM Competitor"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"object_id": {"type": "string"}, "competitor_id": {"type": "string"}, "competitor_uuid": {"type": "uid"}, "status_code": {"type": "string"}, "classification_code": {"type": "string"}, "name": {"type": "string"}, "additional_name": {"type": "string"}, "country_code": {"type": "string"}, "region_code": {"type": "string"}, "care_of_name": {"type": "string"}, "address_line1": {"type": "string"}, "address_line2": {"type": "string"}, "house_number": {"type": "string"}, "street": {"type": "string"}, "address_line4": {"type": "string"}, "address_line5": {"type": "string"}, "district": {"type": "string"}, "city": {"type": "string"}, "additional_city_name": {"type": "string"}, "street_postal_code": {"type": "string"}, "county": {"type": "string"}, "company_postal_code": {"type": "string"}, "po_box": {"type": "string"}, "po_box_postal_code": {"type": "string"}, "po_box_deviating_country_code": {"type": "string"}, "po_box_deviating_state_code": {"type": "string"}, "po_box_deviating_city": {"type": "string"}, "time_zone_code": {"type": "string"}, "tax_jurisdiction_code": {"type": "string"}, "phone": {"type": "string"}, "fax": {"type": "string"}, "email": {"type": "string"}, "web_site": {"type": "string"}, "language_code": {"type": "string"}, "best_reached_by_code": {"type": "string"}, "business_partner_formatted_name": {"type": "string"}, "formatted_postal_address_description": {"type": "string"}, "normalised_phone": {"type": "string"}, "crm_competitor_products": {"type": "relation", "relation": "oneToMany", "target": "api::crm-competitor-product.crm-competitor-product", "mappedBy": "competitor"}}}