{"kind": "collectionType", "collectionName": "product_sales_taxes", "info": {"singularName": "product-sales-tax", "pluralName": "product-sales-taxes", "displayName": "Product Sales Tax"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"product_id": {"type": "string"}, "country": {"type": "string"}, "tax_category": {"type": "string"}, "tax_classification": {"type": "string"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "sales_taxes"}}}