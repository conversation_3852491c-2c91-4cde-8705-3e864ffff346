{"kind": "collectionType", "collectionName": "crm_opportunity_parties", "info": {"singularName": "crm-opportunity-party", "pluralName": "crm-opportunity-parties", "displayName": "CRM Opportunity Party"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"object_id": {"type": "string"}, "parent_object_id": {"type": "string"}, "party_id": {"type": "string"}, "business_partner": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "opportunity_parties"}, "role_category_code": {"type": "string"}, "role_code": {"type": "string"}, "main_indicator": {"type": "boolean"}, "opportunity_id": {"type": "string"}, "opportunity": {"type": "relation", "relation": "manyToOne", "target": "api::crm-opportunity.crm-opportunity", "inversedBy": "opportunity_parties"}}}