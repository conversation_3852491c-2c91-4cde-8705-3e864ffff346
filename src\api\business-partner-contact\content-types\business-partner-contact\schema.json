{"kind": "collectionType", "collectionName": "business_partner_contacts", "info": {"singularName": "business-partner-contact", "pluralName": "business-partner-contacts", "displayName": "Business Partner Contact"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"validity_end_date": {"type": "datetime", "required": true}, "is_standard_relationship": {"type": "boolean"}, "relationship_category": {"type": "string"}, "validity_start_date": {"type": "datetime"}, "bp_person_id": {"type": "string", "required": true}, "business_partner_person": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "contact_persons"}, "bp_company_id": {"type": "string", "required": true}, "business_partner_company": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "contact_companies"}, "relationship_number": {"type": "string", "required": true}, "relationship": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner-relationship.business-partner-relationship", "inversedBy": "contact_relationships"}, "person_func_and_dept": {"type": "relation", "relation": "oneToOne", "target": "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept", "mappedBy": "contact_person"}, "company_func_and_dept": {"type": "relation", "relation": "oneToOne", "target": "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept", "mappedBy": "contact_company"}, "person_addresses": {"type": "relation", "relation": "oneToMany", "target": "api::bp-contact-to-address.bp-contact-to-address", "mappedBy": "contact_person_address"}, "company_addresses": {"type": "relation", "relation": "oneToMany", "target": "api::bp-contact-to-address.bp-contact-to-address", "mappedBy": "contact_company_address"}}}