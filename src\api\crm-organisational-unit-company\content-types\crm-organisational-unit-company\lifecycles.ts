export default {
  async afterCreate(event) {
    const { result, params } = event;

    if (params.data.organisational_unit_id) {
      try {
        const organisational_unit = await strapi
          .query("api::crm-organisational-unit.crm-organisational-unit")
          .findOne({
            where: {
              organisational_unit_id: params.data.organisational_unit_id,
            },
          });

        if (organisational_unit) {
          await strapi
            .query(
              "api::crm-organisational-unit-company.crm-organisational-unit-company"
            )
            .update({
              where: { id: result.id },
              data: {
                organisational_unit: {
                  connect: [organisational_unit.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.organisational_unit_id) {
      try {
        const organisational_unit = await strapi
          .query("api::crm-organisational-unit.crm-organisational-unit")
          .findOne({
            where: {
              organisational_unit_id: params.data.organisational_unit_id,
            },
          });

        if (organisational_unit) {
          await strapi
            .query(
              "api::crm-organisational-unit-company.crm-organisational-unit-company"
            )
            .update({
              where: { id: result.id },
              data: {
                organisational_unit: {
                  connect: [organisational_unit.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.organisational_unit_id &&
          result?.organisational_unit?.count === 1
        ) {
          await strapi
            .query(
              "api::crm-organisational-unit-company.crm-organisational-unit-company"
            )
            .update({
              where: { id: result.id },
              data: {
                organisational_unit: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
