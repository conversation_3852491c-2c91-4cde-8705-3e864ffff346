{"kind": "collectionType", "collectionName": "crm_organisational_unit_addresses", "info": {"singularName": "crm-organisational-unit-address", "pluralName": "crm-organisational-unit-addresses", "displayName": "CRM Organisational Unit Address"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string"}, "start_date": {"type": "date"}, "end_date": {"type": "date"}, "house_number": {"type": "string"}, "additional_street_suffix_name": {"type": "string"}, "additional_street_prefix_name": {"type": "string"}, "care_of_name": {"type": "string"}, "city_name": {"type": "string"}, "district_name": {"type": "string"}, "country_code": {"type": "string"}, "county_name": {"type": "string"}, "po_box_deviating_city_name": {"type": "string"}, "po_box_deviating_country_code": {"type": "string"}, "po_box_deviating_region_code": {"type": "string"}, "po_box_id": {"type": "string"}, "po_box_indicator": {"type": "boolean", "default": false}, "po_box_postal_code": {"type": "string"}, "region_code": {"type": "string"}, "time_zone_code": {"type": "string"}, "street_postal_code": {"type": "string"}, "street_name": {"type": "string"}, "street_prefix_name": {"type": "string"}, "street_suffix_name": {"type": "string"}, "web_uri": {"type": "string"}, "mobile_formatted_number_description": {"type": "string"}, "facsimile_formatted_number_description": {"type": "string"}, "conventional_phone_formatted_number_description": {"type": "string"}, "email_uri": {"type": "string"}, "email_normalised_uri": {"type": "string"}, "formatted_postal_address_description": {"type": "string"}, "organisational_unit_id": {"type": "string"}, "organisational_unit": {"type": "relation", "relation": "manyToOne", "target": "api::crm-organisational-unit.crm-organisational-unit", "inversedBy": "addresses"}}}