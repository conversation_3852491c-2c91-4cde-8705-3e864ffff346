export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.own_product_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.own_product_id },
          });

        if (bp) {
          await strapi
            .query("api::crm-competitor-product.crm-competitor-product")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.competitor_product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.competitor_product_id },
        });

        if (product) {
          await strapi
            .query("api::crm-competitor-product.crm-competitor-product")
            .update({
              where: { id: result.id },
              data: {
                competitor_product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.competitor_id) {
      try {
        const competitor = await strapi
          .query("api::crm-competitor.crm-competitor")
          .findOne({
            where: { competitor_id: params.data.competitor_id },
          });

        if (competitor) {
          await strapi
            .query("api::crm-competitor-product.crm-competitor-product")
            .update({
              where: { id: result.id },
              data: {
                competitor: {
                  connect: [competitor.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.own_product_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.own_product_id },
          });

        if (bp) {
          await strapi
            .query("api::crm-competitor-product.crm-competitor-product")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.own_product_id && result?.business_partner?.count === 1) {
          await strapi
            .query("api::crm-competitor-product.crm-competitor-product")
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.competitor_product_id) {
      try {
        const product = await strapi.query("api::product.product").findOne({
          where: { product_id: params.data.competitor_product_id },
        });

        if (product) {
          await strapi
            .query("api::crm-competitor-product.crm-competitor-product")
            .update({
              where: { id: result.id },
              data: {
                competitor_product: {
                  connect: [product.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.competitor_product_id &&
          result?.competitor_product?.count === 1
        ) {
          await strapi
            .query("api::crm-competitor-product.crm-competitor-product")
            .update({
              where: { id: result.id },
              data: {
                competitor_product: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error(
          "Error in afterUpdate when disconnecting business partner:",
          error
        );
      }
    }

    if (params.data.competitor_id) {
      try {
        const competitor = await strapi
          .query("api::crm-competitor.crm-competitor")
          .findOne({
            where: { competitor_id: params.data.competitor_id },
          });

        if (competitor) {
          await strapi
            .query("api::crm-competitor-product.crm-competitor-product")
            .update({
              where: { id: result.id },
              data: {
                competitor: {
                  connect: [competitor.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.competitor_id && result?.competitor?.count === 1) {
          await strapi
            .query("api::crm-competitor-product.crm-competitor-product")
            .update({
              where: { id: result.id },
              data: {
                competitor: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
