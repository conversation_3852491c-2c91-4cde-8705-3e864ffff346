const ProductHierarchy = async (data) => {
  const { product_id, locale } = data;

  if (!product_id) {
    throw new Error("product_id is required");
  }

  try {
    // Check if a product hierarchy already exists
    const existingRecord = await strapi.db
      .query("api::product-hierarchy.product-hierarchy")
      .findOne({
        where: { product_id, locale },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db.query("api::product-hierarchy.product-hierarchy").update({
        where: { id: existingRecord.id },
        data,
      });
    } else {
      // Create a new record
      await strapi.db.query("api::product-hierarchy.product-hierarchy").create({
        data,
      });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Product Hierarchy ::: ${error.message}`
    );
  }
};

export { ProductHierarchy };
