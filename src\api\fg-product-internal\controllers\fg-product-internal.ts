/**
 * fg-product-internal controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";
import { syncProductInternal } from "./helpers";

export default factories.createCoreController(
  "api::fg-product-internal.fg-product-internal",
  ({ strapi }) => ({
    async sync(ctx: Context) {
      const result = await syncProductInternal();

      if (!result.success) {
        return ctx.badRequest(result.message);
      }

      return ctx.send({ message: result.message });
    },
    async checkRestrictedProductInternals(ctx: Context) {
      const { customer_id, materials }: any = ctx.request.body;

      if (!customer_id || !materials) {
        return ctx.throw(400, "customer_id and materials are required.");
      }

      if (!Array.isArray(materials)) {
        return ctx.throw(400, "Materials must be array");
      }

      const batchSize = 500;
      let isFirstItem = true;

      ctx.set("Content-Type", "application/json");
      ctx.set("Transfer-Encoding", "chunked");
      ctx.status = 200;

      const { PassThrough } = require("stream");
      const stream = new PassThrough();
      ctx.body = stream;

      const processChunks = async (queryName: string, whereCondition: any) => {
        let offset = 0;
        let hasMore = true;

        while (hasMore) {
          const results = await strapi.db.query(queryName).findMany({
            where: whereCondition,
            offset,
            limit: batchSize,
          });

          if (!results.length) {
            hasMore = false;
            break;
          }

          for (const result of results) {
            const flexGroupId = result.flex_group_id;

            const fgCtrlMain = await strapi.db
              .query("api::fg-control-main.fg-control-main")
              .findOne({
                where: { flex_group_id: flexGroupId },
              });

            const row = {
              Customer: result?.bp_id,
              Material: result?.product_id,
              Restriction: fgCtrlMain?.flex_group_type === "REST" ? "X" : "",
            };

            if (!isFirstItem) stream.write(",");
            isFirstItem = false;

            stream.write(JSON.stringify(row));
          }

          offset += batchSize;
        }
      };

      (async () => {
        stream.write('{"materials": [');

        await processChunks(
          "api::fg-customer-product-internal.fg-customer-product-internal",
          {
            bp_id: customer_id,
            product_id: { $in: materials.map((m: any) => m.Material) },
          }
        );

        stream.write("]}");
        stream.end();
      })().catch((err) => {
        console.error("Streaming error:", err);
        stream.end();
      });
    },
    async restoreProductInternalData(ctx: Context) {
      try {
        console.log("Restore FG Product Internal records...");

        await strapi.db.connection.raw(`
            -- Clear existing backup
            TRUNCATE TABLE fg_product_internals;
            -- Insert backup data
            INSERT INTO fg_product_internals SELECT * FROM fg_product_internal_backups;
          `);

        console.log("DONE Restore FG Product Internal records...");

        return ctx.send({ message: "Restore done." });
      } catch (error) {
        console.log("Error Restore FG Product Internal records...", error);
      }
    },
  })
);
