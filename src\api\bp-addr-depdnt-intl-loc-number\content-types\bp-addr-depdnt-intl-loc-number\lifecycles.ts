export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.bp_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_id },
          });

        if (bp) {
          await strapi
            .query(
              "api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.bp_address_id) {
      try {
        const business_partner_address = await strapi
          .query("api::business-partner-address.business-partner-address")
          .findOne({
            where: { bp_address_id: params.data.bp_address_id },
          });

        if (business_partner_address) {
          await strapi
            .query(
              "api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner_address: {
                  connect: [business_partner_address.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.bp_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.bp_id },
          });

        if (bp) {
          await strapi
            .query(
              "api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.bp_id && result?.business_partner?.count === 1) {
          await strapi
            .query(
              "api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.bp_address_id) {
      try {
        const business_partner_address = await strapi
          .query("api::business-partner-address.business-partner-address")
          .findOne({
            where: { bp_address_id: params.data.bp_address_id },
          });

        if (business_partner_address) {
          await strapi
            .query(
              "api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner_address: {
                  connect: [business_partner_address.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.bp_address_id &&
          result?.business_partner_address?.count === 1
        ) {
          await strapi
            .query(
              "api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number"
            )
            .update({
              where: { id: result.id },
              data: {
                business_partner_address: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
