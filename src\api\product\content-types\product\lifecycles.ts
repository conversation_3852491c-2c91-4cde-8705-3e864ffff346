export default {
  /**
   * After fetching multiple entries
   */
  async afterFindMany(event: any) {
    event.result = await Promise.all(
      event.result.map(async (entry: any) => {
        if (Array.isArray(entry?.medias)) {
          entry.medias = await Promise.all(
            entry?.medias.map(async (media: any) => {
              if (media.url) {
                media.url = media.url;
              }
              return media;
            })
          );
        }
        return entry;
      })
    );
  },

  /**
   * After fetching a single entry
   */
  async afterFindOne(event: any) {
    if (event.result && event.result.url) {
      if (Array.isArray(event.result?.medias)) {
        event.result.medias = await Promise.all(
          event.result?.medias.map(async (media: any) => {
            if (media.url) {
              media.url = media.url;
            }
            return media;
          })
        );
      }
    }
  },
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.parentProduct) {
      try {
        const parent_product = await strapi
          .query("api::product.product")
          .findOne({
            where: { product_id: params.data.parentProduct },
          });

        if (parent_product) {
          await strapi.query("api::product.product").update({
            where: { id: result.id },
            data: {
              parent_product: {
                connect: [parent_product.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.parentProduct) {
      try {
        const parent_product = await strapi
          .query("api::product.product")
          .findOne({
            where: { product_id: params.data.parentProduct },
          });

        if (parent_product) {
          await strapi.query("api::product.product").update({
            where: { id: result.id },
            data: {
              parent_product: {
                connect: [parent_product.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.parentProduct && result?.parent_product?.count === 1) {
          await strapi.query("api::product.product").update({
            where: { id: result.id },
            data: {
              parent_product: {
                set: [],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
