{"kind": "collectionType", "collectionName": "supplier_purchasing_orgs", "info": {"singularName": "supplier-purchasing-org", "pluralName": "supplier-purchasing-orgs", "displayName": "Supplier Purchasing Organization"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"purchasing_organization": {"type": "string", "required": true}, "supplier_id": {"type": "string", "required": true}, "supplier": {"type": "relation", "relation": "manyToOne", "target": "api::supplier.supplier", "inversedBy": "supplier_purchasing_orgs"}, "authorization_group": {"type": "string"}, "automatic_evaluated_rcpt_settlmt": {"type": "boolean"}, "calculation_schema_group_code": {"type": "string"}, "deletion_indicator": {"type": "boolean"}, "evald_receipt_settlement_is_active": {"type": "boolean"}, "incoterms_classification": {"type": "string"}, "incoterms_transfer_location": {"type": "string"}, "incoterms_version": {"type": "string"}, "incoterms_location1": {"type": "string"}, "incoterms_location2": {"type": "string"}, "incoterms_sup_chn_loc1_addl_uuid": {"type": "string"}, "incoterms_sup_chn_loc2_addl_uuid": {"type": "string"}, "incoterms_sup_chn_dvtg_loc_addl_uuid": {"type": "string"}, "intrastat_crs_border_tr_mode": {"type": "string"}, "invoice_is_goods_receipt_based": {"type": "boolean"}, "invoice_is_mm_service_entry_based": {"type": "boolean"}, "is_order_ackn_rqd": {"type": "boolean"}, "material_planned_delivery_durn": {"type": "integer"}, "minimum_order_amount": {"type": "decimal"}, "payment_terms": {"type": "string"}, "planning_cycle": {"type": "string"}, "pricing_date_control": {"type": "string"}, "prod_stock_and_sls_data_transf_prfl": {"type": "string"}, "product_unit_group": {"type": "string"}, "purchase_order_currency": {"type": "string"}, "purchasing_group": {"type": "string"}, "purchasing_is_blocked_for_supplier": {"type": "boolean"}, "pur_ord_auto_generation_is_allowed": {"type": "boolean"}, "rounding_profile": {"type": "string"}, "shipping_condition": {"type": "string"}, "suplr_discount_in_kind_is_granted": {"type": "boolean"}, "suplr_invc_reval_is_allowed": {"type": "boolean"}, "suplr_is_rlvt_for_settlmt_mgmt": {"type": "boolean"}, "suplr_purg_org_is_rlvt_for_price_detn": {"type": "boolean"}, "supplier_abc_classification_code": {"type": "string"}, "supplier_account_group": {"type": "string"}, "supplier_account_number": {"type": "string"}, "supplier_confirmation_control_key": {"type": "string"}, "supplier_phone_number": {"type": "string"}, "supplier_resp_sales_person_name": {"type": "string"}, "supplier_is_returns_supplier": {"type": "string"}, "texts": {"type": "relation", "relation": "oneToMany", "target": "api::supplier-purchasing-org-text.supplier-purchasing-org-text", "mappedBy": "supplier_purchasing_org"}}}