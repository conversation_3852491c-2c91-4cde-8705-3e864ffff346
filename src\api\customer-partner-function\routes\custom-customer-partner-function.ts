/**
 * configuration custom router
 */

export default {
  routes: [
    {
      method: "POST",
      path: "/customer-partner-function/registration",
      handler: "customer-partner-function.registration",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "PUT",
      path: "/customer-partner-function/:documentId/save",
      handler: "customer-partner-function.save",
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
