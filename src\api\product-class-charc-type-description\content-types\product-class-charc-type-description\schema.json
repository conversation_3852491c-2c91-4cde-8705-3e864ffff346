{"kind": "collectionType", "collectionName": "product_class_charc_type_descriptions", "info": {"singularName": "product-class-charc-type-description", "pluralName": "product-class-charc-type-descriptions", "displayName": "Product Class Charc Type Description"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"charc_internal_id": {"type": "string"}, "language": {"type": "string"}, "description": {"type": "string"}, "charc_internal": {"type": "relation", "relation": "manyToOne", "target": "api::product-class-charc-type.product-class-charc-type", "inversedBy": "descriptions"}}}