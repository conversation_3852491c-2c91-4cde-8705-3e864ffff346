import { generateUniqueID } from "../../../../utils/cpi";

export default {
  async beforeCreate(event) {
    const { params } = event;
    if (!params.data.activity_id) {
      try {
        // Prefix with A
        const customId = `${await generateUniqueID("crm_activity_id_seq")}`;
        params.data.activity_id = customId;
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.main_account_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.main_account_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.main_contact_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.main_contact_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_contact: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.owner_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.owner_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_owner: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.organizer_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.organizer_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_organizer: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.processor_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.processor_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_processor: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.main_employee_responsible_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.main_employee_responsible_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_employee: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.main_account_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.main_account_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.main_account_party_id &&
          result?.business_partner?.count === 1
        ) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner: {
                set: [],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.main_contact_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.main_contact_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_contact: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.main_contact_party_id &&
          result?.business_partner_contact?.count === 1
        ) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_contact: {
                set: [],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.owner_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.owner_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_owner: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.owner_party_id &&
          result?.business_partner_owner?.count === 1
        ) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_owner: {
                set: [],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.organizer_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.organizer_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_organizer: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.organizer_party_id &&
          result?.business_partner_organizer?.count === 1
        ) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_organizer: {
                set: [],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.processor_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.processor_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_processor: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.processor_party_id &&
          result?.business_partner_processor?.count === 1
        ) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_processor: {
                set: [],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.main_employee_responsible_party_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.main_employee_responsible_party_id },
          });

        if (bp) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_employee: {
                connect: [bp.id],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.main_employee_responsible_party_id &&
          result?.business_partner_employee?.count === 1
        ) {
          await strapi.query("api::crm-activity.crm-activity").update({
            where: { id: result.id },
            data: {
              business_partner_employee: {
                set: [],
              },
            },
          });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
