{"kind": "collectionType", "collectionName": "products", "info": {"singularName": "product", "pluralName": "products", "displayName": "Product", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"product_id": {"type": "string", "unique": true, "required": true, "column": {"unique": true}}, "product_desc": {"type": "string"}, "name": {"type": "string"}, "product_status": {"type": "enumeration", "enum": ["ACTIVE", "INACTIVE"], "default": "INACTIVE"}, "stock": {"type": "string"}, "unit_measure": {"type": "string"}, "sales_org_id": {"type": "string"}, "sales_org_desc": {"type": "string"}, "distri_channel_id": {"type": "string"}, "distri_channel_desc": {"type": "string"}, "division_id": {"type": "string"}, "division_desc": {"type": "string"}, "plant_id": {"type": "string"}, "plant_desc": {"type": "string"}, "is_deleted": {"type": "boolean"}, "product_summary": {"type": "text"}, "productType": {"type": "string"}, "product_type_desc": {"type": "string"}, "product_old_id": {"type": "string"}, "product_old_id_desc": {"type": "string"}, "product_group": {"type": "string"}, "product_group_desc": {"type": "string"}, "base_unit": {"type": "string"}, "base_unit_desc": {"type": "string"}, "base_iso_unit": {"type": "string"}, "base_iso_unit_desc": {"type": "string"}, "item_category_group": {"type": "string"}, "item_category_group_desc": {"type": "string"}, "guid": {"type": "string"}, "specification": {"type": "text"}, "slug": {"type": "uid", "targetField": "name"}, "cross_plant_status": {"type": "string"}, "cross_plant_status_validity_date": {"type": "datetime"}, "creation_date": {"type": "datetime"}, "created_by_user": {"type": "string"}, "last_change_date": {"type": "datetime"}, "last_changed_by_user": {"type": "string"}, "last_change_date_time": {"type": "datetime"}, "is_marked_for_deletion": {"type": "boolean"}, "gross_weight": {"type": "decimal"}, "purchase_order_quantity_unit": {"type": "string"}, "source_of_supply": {"type": "string"}, "weight_unit": {"type": "string"}, "net_weight": {"type": "decimal"}, "country_of_origin": {"type": "string"}, "competitor_id": {"type": "string"}, "product_hierarchy": {"type": "string"}, "variable_purchase_order_unit_is_active": {"type": "boolean"}, "volume_unit": {"type": "string"}, "material_volume": {"type": "decimal"}, "anp_code": {"type": "string"}, "brand": {"type": "string"}, "procurement_rule": {"type": "string"}, "validity_start_date": {"type": "datetime"}, "low_level_code": {"type": "string"}, "serial_number_profile": {"type": "string"}, "size_or_dimension_text": {"type": "string"}, "industry_standard_name": {"type": "string"}, "product_standard_id": {"type": "string"}, "product_is_configurable": {"type": "boolean"}, "is_batch_management_required": {"type": "boolean"}, "handling_unit_type": {"type": "string"}, "is_pilferable": {"type": "boolean"}, "is_relevant_for_hzds_substances": {"type": "boolean"}, "quarantine_period": {"type": "integer"}, "time_unit_for_quarantine_period": {"type": "string"}, "quality_inspection_group": {"type": "string"}, "has_variable_tare_weight": {"type": "boolean"}, "maximum_packaging_length": {"type": "decimal"}, "maximum_packaging_width": {"type": "decimal"}, "maximum_packaging_height": {"type": "decimal"}, "livingGreen": {"type": "boolean"}, "adaCompliant": {"type": "boolean"}, "capacity": {"type": "string"}, "color": {"type": "string"}, "fiberContent": {"type": "string"}, "fillType": {"type": "string"}, "fillWeight": {"type": "string"}, "size": {"type": "string"}, "style": {"type": "string"}, "threadCount": {"type": "integer"}, "parentProduct": {"type": "string"}, "parent_product": {"type": "relation", "relation": "oneToOne", "target": "api::product.product"}, "ahVariantType": {"type": "string"}, "variantSort": {"type": "integer"}, "variantPlpName": {"type": "string"}, "sapHierarchy1": {"type": "string"}, "sapHierarchy2": {"type": "string"}, "sapHierarchy3": {"type": "string"}, "sapHierarchy4": {"type": "string"}, "sapHierarchy5": {"type": "string"}, "creationSystem": {"type": "string"}, "description": {"type": "text"}, "certification": {"type": "string"}, "culApproved": {"type": "boolean"}, "livingGreenLabel": {"type": "string"}, "ulApproved": {"type": "boolean"}, "diversitySupplier": {"type": "boolean"}, "diversityCertificate": {"type": "string"}, "energyStar": {"type": "boolean"}, "fscCertified": {"type": "boolean"}, "greenSealCertified": {"type": "boolean"}, "oekoTex": {"type": "boolean"}, "hazmatCode": {"type": "string"}, "sds": {"type": "string"}, "registryBrand": {"type": "string"}, "collectionName": {"type": "string"}, "originCountry": {"type": "string"}, "madeInUsa": {"type": "boolean"}, "manufacturerName": {"type": "string"}, "ean": {"type": "string"}, "productHeight": {"type": "decimal"}, "productLength": {"type": "decimal"}, "productWeight": {"type": "decimal"}, "productWidth": {"type": "decimal"}, "prop65": {"type": "boolean"}, "prop65Chemical": {"type": "string"}, "prop65ChemType": {"type": "string"}, "shippingHeight": {"type": "decimal"}, "shippingWeight": {"type": "decimal"}, "shippingWidth": {"type": "decimal"}, "shippingLength": {"type": "decimal"}, "truckOnly": {"type": "boolean"}, "amperage": {"type": "string"}, "applicableMaterial": {"type": "string"}, "averageCoveringArea": {"type": "string"}, "heatBTU": {"type": "integer"}, "casePack": {"type": "integer"}, "cleaner": {"type": "string"}, "cleaningPath": {"type": "string"}, "closureType": {"type": "string"}, "coating": {"type": "string"}, "concentrated": {"type": "boolean"}, "coolBTU": {"type": "integer"}, "cordLength": {"type": "decimal"}, "density": {"type": "string"}, "doorStyle": {"type": "string"}, "eel": {"type": "string"}, "filter": {"type": "string"}, "flavor": {"type": "string"}, "gauge": {"type": "string"}, "gsm": {"type": "integer"}, "heat": {"type": "boolean"}, "hemColor": {"type": "string"}, "hetz": {"type": "integer"}, "hoseLength": {"type": "decimal"}, "lbsPerDozen": {"type": "decimal"}, "material": {"type": "string"}, "mount": {"type": "string"}, "noiseLevel": {"type": "string"}, "numberOfPlys": {"type": "integer"}, "numberOfShelves": {"type": "integer"}, "outlet": {"type": "string"}, "piecePerSet": {"type": "integer"}, "plugType": {"type": "string"}, "pocketDepth": {"type": "string"}, "powerSource": {"type": "string"}, "productForm": {"type": "string"}, "refrigerantType": {"type": "string"}, "scent": {"type": "string"}, "sealType": {"type": "string"}, "shape": {"type": "string"}, "sheetCount": {"type": "integer"}, "voltage": {"type": "integer"}, "wattage": {"type": "integer"}, "warranty": {"type": "string"}, "warrantyType": {"type": "string"}, "brandName": {"type": "string"}, "pfas": {"type": "boolean"}, "salesUnit": {"type": "string"}, "brandCode": {"type": "string"}, "collectionCode": {"type": "string"}, "dropShip": {"type": "boolean"}, "sapEAN": {"type": "string"}, "virtualInventory": {"type": "boolean"}, "sapBlockedDate": {"type": "date"}, "sapBlocked": {"type": "boolean"}, "approvalStatus": {"type": "string"}, "unspscCode": {"type": "string"}, "vcClass": {"type": "string"}, "vcGroup": {"type": "string"}, "personalizationText": {"type": "string"}, "hasPersonalization": {"type": "boolean"}, "hasInstructions": {"type": "boolean"}, "videoUrl": {"type": "string"}, "videoTitle": {"type": "string"}, "videoDescription": {"type": "text"}, "howToVideoUrl": {"type": "string"}, "downloadUrl": {"type": "string"}, "pdpCmsId": {"type": "string"}, "orderCount": {"type": "integer"}, "orderDecile": {"type": "integer"}, "marginDecile": {"type": "integer"}, "privateLabel": {"type": "boolean"}, "minOrderQuantity": {"type": "integer"}, "unit": {"type": "string"}, "guage": {"type": "string"}, "hertz": {"type": "string"}, "refigerantType": {"type": "string"}, "itemsPerSalesUnit": {"type": "string"}, "manufacturerPartNumber": {"type": "string"}, "variantType": {"type": "string"}, "shippingUnit": {"type": "string"}, "catalogs": {"type": "relation", "relation": "manyToMany", "target": "api::product-catalog.product-catalog", "mappedBy": "products"}, "categories": {"type": "relation", "relation": "manyToMany", "target": "api::product-category.product-category", "mappedBy": "products"}, "descriptions": {"type": "relation", "relation": "oneToMany", "target": "api::product-description.product-description", "mappedBy": "product"}, "units_of_measures": {"type": "relation", "relation": "oneToMany", "target": "api::product-units-of-measure.product-units-of-measure", "mappedBy": "product"}, "units_of_measure_ean": {"type": "relation", "relation": "oneToMany", "target": "api::product-units-of-measure-ean.product-units-of-measure-ean", "mappedBy": "product"}, "plants": {"type": "relation", "relation": "oneToMany", "target": "api::product-plant.product-plant", "mappedBy": "product"}, "storage_locations": {"type": "relation", "relation": "oneToMany", "target": "api::product-storage-location.product-storage-location", "mappedBy": "product"}, "plant_procurement": {"type": "relation", "relation": "oneToMany", "target": "api::product-plant-procurement.product-plant-procurement", "mappedBy": "product"}, "plant_text": {"type": "relation", "relation": "oneToMany", "target": "api::product-plant-text.product-plant-text", "mappedBy": "product"}, "plant_storage": {"type": "relation", "relation": "oneToMany", "target": "api::product-plant-storage.product-plant-storage", "mappedBy": "product"}, "plant_sale": {"type": "relation", "relation": "oneToMany", "target": "api::product-plant-sale.product-plant-sale", "mappedBy": "product"}, "charc_value_types": {"type": "relation", "relation": "oneToMany", "target": "api::product-charc-value-type.product-charc-value-type", "mappedBy": "product"}, "sales_deliveries": {"type": "relation", "relation": "oneToMany", "target": "api::product-sales-delivery.product-sales-delivery", "mappedBy": "product"}, "suggestions": {"type": "relation", "relation": "oneToMany", "target": "api::product-suggestion.product-suggestion", "mappedBy": "suggested_product"}, "medias": {"type": "relation", "relation": "oneToMany", "target": "api::product-media.product-media", "mappedBy": "product"}, "product_businesses": {"type": "relation", "relation": "oneToMany", "target": "api::fg-product-business.fg-product-business", "mappedBy": "product"}, "product_internals": {"type": "relation", "relation": "oneToMany", "target": "api::fg-product-internal.fg-product-internal", "mappedBy": "product"}, "sales_texts": {"type": "relation", "relation": "oneToMany", "target": "api::product-sales-text.product-sales-text", "mappedBy": "product"}, "sales_taxes": {"type": "relation", "relation": "oneToMany", "target": "api::product-sales-tax.product-sales-tax", "mappedBy": "product"}, "sale": {"type": "relation", "relation": "oneToOne", "target": "api::product-sale.product-sale", "mappedBy": "product"}, "basic_texts": {"type": "relation", "relation": "oneToMany", "target": "api::product-basic-text.product-basic-text", "mappedBy": "product"}, "storage": {"type": "relation", "relation": "oneToOne", "target": "api::product-storage.product-storage", "mappedBy": "product"}, "crm_competitor_products": {"type": "relation", "relation": "oneToMany", "target": "api::crm-competitor-product.crm-competitor-product", "mappedBy": "competitor_product"}}}