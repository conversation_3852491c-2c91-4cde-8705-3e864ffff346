export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.charc_internal_id) {
      try {
        const charc_internal = await strapi
          .query("api::product-class-charc-type.product-class-charc-type")
          .findOne({
            where: { charc_internal_id: params.data.charc_internal_id },
          });

        if (charc_internal) {
          await strapi
            .query(
              "api::product-class-charc-type-description.product-class-charc-type-description"
            )
            .update({
              where: { id: result.id },
              data: {
                charc_internal: {
                  connect: [charc_internal.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.charc_internal_id) {
      try {
        const charc_internal = await strapi
          .query("api::product-class-charc-type.product-class-charc-type")
          .findOne({
            where: { charc_internal_id: params.data.charc_internal_id },
          });

        if (charc_internal) {
          await strapi
            .query(
              "api::product-class-charc-type-description.product-class-charc-type-description"
            )
            .update({
              where: { id: result.id },
              data: {
                charc_internal: {
                  connect: [charc_internal.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.charc_internal_id && result?.charc_internal?.count === 1) {
          await strapi
            .query(
              "api::product-class-charc-type-description.product-class-charc-type-description"
            )
            .update({
              where: { id: result.id },
              data: {
                charc_internal: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
