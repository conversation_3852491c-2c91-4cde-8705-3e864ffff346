
-- Set search path to <PERSON> to run script in public scheam
-- SHOW search_path;
-- SET search_path TO "public";
-- Set search path to QA to run script in QA scheam
-- SET search_path TO "QA";

-- Function to create a data for the new admin user and link it to the admin user
CREATE OR REPLACE FUNCTION create_data_for_admin()
RETURNS TRIGGER AS $$
DECLARE
    cart_id integer;
    document_id uuid;
    user_vendor_id integer;
    user_vendor_document_id uuid;
BEGIN
    -- Generate a UUID for the document_id
    document_id := gen_random_uuid();

    -- Insert into carts table
    INSERT INTO carts (document_id, created_by_id, updated_by_id, created_at, updated_at, locale)
    VALUES (document_id, NEW.id, NEW.id, NOW(), NOW(), 'en')
    RETURNING id INTO cart_id;

    -- Insert into carts_admin_user_lnk table
    INSERT INTO carts_admin_user_lnk (cart_id, user_id)
    VALUES (cart_id, NEW.id);

    -- Generate a UUID for the document_id
    user_vendor_document_id := gen_random_uuid();

    -- Insert into user_vendors table
    INSERT INTO user_vendors (document_id, created_by_id, updated_by_id, created_at, updated_at)
    VALUES (user_vendor_document_id, NEW.id, NEW.id, NOW(), NOW())
    RETURNING id INTO user_vendor_id;

    -- Insert into user_vendors_admin_user_lnk table
    INSERT INTO user_vendors_admin_user_lnk (user_vendor_id, user_id)
    VALUES (user_vendor_id, NEW.id);

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to delete the cart and its link when the admin user is deleted
CREATE OR REPLACE FUNCTION delete_data_for_admin()
RETURNS TRIGGER AS $$
BEGIN
    DELETE FROM carts_admin_user_lnk WHERE user_id = OLD.id;
    DELETE FROM carts WHERE created_by_id = OLD.id;
    DELETE FROM user_vendors_admin_user_lnk WHERE user_id = OLD.id;
    DELETE FROM user_vendors WHERE created_by_id = OLD.id;
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS after_admin_user_create_data ON admin_users;
DROP TRIGGER IF EXISTS before_admin_user_delete_data ON admin_users;

-- Create a trigger to create a cart and link it after an admin user is created
CREATE TRIGGER after_admin_user_create_data
AFTER INSERT ON admin_users
FOR EACH ROW
EXECUTE FUNCTION create_data_for_admin();

-- Create a trigger to delete a cart and its link before an admin user is deleted
CREATE TRIGGER before_admin_user_delete_data
BEFORE DELETE ON admin_users
FOR EACH ROW
EXECUTE FUNCTION delete_data_for_admin();


-- Create Custom sequences to generate unique id.
CREATE SEQUENCE bp_contact_id_seq
  START 1
  INCREMENT 1
  MINVALUE 1
  MAXVALUE 999999999
  CACHE 1;

CREATE SEQUENCE bp_prospect_id_seq
  START 1
  INCREMENT 1
  MINVALUE 1
  MAXVALUE 999999999
  CACHE 1;

-- Drop the sequence if it exists
DROP SEQUENCE IF EXISTS crm_activity_id_seq;

-- Create the sequence again
CREATE SEQUENCE crm_activity_id_seq
  START 1
  INCREMENT 1
  MINVALUE 1
  MAXVALUE 8999999999
  CACHE 1;

-- Drop the sequence if it exists
DROP SEQUENCE IF EXISTS crm_opportunity_id_seq;

-- Create the sequence again
CREATE SEQUENCE crm_opportunity_id_seq
  START 1
  INCREMENT 1
  MINVALUE 1
  MAXVALUE 8999999999
  CACHE 1;

-- Drop the sequence if it exists
DROP SEQUENCE IF EXISTS crm_competitor_id_seq;

-- Create the sequence again
CREATE SEQUENCE crm_competitor_id_seq
  START 1
  INCREMENT 1
  MINVALUE 1
  MAXVALUE 8999999999
  CACHE 1;

-- Drop the sequence if it exists
DROP SEQUENCE IF EXISTS crm_organisational_unit_id_seq;

-- Create the sequence again
CREATE SEQUENCE crm_organisational_unit_id_seq
  START 1
  INCREMENT 1
  MINVALUE 1
  MAXVALUE 8999999999
  CACHE 1;
