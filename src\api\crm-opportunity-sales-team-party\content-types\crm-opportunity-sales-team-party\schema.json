{"kind": "collectionType", "collectionName": "crm_opportunity_sales_team_parties", "info": {"singularName": "crm-opportunity-sales-team-party", "pluralName": "crm-opportunity-sales-team-parties", "displayName": "CRM Opportunity Sales Team Party"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"object_id": {"type": "string"}, "parent_object_id": {"type": "string"}, "role_category_code": {"type": "string"}, "role_code": {"type": "string"}, "party_id": {"type": "string"}, "business_partner": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "opportunity_sales_team_parties"}, "opportunity_id": {"type": "string"}, "opportunity": {"type": "relation", "relation": "manyToOne", "target": "api::crm-opportunity.crm-opportunity", "inversedBy": "opportunity_sales_team_parties"}}}