{"kind": "collectionType", "collectionName": "customer_tax_groupings", "info": {"singularName": "customer-tax-grouping", "pluralName": "customer-tax-groupings", "displayName": "Customer Tax Grouping"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"customer_tax_grouping_code": {"type": "string", "required": true}, "cust_tax_grp_exemption_certificate": {"type": "string"}, "cust_tax_group_exemption_rate": {"type": "decimal"}, "cust_tax_group_exemption_start_date": {"type": "datetime"}, "cust_tax_group_exemption_end_date": {"type": "datetime"}, "cust_tax_group_subjected_start_date": {"type": "datetime"}, "cust_tax_group_subjected_end_date": {"type": "datetime"}, "customer_id": {"type": "string", "required": true}, "customer": {"type": "relation", "relation": "manyToOne", "target": "api::customer.customer", "inversedBy": "customer_tax_groupings"}}}