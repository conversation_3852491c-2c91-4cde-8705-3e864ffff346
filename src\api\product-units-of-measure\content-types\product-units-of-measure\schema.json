{"kind": "collectionType", "collectionName": "product_units_of_measures", "info": {"singularName": "product-units-of-measure", "pluralName": "product-units-of-measures", "displayName": "Product Units Of Measure"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"alternative_unit": {"type": "string"}, "quantity_numerator": {"type": "integer"}, "quantity_denominator": {"type": "integer"}, "material_volume": {"type": "decimal"}, "volume_unit": {"type": "string"}, "gross_weight": {"type": "decimal"}, "weight_unit": {"type": "string"}, "global_trade_item_number": {"type": "string"}, "global_trade_item_number_category": {"type": "string"}, "unit_specific_product_length": {"type": "decimal"}, "unit_specific_product_width": {"type": "decimal"}, "unit_specific_product_height": {"type": "decimal"}, "product_measurement_unit": {"type": "string"}, "lower_level_packaging_unit": {"type": "string"}, "remaining_volume_after_nesting": {"type": "decimal"}, "maximum_stacking_factor": {"type": "integer"}, "capacity_usage": {"type": "decimal"}, "product_id": {"type": "string"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "units_of_measures"}, "units_of_measure_ean": {"type": "relation", "relation": "oneToOne", "target": "api::product-units-of-measure-ean.product-units-of-measure-ean", "mappedBy": "units_of_measure"}}}