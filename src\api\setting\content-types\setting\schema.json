{"kind": "collectionType", "collectionName": "settings", "info": {"singularName": "setting", "pluralName": "settings", "displayName": "Setting"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"base_price_code": {"type": "string"}, "cust_return_type_code": {"type": "string"}, "cust_return_type_descr": {"type": "string"}, "discount_code": {"type": "string"}, "low_stock_qty": {"type": "integer"}, "min_quote_price": {"type": "integer"}, "quote_text_code": {"type": "string"}, "quote_text_descr": {"type": "string"}, "sales_quote_type_code": {"type": "string"}, "sales_quote_type_descr": {"type": "string"}, "shipping_code": {"type": "string"}, "tax_code": {"type": "string"}, "text_code": {"type": "string"}, "text_descr": {"type": "string"}, "global_po_code": {"type": "string"}, "global_po_descr": {"type": "string"}, "global_miscellaneous_code": {"type": "string"}, "global_miscellaneous_descr": {"type": "string"}, "guest_user_customer": {"type": "string"}, "vendor_user_role": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.role"}, "vendor_admin_user_emails": {"type": "json"}, "crm_admin_user_emails": {"type": "json"}, "is_fgci_in_progress": {"type": "boolean", "default": false}, "is_fgpi_in_progress": {"type": "boolean", "default": false}}}