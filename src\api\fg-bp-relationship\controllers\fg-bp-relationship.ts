/**
 * fg-bp-relationship controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";

export default factories.createCoreController(
  "api::fg-bp-relationship.fg-bp-relationship",
  ({ strapi }) => ({
    async import(ctx: Context) {
      try {
        const records = ctx.request.body;

        if (!Array.isArray(records)) {
          return ctx.badRequest("Input must be an array of records.");
        }

        const CHUNK_SIZE = 1000;
        const summary = {
          totalRecords: records.length,
          insertedCount: 0,
          failedCount: 0,
          failedLogs: [],
        };

        for (let i = 0; i < records.length; i += CHUNK_SIZE) {
          const chunk = records.slice(i, i + CHUNK_SIZE);

          // Step 1: Extract unique combinations for deletion
          const combinations = chunk
            .map((r) => {
              if (
                r.locationbusinesspartner &&
                r.relationshipbelongsto &&
                r.membershipparentbp
              ) {
                return {
                  locationbusinesspartner: r.locationbusinesspartner,
                  relationshipbelongsto: r.relationship<PERSON><PERSON>,
                  membershipparentbp: r.membershipparentbp,
                };
              }
              return null;
            })
            .filter(Boolean);

          // Step 2: Build $or condition array
          const deleteConditions = combinations.map((combo) => ({
            locationbusinesspartner: combo.locationbusinesspartner,
            relationshipbelongsto: combo.relationshipbelongsto,
            membershipparentbp: combo.membershipparentbp,
          }));

          // Step 3: Delete existing records matching these composite keys
          if (deleteConditions.length > 0) {
            await strapi.db
              .query("api::fg-bp-relationship.fg-bp-relationship")
              .deleteMany({
                where: {
                  $or: deleteConditions,
                },
              });
          }

          try {
            // Step 3: Bulk insert chunk
            await strapi.db
              .query("api::fg-bp-relationship.fg-bp-relationship")
              .createMany({
                data: { ...chunk, locale: "en" },
              });

            summary.insertedCount += chunk.length;
          } catch (error) {
            // If bulk insert fails, insert one by one to catch errors
            for (const record of chunk) {
              try {
                await strapi.db
                  .query("api::fg-bp-relationship.fg-bp-relationship")
                  .create({
                    data: { ...record, locale: "en" },
                  });
                summary.insertedCount++;
              } catch (err) {
                summary.failedCount++;
                if (summary.failedLogs.length < 50) {
                  summary.failedLogs.push({
                    record,
                    error: err.message || "Unknown error",
                  });
                }
              }
            }
          }
        }

        ctx.send({
          status: "completed",
          totalRecords: summary.totalRecords,
          inserted: summary.insertedCount,
          failed: summary.failedCount,
          failedLogs: summary.failedLogs,
          failedLogsTruncated: summary.failedLogs.length >= 50,
        });
      } catch (error) {
        console.error("Bulk import failed:", error);
        ctx.internalServerError("Unexpected server error during bulk import.");
      }
    },
  })
);
