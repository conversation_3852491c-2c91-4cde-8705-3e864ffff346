{"kind": "collectionType", "collectionName": "customer_sales_areas", "info": {"singularName": "customer-sales-area", "pluralName": "customer-sales-areas", "displayName": "Customer Sales Area", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"sales_organization": {"type": "string", "required": true}, "distribution_channel": {"type": "string", "required": true}, "division": {"type": "string", "required": true}, "currency": {"type": "string"}, "account_by_customer": {"type": "string"}, "authorization_group": {"type": "string"}, "billing_is_blocked_for_customer": {"type": "string"}, "complete_delivery_is_defined": {"type": "string"}, "credit_control_area": {"type": "string"}, "customer_account_group": {"type": "string"}, "cust_is_rlvt_for_settlmt_mgmt": {"type": "boolean"}, "customer_is_rebate_relevant": {"type": "boolean"}, "cust_prod_proposal_procedure": {"type": "string"}, "customer_abc_classification": {"type": "string"}, "customer_account_assignment_group": {"type": "string"}, "customer_group": {"type": "string"}, "additional_customer_group1": {"type": "string"}, "additional_customer_group2": {"type": "string"}, "additional_customer_group3": {"type": "string"}, "additional_customer_group4": {"type": "string"}, "additional_customer_group5": {"type": "string"}, "customer_payment_terms": {"type": "string"}, "customer_price_group": {"type": "string"}, "customer_pricing_procedure": {"type": "string"}, "customer_statistics_group": {"type": "string"}, "deletion_indicator": {"type": "boolean"}, "delivery_is_blocked_for_customer": {"type": "boolean"}, "delivery_priority": {"type": "string"}, "exchange_rate_type": {"type": "string"}, "incoterms_version": {"type": "string"}, "incoterms_classification": {"type": "string"}, "incoterms_location1": {"type": "string"}, "incoterms_location2": {"type": "string"}, "incoterms_transfer_location": {"type": "string"}, "incoterms_sup_chn_loc1_addl_uuid": {"type": "string"}, "incoterms_sup_chn_loc2_addl_uuid": {"type": "string"}, "incoterms_sup_chn_dvtg_loc_addl_uuid": {"type": "string"}, "insp_sbst_has_no_time_or_quantity": {"type": "boolean"}, "invoice_date": {"type": "string"}, "invoice_list_schedule": {"type": "string"}, "item_order_probability_in_percent": {"type": "string"}, "manual_invoice_maint_is_relevant": {"type": "boolean"}, "max_nmbr_of_partial_delivery": {"type": "string"}, "order_combination_is_allowed": {"type": "boolean"}, "order_is_blocked_for_customer": {"type": "boolean"}, "overdeliv_tolrtd_lmt_ratio_in_pct": {"type": "string"}, "partial_delivery_is_allowed": {"type": "boolean"}, "payment_guarantee_procedure": {"type": "string"}, "price_list_type": {"type": "string"}, "product_unit_group": {"type": "string"}, "proof_of_delivery_time_value": {"type": "string"}, "sales_district": {"type": "string"}, "sales_group": {"type": "string"}, "sales_item_proposal": {"type": "string"}, "sales_office": {"type": "string"}, "shipping_condition": {"type": "string"}, "sls_doc_is_rlvt_for_proof_of_deliv": {"type": "boolean"}, "sls_unlmtd_ovrdeliv_is_allwd": {"type": "boolean"}, "supplying_plant": {"type": "string"}, "underdeliv_tolrtd_lmt_ratio_in_pct": {"type": "string"}, "customer_id": {"type": "string", "required": true}, "customer": {"type": "relation", "relation": "manyToOne", "target": "api::customer.customer", "inversedBy": "sales_areas"}, "sales_area_texts": {"type": "relation", "relation": "oneToMany", "target": "api::customer-sales-area-text.customer-sales-area-text", "mappedBy": "sales_area"}}}