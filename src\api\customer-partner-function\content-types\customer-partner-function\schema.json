{"kind": "collectionType", "collectionName": "customer_partner_functions", "info": {"singularName": "customer-partner-function", "pluralName": "customer-partner-functions", "displayName": "Customer Partner Function"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string"}, "address": {"type": "string"}, "sales_organization": {"type": "string", "required": true}, "distribution_channel": {"type": "string", "required": true}, "division": {"type": "string", "required": true}, "partner_counter": {"type": "string", "required": true}, "partner_function": {"type": "string", "required": true}, "bp_customer_number": {"type": "string"}, "business_partner": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "partner_functions"}, "customer_id": {"type": "string", "required": true}, "customer": {"type": "relation", "relation": "manyToOne", "target": "api::customer.customer", "inversedBy": "partner_functions"}}}