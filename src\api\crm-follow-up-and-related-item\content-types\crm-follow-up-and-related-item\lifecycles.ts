export default {
  async afterCreate(event) {
    const { result, params } = event;

    if (params.data.activity_id) {
      try {
        const activity = await strapi
          .query("api::crm-activity.crm-activity")
          .findOne({
            where: { activity_id: params.data.activity_id },
          });

        if (activity) {
          await strapi
            .query(
              "api::crm-follow-up-and-related-item.crm-follow-up-and-related-item"
            )
            .update({
              where: { id: result.id },
              data: {
                activity: {
                  connect: [activity.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.activity_transaction_id) {
      try {
        const activity = await strapi
          .query("api::crm-activity.crm-activity")
          .findOne({
            where: { activity_id: params.data.activity_transaction_id },
          });

        if (activity) {
          await strapi
            .query(
              "api::crm-follow-up-and-related-item.crm-follow-up-and-related-item"
            )
            .update({
              where: { id: result.id },
              data: {
                activity_transaction: {
                  connect: [activity.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.activity_id) {
      try {
        const activity = await strapi
          .query("api::crm-activity.crm-activity")
          .findOne({
            where: { activity_id: params.data.activity_id },
          });

        if (activity) {
          await strapi
            .query(
              "api::crm-follow-up-and-related-item.crm-follow-up-and-related-item"
            )
            .update({
              where: { id: result.id },
              data: {
                activity: {
                  connect: [activity.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.activity_id && result?.activity?.count === 1) {
          await strapi
            .query(
              "api::crm-follow-up-and-related-item.crm-follow-up-and-related-item"
            )
            .update({
              where: { id: result.id },
              data: {
                activity: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.activity_transaction_id) {
      try {
        const activity = await strapi
          .query("api::crm-activity.crm-activity")
          .findOne({
            where: { activity_id: params.data.activity_transaction_id },
          });

        if (activity) {
          await strapi
            .query(
              "api::crm-follow-up-and-related-item.crm-follow-up-and-related-item"
            )
            .update({
              where: { id: result.id },
              data: {
                activity_transaction: {
                  connect: [activity.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.activity_transaction_id &&
          result?.activity_transaction?.count === 1
        ) {
          await strapi
            .query(
              "api::crm-follow-up-and-related-item.crm-follow-up-and-related-item"
            )
            .update({
              where: { id: result.id },
              data: {
                activity_transaction: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
