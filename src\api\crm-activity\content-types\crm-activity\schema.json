{"kind": "collectionType", "collectionName": "crm_activities", "info": {"singularName": "crm-activity", "pluralName": "crm-activities", "displayName": "CRM Activity"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"activity_id": {"type": "string", "unique": true, "column": {"unique": true}}, "document_type": {"type": "string"}, "subject": {"type": "text"}, "start_date": {"type": "datetime"}, "end_date": {"type": "datetime"}, "due_date": {"type": "datetime"}, "completion_date": {"type": "datetime"}, "completion_percent": {"type": "string"}, "actual_duration": {"type": "string"}, "planned_duration": {"type": "string"}, "full_day_indicator": {"type": "boolean", "default": false}, "location": {"type": "string"}, "activity_status": {"type": "string"}, "priority": {"type": "string"}, "initiator_code": {"type": "string"}, "sales_organization": {"type": "string"}, "distribution_channel": {"type": "string"}, "division": {"type": "string"}, "account_uuid": {"type": "string"}, "primary_contact_uuid": {"type": "string"}, "phone_call_category": {"type": "string"}, "reason": {"type": "string"}, "brand": {"type": "string"}, "customer_group": {"type": "string"}, "ranking": {"type": "string"}, "customer_timezone": {"type": "string"}, "disposition_code": {"type": "string"}, "appointment_category": {"type": "string"}, "task_category": {"type": "string"}, "processor_party_id": {"type": "string"}, "business_partner_processor": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "processor_activities"}, "main_employee_responsible_party_id": {"type": "string"}, "business_partner_employee": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "employee_activities"}, "main_account_party_id": {"type": "string"}, "business_partner": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "activities"}, "main_contact_party_id": {"type": "string"}, "business_partner_contact": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "contact_activities"}, "owner_party_id": {"type": "string"}, "business_partner_owner": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "owner_activities"}, "organizer_party_id": {"type": "string"}, "business_partner_organizer": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "organizer_activities"}, "notes": {"type": "relation", "relation": "oneToMany", "target": "api::crm-note.crm-note", "mappedBy": "activity"}, "involved_parties": {"type": "relation", "relation": "oneToMany", "target": "api::crm-involved-party.crm-involved-party", "mappedBy": "activity"}, "follow_up_and_related_items": {"type": "relation", "relation": "oneToMany", "target": "api::crm-follow-up-and-related-item.crm-follow-up-and-related-item", "mappedBy": "activity"}, "follow_up_and_related_item_transactions": {"type": "relation", "relation": "oneToMany", "target": "api::crm-follow-up-and-related-item.crm-follow-up-and-related-item", "mappedBy": "activity_transaction"}, "attachments": {"type": "relation", "relation": "oneToMany", "target": "api::crm-attachment.crm-attachment", "mappedBy": "activity"}, "opportunity_followups": {"type": "relation", "relation": "oneToMany", "target": "api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document", "mappedBy": "activity"}}}