{"kind": "collectionType", "collectionName": "admin_user_role_extentions", "info": {"singularName": "admin-user-role-extention", "pluralName": "admin-user-role-extentions", "displayName": "Admin User Role Extention"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"admin_user_role": {"type": "relation", "relation": "oneToOne", "target": "admin::role", "required": true}, "can_manage_admin_users": {"type": "boolean", "default": false}, "manageable_admin_roles": {"type": "relation", "relation": "oneToMany", "target": "admin::role"}, "manageable_user_roles": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.role"}}}