/**
 * configuration custom router
 */

export default {
  routes: [
    {
      method: "GET",
      path: "/fg-customer-internal/sync",
      handler: "fg-customer-internal.sync",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/fg-customer-internal/restore",
      handler: "fg-customer-internal.restoreCustomerInternalData",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/fg-customer-internal/:customerId/export",
      handler: "fg-customer-internal.exportCustomerInternals",
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
