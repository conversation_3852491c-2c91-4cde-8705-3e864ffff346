export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.supplier_id && params.data.purchasing_organization) {
      try {
        const supplier_purchasing_org = await strapi
          .query("api::supplier-purchasing-org.supplier-purchasing-org")
          .findOne({
            where: {
              supplier_id: params.data.supplier_id,
              purchasing_organization: params.data.purchasing_organization,
            },
          });

        if (supplier_purchasing_org) {
          await strapi
            .query(
              "api::supplier-purchasing-org-text.supplier-purchasing-org-text"
            )
            .update({
              where: { id: result.id },
              data: {
                supplier_purchasing_org: {
                  connect: [supplier_purchasing_org.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.supplier_id && params.data.purchasing_organization) {
      try {
        const supplier_purchasing_org = await strapi
          .query("api::supplier-purchasing-org.supplier-purchasing-org")
          .findOne({
            where: {
              supplier_id: params.data.supplier_id,
              purchasing_organization: params.data.purchasing_organization,
            },
          });

        if (supplier_purchasing_org) {
          await strapi
            .query(
              "api::supplier-purchasing-org-text.supplier-purchasing-org-text"
            )
            .update({
              where: { id: result.id },
              data: {
                supplier_purchasing_org: {
                  connect: [supplier_purchasing_org.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.supplier_id &&
          !result.purchasing_organization &&
          result?.supplier_purchasing_org?.count === 1
        ) {
          await strapi
            .query("api::supplier-purchasing-org.supplier-purchasing-org")
            .update({
              where: { id: result.id },
              data: {
                supplier_purchasing_org: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
