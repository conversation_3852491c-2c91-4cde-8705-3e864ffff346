export default {
  async afterCreate(event) {
    const { result, params } = event;
    if (params.data.vendor_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.vendor_id },
          });

        if (bp) {
          await strapi
            .query("api::business-partner-address.business-partner-address")
            .update({
              where: { id: result.id },
              data: {
                vendor: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.vendor_id) {
      try {
        const bp = await strapi
          .query("api::business-partner.business-partner")
          .findOne({
            where: { bp_id: params.data.vendor_id },
          });

        if (bp) {
          await strapi
            .query("api::business-partner-address.business-partner-address")
            .update({
              where: { id: result.id },
              data: {
                vendor: {
                  connect: [bp.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.vendor_id && result?.vendor?.count === 1) {
          await strapi
            .query("api::business-partner-address.business-partner-address")
            .update({
              where: { id: result.id },
              data: {
                vendor: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
