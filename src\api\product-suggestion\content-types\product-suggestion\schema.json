{"kind": "collectionType", "collectionName": "product_suggestions", "info": {"singularName": "product-suggestion", "pluralName": "product-suggestions", "displayName": "Product Suggestion"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"product_suggestion_type_id": {"type": "relation", "relation": "manyToOne", "target": "api::product-suggestion-type.product-suggestion-type"}, "product_id": {"type": "string"}, "product_suggested_id": {"type": "string"}, "product_suggested": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "suggestions"}}}