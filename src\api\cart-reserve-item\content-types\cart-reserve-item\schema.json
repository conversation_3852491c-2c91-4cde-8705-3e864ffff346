{"kind": "collectionType", "collectionName": "cart_reserve_items", "info": {"singularName": "cart-reserve-item", "pluralName": "cart-reserve-items", "displayName": "Cart Reserve Item"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"material": {"type": "string", "required": true}, "requested_quantity": {"type": "integer", "required": true, "default": 1}, "cart": {"type": "relation", "relation": "manyToOne", "target": "api::cart-reserve.cart-reserve", "inversedBy": "cart_items"}}}