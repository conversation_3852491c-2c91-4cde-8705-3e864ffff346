HOST=0.0.0.0
PORT=1337
APP_KEYS="toBeModified1,toBeModified2"
API_TOKEN_SALT=tobemodified
ADMIN_JWT_SECRET=tobemodified
TRANSFER_TOKEN_SALT=tobemodified
JWT_SECRET=tobemodified

JWT_SECRET=tobemodified

# Database
DATABASE_CLIENT=postgres
DATABASE_URL=postgresql://username:password@localhost:5432/chs
DATABASE_SSL=false
DATABASE_SSL_REJECT_UNAUTHORIZED=false

# AWS S3 Bucket 
AWS_ACCESS_KEY_ID=XXXXX
AWS_ACCESS_SECRET=XXXXXXXXXXXXXXXXXXXX+J
AWS_REGION=us-east-2
AWS_BUCKET=snjya-chs

# SMTP
### Gmail setup Reference Link https://www.youtube.com/watch?v=IWxwWFTlTUQ
SMTP_SERVICE="gmail" # service name
SMTP_HOST="smtp.gmail.com" # service host name
SMTP_SECURE=false # Service security
SMTP_PORT=587 # service port
SMTP_USERNAME="<EMAIL>" # email address
SMTP_PASSWORD="gmail app password" # gmail APP password
SMTP_FROM="<EMAIL>" # send email using this id

