{"kind": "collectionType", "collectionName": "guest_user_carts", "info": {"singularName": "guest-user-cart", "pluralName": "guest-user-carts", "displayName": "Guest User <PERSON>"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"session_id": {"type": "string", "required": true, "unique": true, "column": {"unique": true}}, "expire_at": {"type": "datetime", "required": true}, "customer": {"type": "relation", "relation": "manyToOne", "target": "api::customer.customer"}, "cart_items": {"type": "relation", "relation": "oneToMany", "target": "api::guest-user-cart-item.guest-user-cart-item", "mappedBy": "cart"}}}