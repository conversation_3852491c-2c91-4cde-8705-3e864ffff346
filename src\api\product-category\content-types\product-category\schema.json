{"kind": "collectionType", "collectionName": "product_categories", "info": {"singularName": "product-category", "pluralName": "product-categories", "displayName": "Product Category", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "required": true, "unique": true, "column": {"unique": true}}, "parent_category_id": {"type": "integer"}, "parent": {"type": "relation", "relation": "manyToOne", "target": "api::product-category.product-category", "inversedBy": "children"}, "children": {"type": "relation", "relation": "oneToMany", "target": "api::product-category.product-category", "mappedBy": "parent"}, "products": {"type": "relation", "relation": "manyToMany", "target": "api::product.product", "inversedBy": "categories"}, "catalogs": {"type": "relation", "relation": "manyToMany", "target": "api::product-catalog.product-catalog", "mappedBy": "categories"}, "category_id": {"type": "string", "unique": true, "column": {"unique": true}}, "url": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}}}