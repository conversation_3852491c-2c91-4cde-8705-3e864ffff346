{"kind": "collectionType", "collectionName": "fg_customer_product_internals", "info": {"singularName": "fg-customer-product-internal", "pluralName": "fg-customer-product-internals", "displayName": "Flexible Group Customer Product Internal"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"flex_group_id": {"type": "string"}, "product_id": {"type": "string"}, "bp_id": {"type": "string"}}}