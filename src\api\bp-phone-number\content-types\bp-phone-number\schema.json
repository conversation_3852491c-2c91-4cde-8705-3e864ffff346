{"kind": "collectionType", "collectionName": "bp_phone_numbers", "info": {"singularName": "bp-phone-number", "pluralName": "bp-phone-numbers", "displayName": "Business Partner Phone Number"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"address_id": {"type": "string", "required": true}, "ordinal_number": {"type": "integer", "required": true}, "person": {"type": "string", "required": true}, "destination_location_country": {"type": "string"}, "international_phone_number": {"type": "string"}, "is_default_phone_number": {"type": "boolean"}, "phone_number": {"type": "string"}, "phone_number_extension": {"type": "string"}, "phone_number_type": {"type": "string"}, "address_communication_remark_text": {"type": "text"}, "business_partner_address": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner-address.business-partner-address", "inversedBy": "phone_numbers"}, "bp_contact_address": {"type": "relation", "relation": "manyToOne", "target": "api::bp-contact-to-address.bp-contact-to-address", "inversedBy": "phone_numbers"}}}