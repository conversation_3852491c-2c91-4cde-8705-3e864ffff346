import { SyncBusinessPartner } from "../../sync";

async function queueSafeSync() {
  // Delay execution slightly to avoid DB overload
  await new Promise((resolve) => setTimeout(resolve, 250));
  const inProcess = await strapi.db
    .connection("business_partner_stagings")
    .where({ staging_status: "IN_PROCESS" })
    .first();

  if (!inProcess) {
    SyncBusinessPartner();
  }
}

export default {
  async afterCreate(event) {
    setImmediate(() => {
      queueSafeSync();
    });
  },
  async afterUpdate(event) {
    setImmediate(() => {
      queueSafeSync();
    });
  },
};
