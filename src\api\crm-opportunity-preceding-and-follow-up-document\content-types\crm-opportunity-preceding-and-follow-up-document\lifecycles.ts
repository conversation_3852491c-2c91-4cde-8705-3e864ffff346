export default {
  async afterCreate(event) {
    const { result, params } = event;

    if (params.data.opportunity_id) {
      try {
        const opportunity = await strapi
          .query("api::crm-opportunity.crm-opportunity")
          .findOne({
            where: { opportunity_id: params.data.opportunity_id },
          });

        if (opportunity) {
          await strapi
            .query(
              "api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document"
            )
            .update({
              where: { id: result.id },
              data: {
                opportunity: {
                  connect: [opportunity.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.opportunity_transaction_id) {
      try {
        const opportunity = await strapi
          .query("api::crm-opportunity.crm-opportunity")
          .findOne({
            where: { opportunity_id: params.data.opportunity_transaction_id },
          });

        if (opportunity) {
          await strapi
            .query(
              "api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document"
            )
            .update({
              where: { id: result.id },
              data: {
                opportunity_transaction: {
                  connect: [opportunity.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }

    if (params.data.activity_id) {
      try {
        const activity = await strapi
          .query("api::crm-activity.crm-activity")
          .findOne({
            where: { activity_id: params.data.activity_id },
          });

        if (activity) {
          await strapi
            .query(
              "api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document"
            )
            .update({
              where: { id: result.id },
              data: {
                activity: {
                  connect: [activity.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterCreate:", error);
      }
    }
  },
  async afterUpdate(event) {
    const { result, params } = event;

    if (params.data.opportunity_id) {
      try {
        const opportunity = await strapi
          .query("api::crm-opportunity.crm-opportunity")
          .findOne({
            where: { opportunity_id: params.data.opportunity_id },
          });

        if (opportunity) {
          await strapi
            .query(
              "api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document"
            )
            .update({
              where: { id: result.id },
              data: {
                opportunity: {
                  connect: [opportunity.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.opportunity_id && result?.opportunity?.count === 1) {
          await strapi
            .query(
              "api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document"
            )
            .update({
              where: { id: result.id },
              data: {
                opportunity: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.opportunity_transaction_id) {
      try {
        const opportunity = await strapi
          .query("api::crm-opportunity.crm-opportunity")
          .findOne({
            where: { opportunity_id: params.data.opportunity_transaction_id },
          });

        if (opportunity) {
          await strapi
            .query(
              "api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document"
            )
            .update({
              where: { id: result.id },
              data: {
                opportunity_transaction: {
                  connect: [opportunity.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (
          !result.opportunity_transaction_id &&
          result?.opportunity_transaction?.count === 1
        ) {
          await strapi
            .query(
              "api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document"
            )
            .update({
              where: { id: result.id },
              data: {
                opportunity_transaction: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }

    if (params.data.activity_id) {
      try {
        const activity = await strapi
          .query("api::crm-activity.crm-activity")
          .findOne({
            where: { activity_id: params.data.activity_id },
          });

        if (activity) {
          await strapi
            .query(
              "api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document"
            )
            .update({
              where: { id: result.id },
              data: {
                activity: {
                  connect: [activity.id],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate:", error);
      }
    } else {
      try {
        if (!result.activity_id && result?.activity?.count === 1) {
          await strapi
            .query(
              "api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document"
            )
            .update({
              where: { id: result.id },
              data: {
                activity: {
                  set: [],
                },
              },
            });
        }
      } catch (error) {
        console.error("Error in afterUpdate when disconnecting:", error);
      }
    }
  },
};
