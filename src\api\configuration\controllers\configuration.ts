/**
 * configuration controller
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreController(
  "api::configuration.configuration",
  ({ strapi }) => ({
    async resetOrderTypeIsActive(ctx) {
      try {
        // Update all records in the `configuration` collection to set `is_active` to false
        await strapi.db.query("api::configuration.configuration").updateMany({
          data: { is_active: false, type: "ORDER_TYPE" },
        });

        // Send a success response
        ctx.send({
          message: "All order type records have been reset with is_active set to false.",
        });
      } catch (error) {
        ctx.throw(500, "Failed to reset is_active field");
      }
    },
  })
);
