{"kind": "collectionType", "collectionName": "suppliers", "info": {"singularName": "supplier", "pluralName": "suppliers", "displayName": "Supplier"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"supplier_id": {"type": "string", "unique": true, "required": true, "column": {"unique": true}}, "alternative_payee_account_number": {"type": "string"}, "authorization_group": {"type": "string"}, "birth_date": {"type": "datetime"}, "br_tax_is_split": {"type": "boolean"}, "concatenated_international_loc_no": {"type": "string"}, "created_by_user": {"type": "string"}, "creation_date": {"type": "datetime"}, "data_exchange_instruction_key": {"type": "string"}, "deletion_indicator": {"type": "boolean"}, "fiscal_address": {"type": "string"}, "industry": {"type": "string"}, "international_location_number_1": {"type": "string"}, "international_location_number_2": {"type": "string"}, "international_location_number_3": {"type": "string"}, "is_natural_person": {"type": "boolean"}, "payment_is_blocked_for_supplier": {"type": "boolean"}, "payment_reason": {"type": "string"}, "posting_is_blocked": {"type": "boolean"}, "purchasing_is_blocked": {"type": "boolean"}, "responsible_type": {"type": "string"}, "suplr_qlty_in_procmt_certfn_valid_to": {"type": "datetime"}, "suplr_quality_management_system": {"type": "string"}, "supplier_account_group": {"type": "string"}, "supplier_corporate_group": {"type": "string"}, "supplier_full_name": {"type": "string"}, "supplier_name": {"type": "string"}, "supplier_procurement_block": {"type": "string"}, "suplr_proof_of_deliv_rlvt_code": {"type": "string"}, "tax_number_1": {"type": "string"}, "tax_number_2": {"type": "string"}, "tax_number_3": {"type": "string"}, "tax_number_4": {"type": "string"}, "tax_number_5": {"type": "string"}, "tax_number_responsible": {"type": "string"}, "tax_number_type": {"type": "string"}, "vat_registration": {"type": "string"}, "customer_id": {"type": "string"}, "bp_id": {"type": "string"}, "business_partner": {"type": "relation", "relation": "oneToOne", "target": "api::business-partner.business-partner", "inversedBy": "supplier"}, "texts": {"type": "relation", "relation": "oneToMany", "target": "api::supplier-text.supplier-text", "mappedBy": "supplier"}, "companies": {"type": "relation", "relation": "oneToMany", "target": "api::supplier-company.supplier-company", "mappedBy": "supplier"}, "company_texts": {"type": "relation", "relation": "oneToMany", "target": "api::supplier-company-text.supplier-company-text", "mappedBy": "supplier"}, "partner_functions": {"type": "relation", "relation": "oneToMany", "target": "api::supplier-partner-func.supplier-partner-func", "mappedBy": "supplier"}, "supplier_purchasing_orgs": {"type": "relation", "relation": "oneToMany", "target": "api::supplier-purchasing-org.supplier-purchasing-org", "mappedBy": "supplier"}, "users": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user", "mappedBy": "suppliers"}, "admin_users": {"type": "relation", "relation": "manyToMany", "target": "admin::user"}, "user_vendors": {"type": "relation", "relation": "oneToMany", "target": "api::user-vendor.user-vendor", "mappedBy": "supplier"}}}