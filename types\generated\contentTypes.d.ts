import type { Schema, Struct } from '@strapi/strapi';

export interface AdminApiToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_tokens';
  info: {
    description: '';
    displayName: 'Api Token';
    name: 'Api Token';
    pluralName: 'api-tokens';
    singularName: 'api-token';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Schema.Attribute.DefaultTo<''>;
    encryptedKey: Schema.Attribute.Text &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    expiresAt: Schema.Attribute.DateTime;
    lastUsedAt: Schema.Attribute.DateTime;
    lifespan: Schema.Attribute.BigInteger;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::api-token'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'admin::api-token-permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'read-only'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_api_token_permissions';
  info: {
    description: '';
    displayName: 'API Token Permission';
    name: 'API Token Permission';
    pluralName: 'api-token-permissions';
    singularName: 'api-token-permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::api-token-permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    token: Schema.Attribute.Relation<'manyToOne', 'admin::api-token'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminPermission extends Struct.CollectionTypeSchema {
  collectionName: 'admin_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'Permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
    conditions: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::permission'> &
      Schema.Attribute.Private;
    properties: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Relation<'manyToOne', 'admin::role'>;
    subject: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminRole extends Struct.CollectionTypeSchema {
  collectionName: 'admin_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'Role';
    pluralName: 'roles';
    singularName: 'role';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::role'> &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<'oneToMany', 'admin::permission'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    users: Schema.Attribute.Relation<'manyToMany', 'admin::user'>;
  };
}

export interface AdminTransferToken extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_tokens';
  info: {
    description: '';
    displayName: 'Transfer Token';
    name: 'Transfer Token';
    pluralName: 'transfer-tokens';
    singularName: 'transfer-token';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    accessKey: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Schema.Attribute.DefaultTo<''>;
    expiresAt: Schema.Attribute.DateTime;
    lastUsedAt: Schema.Attribute.DateTime;
    lifespan: Schema.Attribute.BigInteger;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminTransferTokenPermission
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    description: '';
    displayName: 'Transfer Token Permission';
    name: 'Transfer Token Permission';
    pluralName: 'transfer-token-permissions';
    singularName: 'transfer-token-permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'admin::transfer-token-permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    token: Schema.Attribute.Relation<'manyToOne', 'admin::transfer-token'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface AdminUser extends Struct.CollectionTypeSchema {
  collectionName: 'admin_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'User';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    blocked: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email: Schema.Attribute.Email &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.Unique &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    firstname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    isActive: Schema.Attribute.Boolean &
      Schema.Attribute.Private &
      Schema.Attribute.DefaultTo<false>;
    lastname: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<'oneToMany', 'admin::user'> &
      Schema.Attribute.Private;
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    preferedLanguage: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    registrationToken: Schema.Attribute.String & Schema.Attribute.Private;
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
    roles: Schema.Attribute.Relation<'manyToMany', 'admin::role'> &
      Schema.Attribute.Private;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    username: Schema.Attribute.String;
  };
}

export interface ApiAdminUserExtensionAdminUserExtension
  extends Struct.CollectionTypeSchema {
  collectionName: 'admin_user_extensions';
  info: {
    displayName: 'Admin User Extension';
    pluralName: 'admin-user-extensions';
    singularName: 'admin-user-extension';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    address: Schema.Attribute.String;
    admin_user: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    last_logout_at: Schema.Attribute.DateTime;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::admin-user-extension.admin-user-extension'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiAdminUserRoleExtentionAdminUserRoleExtention
  extends Struct.CollectionTypeSchema {
  collectionName: 'admin_user_role_extentions';
  info: {
    displayName: 'Admin User Role Extention';
    pluralName: 'admin-user-role-extentions';
    singularName: 'admin-user-role-extention';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    admin_user_role: Schema.Attribute.Relation<'oneToOne', 'admin::role'> &
      Schema.Attribute.Required;
    can_manage_admin_users: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::admin-user-role-extention.admin-user-role-extention'
    > &
      Schema.Attribute.Private;
    manageable_admin_roles: Schema.Attribute.Relation<
      'oneToMany',
      'admin::role'
    >;
    manageable_user_roles: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.role'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBpAddrDepdntIntlLocNumberBpAddrDepdntIntlLocNumber
  extends Struct.CollectionTypeSchema {
  collectionName: 'bp_addr_depdnt_intl_loc_numbers';
  info: {
    displayName: 'Business Partner Address-Dependent International Location Number (ILN)';
    pluralName: 'bp-addr-depdnt-intl-loc-numbers';
    singularName: 'bp-addr-depdnt-intl-loc-number';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    bp_address_id: Schema.Attribute.String & Schema.Attribute.Required;
    bp_id: Schema.Attribute.String & Schema.Attribute.Required;
    business_partner: Schema.Attribute.Relation<
      'oneToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_address: Schema.Attribute.Relation<
      'oneToOne',
      'api::business-partner-address.business-partner-address'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    international_location_number_1: Schema.Attribute.String;
    international_location_number_2: Schema.Attribute.String;
    international_location_number_3: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBpAddressUsageBpAddressUsage
  extends Struct.CollectionTypeSchema {
  collectionName: 'bp_address_usages';
  info: {
    displayName: 'Business Partner Address Usage';
    pluralName: 'bp-address-usages';
    singularName: 'bp-address-usage';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    address_usage: Schema.Attribute.String & Schema.Attribute.Required;
    authorization_group: Schema.Attribute.String;
    bp_address_id: Schema.Attribute.String & Schema.Attribute.Required;
    bp_id: Schema.Attribute.String & Schema.Attribute.Required;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_address: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner-address.business-partner-address'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-address-usage.bp-address-usage'
    >;
    publishedAt: Schema.Attribute.DateTime;
    standard_usage: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    validity_end_date: Schema.Attribute.DateTime & Schema.Attribute.Required;
    validity_start_date: Schema.Attribute.DateTime;
  };
}

export interface ApiBpContactStagingBpContactStaging
  extends Struct.CollectionTypeSchema {
  collectionName: 'bp_contact_stagings';
  info: {
    displayName: 'Business Partner Contact Staging';
    pluralName: 'bp-contact-stagings';
    singularName: 'bp-contact-staging';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    data: Schema.Attribute.JSON & Schema.Attribute.Required;
    error_message: Schema.Attribute.Text;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-contact-staging.bp-contact-staging'
    >;
    publishedAt: Schema.Attribute.DateTime;
    staging_status: Schema.Attribute.Enumeration<
      ['PENDING', 'IN_PROCESS', 'FAILED']
    > &
      Schema.Attribute.DefaultTo<'PENDING'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBpContactToAddressBpContactToAddress
  extends Struct.CollectionTypeSchema {
  collectionName: 'bp_contact_to_addresses';
  info: {
    displayName: 'Business Partner Contact To Address';
    pluralName: 'bp-contact-to-addresses';
    singularName: 'bp-contact-to-address';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    additional_street_prefix_name: Schema.Attribute.String;
    additional_street_suffix_name: Schema.Attribute.String;
    address_number: Schema.Attribute.String;
    address_representation_code: Schema.Attribute.String;
    address_time_zone: Schema.Attribute.String;
    bp_company_id: Schema.Attribute.String & Schema.Attribute.Required;
    bp_contact_address_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    bp_person_id: Schema.Attribute.String & Schema.Attribute.Required;
    business_partner_company: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_person: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    care_of_name: Schema.Attribute.String;
    city_code: Schema.Attribute.String;
    city_name: Schema.Attribute.String;
    company_postal_code: Schema.Attribute.String;
    contact_company_address: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner-contact.business-partner-contact'
    >;
    contact_person_address: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner-contact.business-partner-contact'
    >;
    contact_person_building: Schema.Attribute.String;
    contact_person_prfrd_comm_medium: Schema.Attribute.String;
    contact_relationship_department: Schema.Attribute.String;
    contact_relationship_function: Schema.Attribute.String;
    correspondence_short_name: Schema.Attribute.String;
    country: Schema.Attribute.String;
    county: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    delivery_service_number: Schema.Attribute.String;
    delivery_service_type_code: Schema.Attribute.String;
    district: Schema.Attribute.String;
    emails: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-email-address.bp-email-address'
    >;
    fax_numbers: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-fax-number.bp-fax-number'
    >;
    floor: Schema.Attribute.String;
    form_of_address: Schema.Attribute.String;
    full_name: Schema.Attribute.String;
    home_city_name: Schema.Attribute.String;
    home_page_urls: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-home-page-url.bp-home-page-url'
    >;
    house_number: Schema.Attribute.String;
    house_number_supplement_text: Schema.Attribute.String;
    inhouse_mail: Schema.Attribute.String;
    language: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-contact-to-address.bp-contact-to-address'
    >;
    person: Schema.Attribute.String;
    phone_numbers: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-phone-number.bp-phone-number'
    >;
    po_box: Schema.Attribute.String;
    po_box_deviating_city_name: Schema.Attribute.String;
    po_box_deviating_country: Schema.Attribute.String;
    po_box_deviating_region: Schema.Attribute.String;
    po_box_is_without_number: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    po_box_lobby_name: Schema.Attribute.String;
    po_box_postal_code: Schema.Attribute.String;
    postal_code: Schema.Attribute.String;
    prfrd_comm_medium_type: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    region: Schema.Attribute.String;
    relationship: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner-relationship.business-partner-relationship'
    >;
    relationship_number: Schema.Attribute.String & Schema.Attribute.Required;
    room_number: Schema.Attribute.String;
    street_name: Schema.Attribute.String;
    street_prefix_name: Schema.Attribute.String;
    street_suffix_name: Schema.Attribute.String;
    tax_jurisdiction: Schema.Attribute.String;
    transport_zone: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    validity_end_date: Schema.Attribute.DateTime & Schema.Attribute.Required;
  };
}

export interface ApiBpContactToFuncAndDeptBpContactToFuncAndDept
  extends Struct.CollectionTypeSchema {
  collectionName: 'bp_contact_to_func_and_depts';
  info: {
    displayName: 'Business Partner Contact Person Function and Department';
    pluralName: 'bp-contact-to-func-and-depts';
    singularName: 'bp-contact-to-func-and-dept';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    bp_company_id: Schema.Attribute.String & Schema.Attribute.Required;
    bp_person_id: Schema.Attribute.String & Schema.Attribute.Required;
    business_partner_company: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_person: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    contact_company: Schema.Attribute.Relation<
      'oneToOne',
      'api::business-partner-contact.business-partner-contact'
    >;
    contact_person: Schema.Attribute.Relation<
      'oneToOne',
      'api::business-partner-contact.business-partner-contact'
    >;
    contact_person_authority_type: Schema.Attribute.String;
    contact_person_department: Schema.Attribute.String;
    contact_person_department_name: Schema.Attribute.String;
    contact_person_function: Schema.Attribute.String;
    contact_person_function_name: Schema.Attribute.String;
    contact_person_remark_text: Schema.Attribute.String;
    contact_person_vip_type: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email_address: Schema.Attribute.String;
    fax_number: Schema.Attribute.String;
    fax_number_extension: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept'
    >;
    phone_number: Schema.Attribute.String;
    phone_number_extension: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    relationship: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner-relationship.business-partner-relationship'
    >;
    relationship_category: Schema.Attribute.String;
    relationship_number: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    validity_end_date: Schema.Attribute.DateTime & Schema.Attribute.Required;
  };
}

export interface ApiBpCreditWorthinessBpCreditWorthiness
  extends Struct.CollectionTypeSchema {
  collectionName: 'bp_credit_worthinesses';
  info: {
    displayName: 'Business Partner Credit Worthiness';
    pluralName: 'bp-credit-worthinesses';
    singularName: 'bp-credit-worthiness';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    bp_crdt_wrthnss_access_chk_is_active: Schema.Attribute.Boolean;
    bp_credit_standing_comment: Schema.Attribute.Text;
    bp_credit_standing_date: Schema.Attribute.DateTime;
    bp_credit_standing_rating: Schema.Attribute.String;
    bp_credit_standing_status: Schema.Attribute.String;
    bp_foreclosure_date: Schema.Attribute.DateTime;
    bp_foreclosure_is_initiated: Schema.Attribute.Boolean;
    bp_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    bp_legal_proceeding_status: Schema.Attribute.String;
    bp_lgl_proceeding_initiation_date: Schema.Attribute.DateTime;
    bus_part_credit_standing: Schema.Attribute.String;
    business_partner: Schema.Attribute.Relation<
      'oneToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_bankruptcy_date: Schema.Attribute.DateTime;
    business_partner_is_bankrupt: Schema.Attribute.Boolean;
    business_partner_is_under_oath: Schema.Attribute.Boolean;
    business_partner_oath_date: Schema.Attribute.DateTime;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    credit_rating_agency: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-credit-worthiness.bp-credit-worthiness'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBpEmailAddressBpEmailAddress
  extends Struct.CollectionTypeSchema {
  collectionName: 'bp_email_addresses';
  info: {
    displayName: 'Business Partner Email Address';
    pluralName: 'bp-email-addresses';
    singularName: 'bp-email-address';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    address_communication_remark_text: Schema.Attribute.Text;
    address_id: Schema.Attribute.String & Schema.Attribute.Required;
    bp_contact_address: Schema.Attribute.Relation<
      'manyToOne',
      'api::bp-contact-to-address.bp-contact-to-address'
    >;
    business_partner_address: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner-address.business-partner-address'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email_address: Schema.Attribute.Email;
    is_default_email_address: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-email-address.bp-email-address'
    >;
    ordinal_number: Schema.Attribute.Integer & Schema.Attribute.Required;
    person: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    search_email_address: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBpFaxNumberBpFaxNumber extends Struct.CollectionTypeSchema {
  collectionName: 'bp_fax_numbers';
  info: {
    displayName: 'Business Partner Fax Number';
    pluralName: 'bp-fax-numbers';
    singularName: 'bp-fax-number';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    address_communication_remark_text: Schema.Attribute.Text;
    address_id: Schema.Attribute.String & Schema.Attribute.Required;
    bp_contact_address: Schema.Attribute.Relation<
      'manyToOne',
      'api::bp-contact-to-address.bp-contact-to-address'
    >;
    business_partner_address: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner-address.business-partner-address'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    fax_country: Schema.Attribute.String;
    fax_number: Schema.Attribute.String;
    fax_number_extension: Schema.Attribute.String;
    international_fax_number: Schema.Attribute.String;
    is_default_fax_number: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-fax-number.bp-fax-number'
    >;
    ordinal_number: Schema.Attribute.Integer & Schema.Attribute.Required;
    person: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBpHomePageUrlBpHomePageUrl
  extends Struct.CollectionTypeSchema {
  collectionName: 'bp_home_page_urls';
  info: {
    displayName: 'Business Partner Home Page URL';
    pluralName: 'bp-home-page-urls';
    singularName: 'bp-home-page-url';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    address_communication_remark_text: Schema.Attribute.Text;
    address_id: Schema.Attribute.String & Schema.Attribute.Required;
    bp_contact_address: Schema.Attribute.Relation<
      'manyToOne',
      'api::bp-contact-to-address.bp-contact-to-address'
    >;
    business_partner_address: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner-address.business-partner-address'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    is_default_url_address: Schema.Attribute.Boolean &
      Schema.Attribute.Required;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-home-page-url.bp-home-page-url'
    >;
    ordinal_number: Schema.Attribute.Integer & Schema.Attribute.Required;
    person: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    search_url_address: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    url_field_length: Schema.Attribute.Integer;
    validity_start_date: Schema.Attribute.DateTime;
    website_url: Schema.Attribute.String;
  };
}

export interface ApiBpIntlAddressVersionBpIntlAddressVersion
  extends Struct.CollectionTypeSchema {
  collectionName: 'bp_intl_address_versions';
  info: {
    displayName: 'Business Partner International Address Version';
    pluralName: 'bp-intl-address-versions';
    singularName: 'bp-intl-address-version';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    address_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    address_id_by_external_system: Schema.Attribute.String;
    address_person_id: Schema.Attribute.String;
    address_representation_code: Schema.Attribute.String &
      Schema.Attribute.Required;
    address_search_term1: Schema.Attribute.String;
    address_search_term2: Schema.Attribute.String;
    address_time_zone: Schema.Attribute.String;
    addressee_full_name: Schema.Attribute.String;
    bp_id: Schema.Attribute.String & Schema.Attribute.Required;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    care_of_name: Schema.Attribute.String;
    city_name: Schema.Attribute.String;
    city_number: Schema.Attribute.String;
    company_postal_code: Schema.Attribute.String;
    country: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    delivery_service_number: Schema.Attribute.String;
    delivery_service_type_code: Schema.Attribute.String;
    district_name: Schema.Attribute.String;
    form_of_address: Schema.Attribute.String;
    house_number: Schema.Attribute.String;
    house_number_supplement_text: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-intl-address-version.bp-intl-address-version'
    >;
    organization_name1: Schema.Attribute.String;
    organization_name2: Schema.Attribute.String;
    organization_name3: Schema.Attribute.String;
    organization_name4: Schema.Attribute.String;
    person_family_name: Schema.Attribute.String;
    person_given_name: Schema.Attribute.String;
    po_box: Schema.Attribute.String;
    po_box_deviating_city_name: Schema.Attribute.String;
    po_box_deviating_country: Schema.Attribute.String;
    po_box_deviating_region: Schema.Attribute.String;
    po_box_is_without_number: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    po_box_lobby_name: Schema.Attribute.String;
    po_box_postal_code: Schema.Attribute.String;
    postal_code: Schema.Attribute.String;
    prfrd_comm_medium_type: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    region: Schema.Attribute.String;
    secondary_region: Schema.Attribute.String;
    secondary_region_name: Schema.Attribute.String;
    street_name: Schema.Attribute.String;
    street_prefix_name1: Schema.Attribute.String;
    street_prefix_name2: Schema.Attribute.String;
    street_suffix_name1: Schema.Attribute.String;
    street_suffix_name2: Schema.Attribute.String;
    tax_jurisdiction: Schema.Attribute.String;
    tertiary_region: Schema.Attribute.String;
    tertiary_region_name: Schema.Attribute.String;
    transport_zone: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    village_name: Schema.Attribute.String;
  };
}

export interface ApiBpMarketingAttributeBpMarketingAttribute
  extends Struct.CollectionTypeSchema {
  collectionName: 'bp_marketing_attributes';
  info: {
    displayName: 'Business Partner Marketing Attribute';
    pluralName: 'bp-marketing-attributes';
    singularName: 'bp-marketing-attribute';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    bp_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    business_partner: Schema.Attribute.Relation<
      'oneToOne',
      'api::business-partner.business-partner'
    >;
    conference_room: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    date_opened: Schema.Attribute.Date;
    fitness_center: Schema.Attribute.String;
    gym: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-marketing-attribute.bp-marketing-attribute'
    >;
    pool: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    renovation_date: Schema.Attribute.Date;
    restaurant: Schema.Attribute.String;
    seasonal_close_date: Schema.Attribute.String;
    seasonal_open_date: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBpPhoneNumberBpPhoneNumber
  extends Struct.CollectionTypeSchema {
  collectionName: 'bp_phone_numbers';
  info: {
    displayName: 'Business Partner Phone Number';
    pluralName: 'bp-phone-numbers';
    singularName: 'bp-phone-number';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    address_communication_remark_text: Schema.Attribute.Text;
    address_id: Schema.Attribute.String & Schema.Attribute.Required;
    bp_contact_address: Schema.Attribute.Relation<
      'manyToOne',
      'api::bp-contact-to-address.bp-contact-to-address'
    >;
    business_partner_address: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner-address.business-partner-address'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    destination_location_country: Schema.Attribute.String;
    international_phone_number: Schema.Attribute.String;
    is_default_phone_number: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-phone-number.bp-phone-number'
    >;
    ordinal_number: Schema.Attribute.Integer & Schema.Attribute.Required;
    person: Schema.Attribute.String & Schema.Attribute.Required;
    phone_number: Schema.Attribute.String;
    phone_number_extension: Schema.Attribute.String;
    phone_number_type: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBusinessPartnerAddressBusinessPartnerAddress
  extends Struct.CollectionTypeSchema {
  collectionName: 'business_partner_addresses';
  info: {
    description: '';
    displayName: 'Business Partner Address';
    pluralName: 'business-partner-addresses';
    singularName: 'business-partner-address';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    additional_street_prefix_name: Schema.Attribute.String;
    additional_street_suffix_name: Schema.Attribute.String;
    address_id_by_external_system: Schema.Attribute.String;
    address_time_zone: Schema.Attribute.String;
    address_usages: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-address-usage.bp-address-usage'
    >;
    authorization_group: Schema.Attribute.String;
    bp_address_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    bp_id: Schema.Attribute.String & Schema.Attribute.Required;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    care_of_name: Schema.Attribute.String;
    city_code: Schema.Attribute.String;
    city_name: Schema.Attribute.String;
    company_postal_code: Schema.Attribute.String;
    country: Schema.Attribute.String;
    county: Schema.Attribute.String;
    county_code: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    delivery_service_number: Schema.Attribute.String;
    delivery_service_type_code: Schema.Attribute.String;
    district: Schema.Attribute.String;
    emails: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-email-address.bp-email-address'
    >;
    fax_numbers: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-fax-number.bp-fax-number'
    >;
    form_of_address: Schema.Attribute.String;
    full_name: Schema.Attribute.String;
    home_city_name: Schema.Attribute.String;
    home_page_urls: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-home-page-url.bp-home-page-url'
    >;
    house_number: Schema.Attribute.String;
    house_number_supplement_text: Schema.Attribute.String;
    intl_loc_number: Schema.Attribute.Relation<
      'oneToOne',
      'api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number'
    >;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-address.business-partner-address'
    >;
    phone_numbers: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-phone-number.bp-phone-number'
    >;
    po_box: Schema.Attribute.String;
    po_box_deviating_city_name: Schema.Attribute.String;
    po_box_deviating_country: Schema.Attribute.String;
    po_box_deviating_region: Schema.Attribute.String;
    po_box_is_without_number: Schema.Attribute.Boolean;
    po_box_lobby_name: Schema.Attribute.String;
    po_box_postal_code: Schema.Attribute.String;
    postal_code: Schema.Attribute.String;
    prfrd_comm_medium_type: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    region: Schema.Attribute.String;
    street_name: Schema.Attribute.String;
    township_code: Schema.Attribute.String;
    township_name: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    uuid: Schema.Attribute.String;
    validity_end_date: Schema.Attribute.DateTime;
    validity_start_date: Schema.Attribute.DateTime;
  };
}

export interface ApiBusinessPartnerBankBusinessPartnerBank
  extends Struct.CollectionTypeSchema {
  collectionName: 'business_partner_banks';
  info: {
    displayName: 'Business Partner Bank';
    pluralName: 'business-partner-banks';
    singularName: 'business-partner-bank';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    bank_account: Schema.Attribute.String;
    bank_account_holder_name: Schema.Attribute.String;
    bank_account_name: Schema.Attribute.String;
    bank_account_reference_text: Schema.Attribute.String;
    bank_control_key: Schema.Attribute.String;
    bank_country_key: Schema.Attribute.String;
    bank_identification: Schema.Attribute.String & Schema.Attribute.Required;
    bank_name: Schema.Attribute.String;
    bank_number: Schema.Attribute.String;
    bp_id: Schema.Attribute.String & Schema.Attribute.Required;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    city_name: Schema.Attribute.String;
    collection_auth_ind: Schema.Attribute.Boolean;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    iban: Schema.Attribute.String;
    iban_validity_start_date: Schema.Attribute.DateTime;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-bank.business-partner-bank'
    >;
    publishedAt: Schema.Attribute.DateTime;
    swift_code: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    validity_end_date: Schema.Attribute.DateTime;
    validity_start_date: Schema.Attribute.DateTime;
  };
}

export interface ApiBusinessPartnerContactBusinessPartnerContact
  extends Struct.CollectionTypeSchema {
  collectionName: 'business_partner_contacts';
  info: {
    displayName: 'Business Partner Contact';
    pluralName: 'business-partner-contacts';
    singularName: 'business-partner-contact';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    bp_company_id: Schema.Attribute.String & Schema.Attribute.Required;
    bp_person_id: Schema.Attribute.String & Schema.Attribute.Required;
    business_partner_company: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_person: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    company_addresses: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-contact-to-address.bp-contact-to-address'
    >;
    company_func_and_dept: Schema.Attribute.Relation<
      'oneToOne',
      'api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    is_standard_relationship: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-contact.business-partner-contact'
    >;
    person_addresses: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-contact-to-address.bp-contact-to-address'
    >;
    person_func_and_dept: Schema.Attribute.Relation<
      'oneToOne',
      'api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept'
    >;
    publishedAt: Schema.Attribute.DateTime;
    relationship: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner-relationship.business-partner-relationship'
    >;
    relationship_category: Schema.Attribute.String;
    relationship_number: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    validity_end_date: Schema.Attribute.DateTime & Schema.Attribute.Required;
    validity_start_date: Schema.Attribute.DateTime;
  };
}

export interface ApiBusinessPartnerExtensionBusinessPartnerExtension
  extends Struct.CollectionTypeSchema {
  collectionName: 'business_partner_extensions';
  info: {
    displayName: 'Business Partner Extension';
    pluralName: 'business-partner-extensions';
    singularName: 'business-partner-extension';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    admin_user: Schema.Attribute.Boolean;
    approver: Schema.Attribute.Boolean;
    best_reached_by: Schema.Attribute.String;
    bp_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    business_department: Schema.Attribute.String;
    business_partner: Schema.Attribute.Relation<
      'oneToOne',
      'api::business-partner.business-partner'
    >;
    buying_guide_opt_in: Schema.Attribute.Boolean;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    email_optcode_chg_ind: Schema.Attribute.Boolean;
    emails_opt_in: Schema.Attribute.Boolean;
    hs_update_flag: Schema.Attribute.String;
    hubspot_chg_flag: Schema.Attribute.Boolean;
    job_title: Schema.Attribute.String;
    last_login: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-extension.business-partner-extension'
    >;
    native_language: Schema.Attribute.String;
    print_marketing_opt_in: Schema.Attribute.Boolean;
    publishedAt: Schema.Attribute.DateTime;
    punch_out_user: Schema.Attribute.Boolean;
    purchasing_control: Schema.Attribute.String;
    sms_promotions_opt_in: Schema.Attribute.Boolean;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    web_registered: Schema.Attribute.Boolean;
    web_user_id: Schema.Attribute.String;
  };
}

export interface ApiBusinessPartnerIdentificationBusinessPartnerIdentification
  extends Struct.CollectionTypeSchema {
  collectionName: 'business_partner_identifications';
  info: {
    displayName: 'Business Partner Identification';
    pluralName: 'business-partner-identifications';
    singularName: 'business-partner-identification';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    authorization_group: Schema.Attribute.String;
    bp_id: Schema.Attribute.String & Schema.Attribute.Required;
    bp_identification_entry_date: Schema.Attribute.DateTime;
    bp_identification_number: Schema.Attribute.String;
    bp_identification_type: Schema.Attribute.String;
    bp_idn_nmbr_issuing_institute: Schema.Attribute.String;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    country: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-identification.business-partner-identification'
    >;
    publishedAt: Schema.Attribute.DateTime;
    region: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    validity_end_date: Schema.Attribute.DateTime;
    validity_start_date: Schema.Attribute.DateTime;
  };
}

export interface ApiBusinessPartnerPaymentCardBusinessPartnerPaymentCard
  extends Struct.CollectionTypeSchema {
  collectionName: 'business_partner_payment_cards';
  info: {
    displayName: 'Business Partner Payment Card';
    pluralName: 'business-partner-payment-cards';
    singularName: 'business-partner-payment-card';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    bp_id: Schema.Attribute.String & Schema.Attribute.Required;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    card_description: Schema.Attribute.String;
    card_holder: Schema.Attribute.String;
    card_issue_date: Schema.Attribute.DateTime;
    card_issuing_bank: Schema.Attribute.String;
    card_number: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    is_standard_card: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-payment-card.business-partner-payment-card'
    >;
    masked_card_number: Schema.Attribute.String;
    payment_card_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    payment_card_lock: Schema.Attribute.String;
    payment_card_type: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    validity_date: Schema.Attribute.DateTime;
    validity_end_date: Schema.Attribute.DateTime;
  };
}

export interface ApiBusinessPartnerRelationshipBusinessPartnerRelationship
  extends Struct.CollectionTypeSchema {
  collectionName: 'business_partner_relationships';
  info: {
    displayName: 'Business Partner Relationship';
    pluralName: 'business-partner-relationships';
    singularName: 'business-partner-relationship';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    bp_contact_to_address_relationships: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-contact-to-address.bp-contact-to-address'
    >;
    bp_id1: Schema.Attribute.String & Schema.Attribute.Required;
    bp_id2: Schema.Attribute.String & Schema.Attribute.Required;
    bp_relationship_type: Schema.Attribute.String;
    business_partner1: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner2: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    contact_func_and_dept_relationships: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept'
    >;
    contact_relationships: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-contact.business-partner-contact'
    >;
    created_by_user: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    creation_date: Schema.Attribute.DateTime;
    creation_time: Schema.Attribute.DateTime;
    is_standard_relationship: Schema.Attribute.Boolean;
    last_change_date: Schema.Attribute.DateTime;
    last_change_time: Schema.Attribute.DateTime;
    last_changed_by_user: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-relationship.business-partner-relationship'
    >;
    publishedAt: Schema.Attribute.DateTime;
    relationship_category: Schema.Attribute.String;
    relationship_number: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    validity_end_date: Schema.Attribute.DateTime & Schema.Attribute.Required;
    validity_start_date: Schema.Attribute.DateTime;
  };
}

export interface ApiBusinessPartnerRoleBusinessPartnerRole
  extends Struct.CollectionTypeSchema {
  collectionName: 'business_partner_roles';
  info: {
    displayName: 'Business Partner Role';
    pluralName: 'business-partner-roles';
    singularName: 'business-partner-role';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    authorization_group: Schema.Attribute.String;
    bp_id: Schema.Attribute.String & Schema.Attribute.Required;
    bp_role: Schema.Attribute.String & Schema.Attribute.Required;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-role.business-partner-role'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    valid_from: Schema.Attribute.DateTime;
    valid_to: Schema.Attribute.DateTime;
  };
}

export interface ApiBusinessPartnerStagingBusinessPartnerStaging
  extends Struct.CollectionTypeSchema {
  collectionName: 'business_partner_stagings';
  info: {
    displayName: 'Business Partner Staging';
    pluralName: 'business-partner-stagings';
    singularName: 'business-partner-staging';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    data: Schema.Attribute.JSON & Schema.Attribute.Required;
    error_message: Schema.Attribute.Text;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-staging.business-partner-staging'
    >;
    publishedAt: Schema.Attribute.DateTime;
    staging_status: Schema.Attribute.Enumeration<
      ['PENDING', 'IN_PROCESS', 'FAILED']
    > &
      Schema.Attribute.DefaultTo<'PENDING'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiBusinessPartnerBusinessPartner
  extends Struct.CollectionTypeSchema {
  collectionName: 'business_partners';
  info: {
    description: '';
    displayName: 'Business Partner';
    pluralName: 'business-partners';
    singularName: 'business-partner';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    activities: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-activity.crm-activity'
    >;
    address_usages: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-address-usage.bp-address-usage'
    >;
    addresses: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-address.business-partner-address'
    >;
    banks: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-bank.business-partner-bank'
    >;
    bp_attachments: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-attachment.crm-attachment'
    >;
    bp_category: Schema.Attribute.String;
    bp_data_controller_is_not_required: Schema.Attribute.Boolean;
    bp_extension: Schema.Attribute.Relation<
      'oneToOne',
      'api::business-partner-extension.business-partner-extension'
    >;
    bp_full_name: Schema.Attribute.String;
    bp_grouping: Schema.Attribute.String;
    bp_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    bp_intl_address_versions: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-intl-address-version.bp-intl-address-version'
    >;
    bp_uuid: Schema.Attribute.String;
    bus_part_marital_status: Schema.Attribute.String;
    bus_part_nationality: Schema.Attribute.String;
    business_partner_birth_name: Schema.Attribute.String;
    business_partner_id_by_ext_system: Schema.Attribute.String;
    business_partner_occupation: Schema.Attribute.String;
    business_partner_print_format: Schema.Attribute.String;
    business_partner_supplement_name: Schema.Attribute.String;
    cb_memberships: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-customer-business.fg-customer-business'
    >;
    contact_activities: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-activity.crm-activity'
    >;
    contact_companies: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-contact.business-partner-contact'
    >;
    contact_company_addresses: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-contact-to-address.bp-contact-to-address'
    >;
    contact_company_func_and_depts: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept'
    >;
    contact_opportunities: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity.crm-opportunity'
    >;
    contact_person_addresses: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-contact-to-address.bp-contact-to-address'
    >;
    contact_person_func_and_depts: Schema.Attribute.Relation<
      'oneToMany',
      'api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept'
    >;
    contact_persons: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-contact.business-partner-contact'
    >;
    correspondence_language: Schema.Attribute.String;
    created_by_user: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    creation_date: Schema.Attribute.DateTime;
    creation_time: Schema.Attribute.DateTime;
    credit_worthiness: Schema.Attribute.Relation<
      'oneToOne',
      'api::bp-credit-worthiness.bp-credit-worthiness'
    >;
    crm_competitor_products: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-competitor-product.crm-competitor-product'
    >;
    crm_org_unit_employees: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit-employee.crm-organisational-unit-employee'
    >;
    crm_org_unit_managers: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit-manager.crm-organisational-unit-manager'
    >;
    customer: Schema.Attribute.Relation<'oneToOne', 'api::customer.customer'>;
    customer_businesses: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-customer-business.fg-customer-business'
    >;
    customer_internals: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-customer-internal.fg-customer-internal'
    >;
    employee_activities: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-activity.crm-activity'
    >;
    etag: Schema.Attribute.String;
    first_name: Schema.Attribute.String;
    form_of_address: Schema.Attribute.String;
    gender_code_name: Schema.Attribute.String;
    group_business_partner_name1: Schema.Attribute.String;
    group_business_partner_name2: Schema.Attribute.String;
    identifications: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-identification.business-partner-identification'
    >;
    independent_address_id: Schema.Attribute.String;
    industry: Schema.Attribute.String;
    initials: Schema.Attribute.String;
    international_location_number1: Schema.Attribute.String;
    international_location_number2: Schema.Attribute.String;
    international_location_number3: Schema.Attribute.String;
    intl_loc_number: Schema.Attribute.Relation<
      'oneToOne',
      'api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number'
    >;
    involved_parties: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-involved-party.crm-involved-party'
    >;
    is_female: Schema.Attribute.Boolean;
    is_male: Schema.Attribute.Boolean;
    is_marked_for_archiving: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    is_natural_person: Schema.Attribute.Boolean;
    is_sex_unknown: Schema.Attribute.Boolean;
    language: Schema.Attribute.String;
    last_change_date: Schema.Attribute.DateTime;
    last_change_time: Schema.Attribute.DateTime;
    last_changed_by_user: Schema.Attribute.String;
    last_name: Schema.Attribute.String;
    last_name_prefix: Schema.Attribute.String;
    last_name_second_prefix: Schema.Attribute.String;
    legal_form: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner.business-partner'
    >;
    marketing_attributes: Schema.Attribute.Relation<
      'oneToOne',
      'api::bp-marketing-attribute.bp-marketing-attribute'
    >;
    middle_name: Schema.Attribute.String;
    name_country: Schema.Attribute.String;
    name_format: Schema.Attribute.String;
    natural_person_employer_name: Schema.Attribute.String;
    notes: Schema.Attribute.Relation<'oneToMany', 'api::crm-note.crm-note'>;
    opportunities: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity.crm-opportunity'
    >;
    opportunity_contact_parties: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity-party-contact-party.crm-opportunity-party-contact-party'
    >;
    opportunity_parties: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity-party.crm-opportunity-party'
    >;
    opportunity_sales_team_parties: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity-sales-team-party.crm-opportunity-sales-team-party'
    >;
    org_bp_name1: Schema.Attribute.String;
    org_bp_name2: Schema.Attribute.String;
    org_bp_name3: Schema.Attribute.String;
    org_bp_name4: Schema.Attribute.String;
    organization_foundation_date: Schema.Attribute.DateTime;
    organization_liquidation_date: Schema.Attribute.DateTime;
    organizer_activities: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-activity.crm-activity'
    >;
    owner_activities: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-activity.crm-activity'
    >;
    owner_opportunities: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity.crm-opportunity'
    >;
    partner_functions: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-partner-function.customer-partner-function'
    >;
    payment_cards: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-payment-card.business-partner-payment-card'
    >;
    person_full_name: Schema.Attribute.String;
    person_number: Schema.Attribute.String;
    processor_activities: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-activity.crm-activity'
    >;
    product_businesses: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-product-business.fg-product-business'
    >;
    publishedAt: Schema.Attribute.DateTime;
    relationships1: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-relationship.business-partner-relationship'
    >;
    relationships2: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-relationship.business-partner-relationship'
    >;
    roles: Schema.Attribute.Relation<
      'oneToMany',
      'api::business-partner-role.business-partner-role'
    >;
    search_term1: Schema.Attribute.String;
    search_term2: Schema.Attribute.String;
    supplier: Schema.Attribute.Relation<'oneToOne', 'api::supplier.supplier'>;
    trading_partner: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    user_vendors: Schema.Attribute.Relation<
      'oneToMany',
      'api::user-vendor.user-vendor'
    >;
  };
}

export interface ApiCartItemCartItem extends Struct.CollectionTypeSchema {
  collectionName: 'cart_items';
  info: {
    displayName: 'Cart Item';
    pluralName: 'cart-items';
    singularName: 'cart-item';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    cart: Schema.Attribute.Relation<'manyToOne', 'api::cart.cart'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::cart-item.cart-item'
    >;
    material: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    requested_quantity: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<1>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCartReserveItemCartReserveItem
  extends Struct.CollectionTypeSchema {
  collectionName: 'cart_reserve_items';
  info: {
    displayName: 'Cart Reserve Item';
    pluralName: 'cart-reserve-items';
    singularName: 'cart-reserve-item';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    cart: Schema.Attribute.Relation<
      'manyToOne',
      'api::cart-reserve.cart-reserve'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::cart-reserve-item.cart-reserve-item'
    >;
    material: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    requested_quantity: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<1>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCartReserveCartReserve extends Struct.CollectionTypeSchema {
  collectionName: 'cart_reserves';
  info: {
    displayName: 'Cart Reserve';
    pluralName: 'cart-reserves';
    singularName: 'cart-reserve';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    admin_user: Schema.Attribute.Relation<'manyToOne', 'admin::user'>;
    cart_items: Schema.Attribute.Relation<
      'oneToMany',
      'api::cart-reserve-item.cart-reserve-item'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customer: Schema.Attribute.Relation<'manyToOne', 'api::customer.customer'>;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::cart-reserve.cart-reserve'
    >;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    user: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.user'
    >;
  };
}

export interface ApiCartCart extends Struct.CollectionTypeSchema {
  collectionName: 'carts';
  info: {
    displayName: 'Cart';
    pluralName: 'carts';
    singularName: 'cart';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    admin_user: Schema.Attribute.Relation<'oneToOne', 'admin::user'>;
    cart_items: Schema.Attribute.Relation<
      'oneToMany',
      'api::cart-item.cart-item'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customer: Schema.Attribute.Relation<'manyToOne', 'api::customer.customer'>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<'oneToMany', 'api::cart.cart'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    user: Schema.Attribute.Relation<
      'oneToOne',
      'plugin::users-permissions.user'
    >;
  };
}

export interface ApiConfigurationConfiguration
  extends Struct.CollectionTypeSchema {
  collectionName: 'configurations';
  info: {
    displayName: 'Configuration';
    pluralName: 'configurations';
    singularName: 'configuration';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    code: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    is_active: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::configuration.configuration'
    >;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.Enumeration<
      [
        'CUSTOMER_TEXT_DESC',
        'INVOICE_FORM_TYPE',
        'INVOICE_STATUS',
        'INVOICE_TYPE',
        'NOTIFICATION',
        'ORDER_STATUS',
        'ORDER_TYPE',
        'QUOTE_STATUS',
        'RETURN_REASON',
        'RETURN_REFUND_PROGRESS',
        'RETURN_STATUS',
        'TICKET_STATUS',
        'PAYMENT_TERMS',
        'INCOTERMS',
        'PAYMENT_METHOD',
        'FUNCTION_CP',
        'VIP',
        'CP_AUTHORITY',
        'CP_DEPARTMENTS',
        'PARTNER_FUNCTIONS',
        'CRM_ACTIVITY_STATUS',
        'CRM_ACTIVITY_PRIORITY',
        'CRM_ACTIVITY_INITIATOR_CODE',
        'CRM_ACTIVITY_DOCUMENT_TYPE',
        'CRM_ACTIVITY_DOC_TYPE_PHONE_CALL',
        'CRM_ACTIVITY_DOC_TYPE_FOLLOWUP_RELATED_ITEM',
        'CRM_ACTIVITY_PHONE_CALL_CATEGORY',
        'CRM_ACTIVITY_TASK_CATEGORY',
        'CRM_ACTIVITY_APPOINTMENT_CATEGORY',
        'CRM_ACTIVITY_DISPOSITION_CODE',
        'CRM_OPPORTUNITY_STATUS',
        'CRM_OPPORTUNITY_ORIGIN_TYPE',
        'CRM_OPPORTUNITY_GROUP',
        'CRM_PURCHASING_CTRL',
        'CRM_NATIVE_LANG',
        'BPMA_STR_CHAIN_SCALE',
        'BPMA_SIZE_UNIT',
        'PRFRD_COMM_MEDIUM_TYPE',
        'FLEX_GROUP_TYPE',
      ]
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    usage: Schema.Attribute.Enumeration<
      ['CUSTOMER', 'VENDOR', 'CONTACT_PERSON', 'EMPLOYEE', 'CRM']
    >;
  };
}

export interface ApiContentCrmContentCrm extends Struct.CollectionTypeSchema {
  collectionName: 'content_crms';
  info: {
    description: '';
    displayName: 'Content CRM';
    pluralName: 'content-crms';
    singularName: 'content-crm';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    body: Schema.Attribute.DynamicZone<['crm.logo', 'crm.media']>;
    content_name: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    i18n: Schema.Attribute.JSON;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::content-crm.content-crm'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    slug: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiContentEcomContentEcom extends Struct.CollectionTypeSchema {
  collectionName: 'content_ecoms';
  info: {
    description: '';
    displayName: 'Content ECOM';
    pluralName: 'content-ecoms';
    singularName: 'content-ecom';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    body: Schema.Attribute.DynamicZone<
      [
        'ecom.customer-service-categories',
        'ecom.popular-questions',
        'vendor.logo',
        'ecom.menu',
        'ecom.header-important-note',
        'ecom.banner',
        'ecom.popular-categories',
        'ecom.offer',
        'ecom.hospitality',
        'ecom.featured-brands',
        'ecom.footer',
        'ecom.shop-by-categoried',
        'ecom.portal-features',
        'ecom.image-with-title-desc-link',
        'ecom.video',
        'ecom.media-center',
        'ecom.contacts-detail',
        'ecom.sustainability',
        'ecom.who-we-are',
        'ecom.dedicated-delivery',
        'ecom.terms-and-conditions',
        'ecom.privacy-and-security',
        'ecom.catalogs',
        'ecom.projects',
      ]
    >;
    content_name: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    i18n: Schema.Attribute.JSON;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::content-ecom.content-ecom'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    slug: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiContentPimContentPim extends Struct.CollectionTypeSchema {
  collectionName: 'content_pims';
  info: {
    description: '';
    displayName: 'Content PIM';
    pluralName: 'content-pims';
    singularName: 'content-pim';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    body: Schema.Attribute.DynamicZone<['pim.logo', 'pim.media']>;
    content_name: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    i18n: Schema.Attribute.JSON;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::content-pim.content-pim'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    slug: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiContentVendorContentVendor
  extends Struct.CollectionTypeSchema {
  collectionName: 'content_vendors';
  info: {
    description: '';
    displayName: 'Content Vendor';
    pluralName: 'content-vendors';
    singularName: 'content-vendor';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    body: Schema.Attribute.DynamicZone<
      ['vendor.logo', 'vendor.media', 'vendor.resource-section']
    >;
    content_name: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    i18n: Schema.Attribute.JSON &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::content-vendor.content-vendor'
    >;
    publishedAt: Schema.Attribute.DateTime;
    slug: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCrmActivityCrmActivity extends Struct.CollectionTypeSchema {
  collectionName: 'crm_activities';
  info: {
    displayName: 'CRM Activity';
    pluralName: 'crm-activities';
    singularName: 'crm-activity';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    account_uuid: Schema.Attribute.String;
    activity_id: Schema.Attribute.String & Schema.Attribute.Unique;
    activity_status: Schema.Attribute.String;
    actual_duration: Schema.Attribute.String;
    appointment_category: Schema.Attribute.String;
    attachments: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-attachment.crm-attachment'
    >;
    brand: Schema.Attribute.String;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_contact: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_employee: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_organizer: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_owner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_processor: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    completion_date: Schema.Attribute.DateTime;
    completion_percent: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customer_group: Schema.Attribute.String;
    customer_timezone: Schema.Attribute.String;
    disposition_code: Schema.Attribute.String;
    distribution_channel: Schema.Attribute.String;
    division: Schema.Attribute.String;
    document_type: Schema.Attribute.String;
    due_date: Schema.Attribute.DateTime;
    end_date: Schema.Attribute.DateTime;
    follow_up_and_related_item_transactions: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-follow-up-and-related-item.crm-follow-up-and-related-item'
    >;
    follow_up_and_related_items: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-follow-up-and-related-item.crm-follow-up-and-related-item'
    >;
    full_day_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    initiator_code: Schema.Attribute.String;
    involved_parties: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-involved-party.crm-involved-party'
    >;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-activity.crm-activity'
    >;
    location: Schema.Attribute.String;
    main_account_party_id: Schema.Attribute.String;
    main_contact_party_id: Schema.Attribute.String;
    main_employee_responsible_party_id: Schema.Attribute.String;
    notes: Schema.Attribute.Relation<'oneToMany', 'api::crm-note.crm-note'>;
    opportunity_followups: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document'
    >;
    organizer_party_id: Schema.Attribute.String;
    owner_party_id: Schema.Attribute.String;
    phone_call_category: Schema.Attribute.String;
    planned_duration: Schema.Attribute.String;
    primary_contact_uuid: Schema.Attribute.String;
    priority: Schema.Attribute.String;
    processor_party_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    ranking: Schema.Attribute.String;
    reason: Schema.Attribute.String;
    sales_organization: Schema.Attribute.String;
    start_date: Schema.Attribute.DateTime;
    subject: Schema.Attribute.Text;
    task_category: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCrmAttachmentCrmAttachment
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_attachments';
  info: {
    description: '';
    displayName: 'CRM Attachment';
    pluralName: 'crm-attachments';
    singularName: 'crm-attachment';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    activity: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-activity.crm-activity'
    >;
    activity_id: Schema.Attribute.String;
    bp_id: Schema.Attribute.String;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    category_code: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    document_link: Schema.Attribute.String;
    link_web_uri: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-attachment.crm-attachment'
    >;
    mime_type: Schema.Attribute.String;
    name: Schema.Attribute.String;
    opportunity: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-opportunity.crm-opportunity'
    >;
    opportunity_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    type_code: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    uuid: Schema.Attribute.UID;
  };
}

export interface ApiCrmCompetitorProductCrmCompetitorProduct
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_competitor_products';
  info: {
    displayName: 'CRM Competitor Product';
    pluralName: 'crm-competitor-products';
    singularName: 'crm-competitor-product';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    base_uom: Schema.Attribute.String;
    best_seller_indicator: Schema.Attribute.Boolean;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    competitor: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-competitor.crm-competitor'
    >;
    competitor_id: Schema.Attribute.String;
    competitor_product: Schema.Attribute.Relation<
      'manyToOne',
      'api::product.product'
    >;
    competitor_product_id: Schema.Attribute.String;
    competitor_product_name: Schema.Attribute.String;
    competitor_product_uuid: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    currency: Schema.Attribute.String;
    list_price: Schema.Attribute.Decimal;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-competitor-product.crm-competitor-product'
    >;
    own_product_category_id: Schema.Attribute.String;
    own_product_id: Schema.Attribute.String;
    product_comparison: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    status_code: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCrmCompetitorCrmCompetitor
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_competitors';
  info: {
    displayName: 'CRM Competitor';
    pluralName: 'crm-competitors';
    singularName: 'crm-competitor';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    additional_city_name: Schema.Attribute.String;
    additional_name: Schema.Attribute.String;
    address_line1: Schema.Attribute.String;
    address_line2: Schema.Attribute.String;
    address_line4: Schema.Attribute.String;
    address_line5: Schema.Attribute.String;
    best_reached_by_code: Schema.Attribute.String;
    business_partner_formatted_name: Schema.Attribute.String;
    care_of_name: Schema.Attribute.String;
    city: Schema.Attribute.String;
    classification_code: Schema.Attribute.String;
    company_postal_code: Schema.Attribute.String;
    competitor_id: Schema.Attribute.String;
    competitor_uuid: Schema.Attribute.UID;
    country_code: Schema.Attribute.String;
    county: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    crm_competitor_products: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-competitor-product.crm-competitor-product'
    >;
    district: Schema.Attribute.String;
    email: Schema.Attribute.String;
    fax: Schema.Attribute.String;
    formatted_postal_address_description: Schema.Attribute.String;
    house_number: Schema.Attribute.String;
    language_code: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-competitor.crm-competitor'
    >;
    name: Schema.Attribute.String;
    normalised_phone: Schema.Attribute.String;
    object_id: Schema.Attribute.String;
    phone: Schema.Attribute.String;
    po_box: Schema.Attribute.String;
    po_box_deviating_city: Schema.Attribute.String;
    po_box_deviating_country_code: Schema.Attribute.String;
    po_box_deviating_state_code: Schema.Attribute.String;
    po_box_postal_code: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    region_code: Schema.Attribute.String;
    status_code: Schema.Attribute.String;
    street: Schema.Attribute.String;
    street_postal_code: Schema.Attribute.String;
    tax_jurisdiction_code: Schema.Attribute.String;
    time_zone_code: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    web_site: Schema.Attribute.String;
  };
}

export interface ApiCrmFollowUpAndRelatedItemCrmFollowUpAndRelatedItem
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_follow_up_and_related_items';
  info: {
    displayName: 'CRM Follow Up And Related Item';
    pluralName: 'crm-follow-up-and-related-items';
    singularName: 'crm-follow-up-and-related-item';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    activity: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-activity.crm-activity'
    >;
    activity_id: Schema.Attribute.String;
    activity_transaction: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-activity.crm-activity'
    >;
    activity_transaction_id: Schema.Attribute.String;
    activity_type_code: Schema.Attribute.String;
    btd_role_code: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-follow-up-and-related-item.crm-follow-up-and-related-item'
    >;
    publishedAt: Schema.Attribute.DateTime;
    type_code: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCrmInvolvedPartyCrmInvolvedParty
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_involved_parties';
  info: {
    displayName: 'CRM Involved Party';
    pluralName: 'crm-involved-parties';
    singularName: 'crm-involved-party';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    activity: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-activity.crm-activity'
    >;
    activity_id: Schema.Attribute.String;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-involved-party.crm-involved-party'
    >;
    main_indicator: Schema.Attribute.Boolean;
    party_id: Schema.Attribute.String;
    party_type_code: Schema.Attribute.String;
    party_uuid: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    role_category_code: Schema.Attribute.String;
    role_code: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCrmNoteCrmNote extends Struct.CollectionTypeSchema {
  collectionName: 'crm_notes';
  info: {
    displayName: 'CRM Note';
    pluralName: 'crm-notes';
    singularName: 'crm-note';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    activity: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-activity.crm-activity'
    >;
    activity_id: Schema.Attribute.String;
    bp_id: Schema.Attribute.String;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    is_global_note: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-note.crm-note'
    >;
    note: Schema.Attribute.Text;
    opportunity: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-opportunity.crm-opportunity'
    >;
    opportunity_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCrmOpportunityPartyContactPartyCrmOpportunityPartyContactParty
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_opportunity_party_contact_parties';
  info: {
    displayName: 'CRM Opportunity Party Contact Party';
    pluralName: 'crm-opportunity-party-contact-parties';
    singularName: 'crm-opportunity-party-contact-party';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    attitude_toward_opportunity_code: Schema.Attribute.String;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity-party-contact-party.crm-opportunity-party-contact-party'
    >;
    object_id: Schema.Attribute.String;
    opportunity: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-opportunity.crm-opportunity'
    >;
    opportunity_id: Schema.Attribute.String;
    opportunity_party_contact_main_indicator: Schema.Attribute.Boolean;
    opportunity_party_contact_party_id: Schema.Attribute.String;
    parent_object_id: Schema.Attribute.String;
    party_contact_party_uuid: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    role_code: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    uuid: Schema.Attribute.UID;
  };
}

export interface ApiCrmOpportunityPartyCrmOpportunityParty
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_opportunity_parties';
  info: {
    displayName: 'CRM Opportunity Party';
    pluralName: 'crm-opportunity-parties';
    singularName: 'crm-opportunity-party';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity-party.crm-opportunity-party'
    >;
    main_indicator: Schema.Attribute.Boolean;
    object_id: Schema.Attribute.String;
    opportunity: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-opportunity.crm-opportunity'
    >;
    opportunity_id: Schema.Attribute.String;
    parent_object_id: Schema.Attribute.String;
    party_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    role_category_code: Schema.Attribute.String;
    role_code: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCrmOpportunityPrecedingAndFollowUpDocumentCrmOpportunityPrecedingAndFollowUpDocument
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_opportunity_preceding_and_follow_up_documents';
  info: {
    displayName: 'CRM Opportunity Preceding And Follow-Up Document';
    pluralName: 'crm-opportunity-preceding-and-follow-up-documents';
    singularName: 'crm-opportunity-preceding-and-follow-up-document';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    activity: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-activity.crm-activity'
    >;
    activity_id: Schema.Attribute.String;
    business_transaction_document_relationship_role_code: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    item_id: Schema.Attribute.String;
    item_type_code: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document'
    >;
    main_indicator: Schema.Attribute.Boolean;
    object_id: Schema.Attribute.String;
    opportunity: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-opportunity.crm-opportunity'
    >;
    opportunity_id: Schema.Attribute.String;
    opportunity_transaction: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-opportunity.crm-opportunity'
    >;
    opportunity_transaction_id: Schema.Attribute.String;
    parent_object_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    sales_cycle_code: Schema.Attribute.String;
    sales_cycle_phase_code: Schema.Attribute.String;
    sales_cycle_phase_step_code: Schema.Attribute.String;
    type_code: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCrmOpportunitySalesTeamPartyCrmOpportunitySalesTeamParty
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_opportunity_sales_team_parties';
  info: {
    displayName: 'CRM Opportunity Sales Team Party';
    pluralName: 'crm-opportunity-sales-team-parties';
    singularName: 'crm-opportunity-sales-team-party';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity-sales-team-party.crm-opportunity-sales-team-party'
    >;
    object_id: Schema.Attribute.String;
    opportunity: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-opportunity.crm-opportunity'
    >;
    opportunity_id: Schema.Attribute.String;
    parent_object_id: Schema.Attribute.String;
    party_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    role_category_code: Schema.Attribute.String;
    role_code: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCrmOpportunityCrmOpportunity
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_opportunities';
  info: {
    displayName: 'CRM Opportunity';
    pluralName: 'crm-opportunities';
    singularName: 'crm-opportunity';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    approval_status_code: Schema.Attribute.String;
    approver_party_id: Schema.Attribute.String;
    attachments: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-attachment.crm-attachment'
    >;
    best_connected_colleague: Schema.Attribute.String;
    bill_to_party_id: Schema.Attribute.String;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_contact: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_owner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    consistency_status_code: Schema.Attribute.String;
    created_by: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    creation_date: Schema.Attribute.Date;
    creation_date_time: Schema.Attribute.DateTime;
    deal_score: Schema.Attribute.Decimal;
    deal_score_reason: Schema.Attribute.String;
    distribution_channel_code: Schema.Attribute.String;
    division_code: Schema.Attribute.String;
    end_buyer_party_id: Schema.Attribute.String;
    expected_processing_end_date: Schema.Attribute.Date;
    expected_processing_start_date: Schema.Attribute.Date;
    expected_revenue_amount: Schema.Attribute.Decimal;
    expected_revenue_amount_currency_code: Schema.Attribute.String;
    expected_revenue_end_date: Schema.Attribute.Date;
    expected_revenue_start_date: Schema.Attribute.Date;
    external_id: Schema.Attribute.String;
    external_user_status_code: Schema.Attribute.String;
    group_code: Schema.Attribute.String;
    header_revenue_schedule: Schema.Attribute.Boolean;
    last_change_date: Schema.Attribute.Date;
    last_change_date_time: Schema.Attribute.DateTime;
    last_changed_by: Schema.Attribute.String;
    life_cycle_status_code: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity.crm-opportunity'
    >;
    main_employee_responsible_party_id: Schema.Attribute.String;
    name: Schema.Attribute.String;
    need_help: Schema.Attribute.Boolean;
    notes: Schema.Attribute.Relation<'oneToMany', 'api::crm-note.crm-note'>;
    object_id: Schema.Attribute.String;
    opportunity_contact_parties: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity-party-contact-party.crm-opportunity-party-contact-party'
    >;
    opportunity_followup_transactions: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document'
    >;
    opportunity_followups: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document'
    >;
    opportunity_id: Schema.Attribute.String & Schema.Attribute.Unique;
    opportunity_parties: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity-party.crm-opportunity-party'
    >;
    opportunity_sales_team_parties: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-opportunity-sales-team-party.crm-opportunity-sales-team-party'
    >;
    origin_type_code: Schema.Attribute.String;
    payer_party_id: Schema.Attribute.String;
    phase_progress_evaluation_status_code: Schema.Attribute.String;
    primary_contact_party_id: Schema.Attribute.String;
    priority_code: Schema.Attribute.String;
    probability_percent: Schema.Attribute.Integer;
    process_status_valid_since_date: Schema.Attribute.Date;
    processing_type_code: Schema.Attribute.String;
    product_recepient_party_id: Schema.Attribute.String;
    prospect_budget_amount: Schema.Attribute.Decimal;
    prospect_budget_amount_currency_code: Schema.Attribute.String;
    prospect_party_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    result_reason_code: Schema.Attribute.String;
    sales_cycle_code: Schema.Attribute.String;
    sales_cycle_phase_code: Schema.Attribute.String;
    sales_cycle_phase_start_date: Schema.Attribute.Date;
    sales_forecast_category_code: Schema.Attribute.String;
    sales_group_id: Schema.Attribute.String;
    sales_office_id: Schema.Attribute.String;
    sales_organisation_id: Schema.Attribute.String;
    sales_revenue_forecast_relevance_indicator: Schema.Attribute.Boolean;
    sales_territory_id: Schema.Attribute.String;
    sales_unit_party_id: Schema.Attribute.String;
    score: Schema.Attribute.Decimal;
    seller_party_id: Schema.Attribute.String;
    total_expected_net_amount: Schema.Attribute.Decimal;
    total_expected_net_amount_amount_currency_code: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    uuid: Schema.Attribute.String;
    weighted_expected_net_amount: Schema.Attribute.Decimal;
    weighted_expected_net_amount_currency_code: Schema.Attribute.String;
  };
}

export interface ApiCrmOrganisationalUnitAddressCrmOrganisationalUnitAddress
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_organisational_unit_addresses';
  info: {
    displayName: 'CRM Organisational Unit Address';
    pluralName: 'crm-organisational-unit-addresses';
    singularName: 'crm-organisational-unit-address';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    additional_street_prefix_name: Schema.Attribute.String;
    additional_street_suffix_name: Schema.Attribute.String;
    care_of_name: Schema.Attribute.String;
    city_name: Schema.Attribute.String;
    conventional_phone_formatted_number_description: Schema.Attribute.String;
    country_code: Schema.Attribute.String;
    county_name: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    district_name: Schema.Attribute.String;
    email_normalised_uri: Schema.Attribute.String;
    email_uri: Schema.Attribute.String;
    end_date: Schema.Attribute.Date;
    facsimile_formatted_number_description: Schema.Attribute.String;
    formatted_postal_address_description: Schema.Attribute.String;
    house_number: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit-address.crm-organisational-unit-address'
    >;
    mobile_formatted_number_description: Schema.Attribute.String;
    name: Schema.Attribute.String;
    organisational_unit: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-organisational-unit.crm-organisational-unit'
    >;
    organisational_unit_id: Schema.Attribute.String;
    po_box_deviating_city_name: Schema.Attribute.String;
    po_box_deviating_country_code: Schema.Attribute.String;
    po_box_deviating_region_code: Schema.Attribute.String;
    po_box_id: Schema.Attribute.String;
    po_box_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    po_box_postal_code: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    region_code: Schema.Attribute.String;
    start_date: Schema.Attribute.Date;
    street_name: Schema.Attribute.String;
    street_postal_code: Schema.Attribute.String;
    street_prefix_name: Schema.Attribute.String;
    street_suffix_name: Schema.Attribute.String;
    time_zone_code: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    web_uri: Schema.Attribute.String;
  };
}

export interface ApiCrmOrganisationalUnitCompanyCrmOrganisationalUnitCompany
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_organisational_unit_companies';
  info: {
    displayName: 'CRM Organisational Unit Company';
    pluralName: 'crm-organisational-unit-companies';
    singularName: 'crm-organisational-unit-company';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    company_name: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    end_date: Schema.Attribute.Date;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit-company.crm-organisational-unit-company'
    >;
    organisational_unit: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-organisational-unit.crm-organisational-unit'
    >;
    organisational_unit_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    start_date: Schema.Attribute.Date;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCrmOrganisationalUnitEmployeeCrmOrganisationalUnitEmployee
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_organisational_unit_employees';
  info: {
    displayName: 'CRM Organisational Unit Employee';
    pluralName: 'crm-organisational-unit-employees';
    singularName: 'crm-organisational-unit-employee';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_internal_id: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    employee_external_key: Schema.Attribute.String;
    employee_id: Schema.Attribute.String;
    end_date: Schema.Attribute.Date;
    functional_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    job_external_key: Schema.Attribute.String;
    job_id: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit-employee.crm-organisational-unit-employee'
    >;
    organisational_unit: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-organisational-unit.crm-organisational-unit'
    >;
    organisational_unit_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    reporting_line_indicator: Schema.Attribute.Boolean;
    role_code: Schema.Attribute.Integer;
    start_date: Schema.Attribute.Date;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCrmOrganisationalUnitFunctionCrmOrganisationalUnitFunction
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_organisational_unit_functions';
  info: {
    displayName: 'CRM Organisational Unit Function';
    pluralName: 'crm-organisational-unit-functions';
    singularName: 'crm-organisational-unit-function';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    company_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    currency_code: Schema.Attribute.String;
    end_date: Schema.Attribute.Date;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit-function.crm-organisational-unit-function'
    >;
    marketing_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    organisational_unit: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-organisational-unit.crm-organisational-unit'
    >;
    organisational_unit_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    reporting_line_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    sales_group_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    sales_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    sales_office_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    sales_organisation_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    service_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    service_organisation_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    start_date: Schema.Attribute.Date;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCrmOrganisationalUnitManagerCrmOrganisationalUnitManager
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_organisational_unit_managers';
  info: {
    displayName: 'CRM Organisational Unit Manager';
    pluralName: 'crm-organisational-unit-managers';
    singularName: 'crm-organisational-unit-manager';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    business_partner_internal_id: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    end_date: Schema.Attribute.Date;
    functional_manager_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit-manager.crm-organisational-unit-manager'
    >;
    organisational_unit: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-organisational-unit.crm-organisational-unit'
    >;
    organisational_unit_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    reporting_line_manager_indicator: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    start_date: Schema.Attribute.Date;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCrmOrganisationalUnitCrmOrganisationalUnit
  extends Struct.CollectionTypeSchema {
  collectionName: 'crm_organisational_units';
  info: {
    displayName: 'CRM Organisational Unit';
    pluralName: 'crm-organisational-units';
    singularName: 'crm-organisational-unit';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    addresses: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit-address.crm-organisational-unit-address'
    >;
    child_organisational_units: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit.crm-organisational-unit'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    crm_org_unit_companies: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit-company.crm-organisational-unit-company'
    >;
    crm_org_unit_employees: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit-employee.crm-organisational-unit-employee'
    >;
    crm_org_unit_functions: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit-function.crm-organisational-unit-function'
    >;
    crm_org_unit_managers: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit-manager.crm-organisational-unit-manager'
    >;
    end_date: Schema.Attribute.Date;
    lifecycle_status_code: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-organisational-unit.crm-organisational-unit'
    >;
    mark_as_deleted: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    name: Schema.Attribute.String;
    organisational_unit_id: Schema.Attribute.String & Schema.Attribute.Unique;
    parent_organisational_unit: Schema.Attribute.Relation<
      'manyToOne',
      'api::crm-organisational-unit.crm-organisational-unit'
    >;
    parent_organisational_unit_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    start_date: Schema.Attribute.Date;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCustAddrDepdntInformationCustAddrDepdntInformation
  extends Struct.CollectionTypeSchema {
  collectionName: 'cust_addr_depdnt_informations';
  info: {
    displayName: 'Customer Address Dependent Information';
    pluralName: 'cust-addr-depdnt-informations';
    singularName: 'cust-addr-depdnt-information';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    address_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    city_code: Schema.Attribute.String;
    county: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customer: Schema.Attribute.Relation<'manyToOne', 'api::customer.customer'>;
    customer_id: Schema.Attribute.String;
    express_train_station_name: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::cust-addr-depdnt-information.cust-addr-depdnt-information'
    >;
    publishedAt: Schema.Attribute.DateTime;
    train_station_name: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCustomerCompanyTextCustomerCompanyText
  extends Struct.CollectionTypeSchema {
  collectionName: 'customer_company_texts';
  info: {
    displayName: 'Customer Company Text';
    pluralName: 'customer-company-texts';
    singularName: 'customer-company-text';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    company: Schema.Attribute.Relation<
      'manyToOne',
      'api::customer-company.customer-company'
    >;
    company_code: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customer: Schema.Attribute.Relation<'manyToOne', 'api::customer.customer'>;
    customer_id: Schema.Attribute.String & Schema.Attribute.Required;
    language: Schema.Attribute.String & Schema.Attribute.Required;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-company-text.customer-company-text'
    >;
    long_text: Schema.Attribute.Text;
    long_text_id: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCustomerCompanyCustomerCompany
  extends Struct.CollectionTypeSchema {
  collectionName: 'customer_companies';
  info: {
    displayName: 'Customer Company';
    pluralName: 'customer-companies';
    singularName: 'customer-company';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    account_by_customer: Schema.Attribute.String;
    accounting_clerk: Schema.Attribute.String;
    accounting_clerk_fax_number: Schema.Attribute.String;
    accounting_clerk_internet_address: Schema.Attribute.String;
    accounting_clerk_phone_number: Schema.Attribute.String;
    alternative_payer_account: Schema.Attribute.String;
    apart_tolerance_group: Schema.Attribute.String;
    authorization_group: Schema.Attribute.String;
    cash_planning_group: Schema.Attribute.String;
    collective_invoice_variant: Schema.Attribute.String;
    company_code: Schema.Attribute.String;
    company_texts: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-company-text.customer-company-text'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customer: Schema.Attribute.Relation<'manyToOne', 'api::customer.customer'>;
    customer_account_group: Schema.Attribute.String;
    customer_account_note: Schema.Attribute.Text;
    customer_head_office: Schema.Attribute.String;
    customer_id: Schema.Attribute.String;
    customer_supplier_clearing_is_used: Schema.Attribute.Boolean;
    deletion_indicator: Schema.Attribute.Boolean;
    house_bank: Schema.Attribute.String;
    interest_calculation_code: Schema.Attribute.String;
    interest_calculation_date: Schema.Attribute.DateTime;
    intrst_calc_frequency_in_months: Schema.Attribute.Integer;
    is_to_be_locally_processed: Schema.Attribute.Boolean;
    item_is_to_be_paid_separately: Schema.Attribute.Boolean;
    known_or_negotiated_leave: Schema.Attribute.String;
    last_interest_calc_run_date: Schema.Attribute.DateTime;
    layout_sorting_rule: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-company.customer-company'
    >;
    payment_blocking_reason: Schema.Attribute.String;
    payment_methods_list: Schema.Attribute.String;
    payment_reason: Schema.Attribute.String;
    payment_terms: Schema.Attribute.String;
    payt_advice_is_sent_by_edi: Schema.Attribute.Boolean;
    physical_inventory_block_ind: Schema.Attribute.Boolean;
    publishedAt: Schema.Attribute.DateTime;
    reconciliation_account: Schema.Attribute.String;
    record_payment_history_indicator: Schema.Attribute.Boolean;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    user_at_customer: Schema.Attribute.String;
    value_adjustment_key: Schema.Attribute.String;
  };
}

export interface ApiCustomerPartnerFunctionCustomerPartnerFunction
  extends Struct.CollectionTypeSchema {
  collectionName: 'customer_partner_functions';
  info: {
    displayName: 'Customer Partner Function';
    pluralName: 'customer-partner-functions';
    singularName: 'customer-partner-function';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    address: Schema.Attribute.String;
    bp_customer_number: Schema.Attribute.String;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customer: Schema.Attribute.Relation<'manyToOne', 'api::customer.customer'>;
    customer_id: Schema.Attribute.String & Schema.Attribute.Required;
    distribution_channel: Schema.Attribute.String & Schema.Attribute.Required;
    division: Schema.Attribute.String & Schema.Attribute.Required;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-partner-function.customer-partner-function'
    >;
    name: Schema.Attribute.String;
    partner_counter: Schema.Attribute.String & Schema.Attribute.Required;
    partner_function: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    sales_organization: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCustomerSalesAreaTextCustomerSalesAreaText
  extends Struct.CollectionTypeSchema {
  collectionName: 'customer_sales_area_texts';
  info: {
    displayName: 'Customer Sales Area Text';
    pluralName: 'customer-sales-area-texts';
    singularName: 'customer-sales-area-text';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customer: Schema.Attribute.Relation<'manyToOne', 'api::customer.customer'>;
    customer_id: Schema.Attribute.String & Schema.Attribute.Required;
    distribution_channel: Schema.Attribute.String & Schema.Attribute.Required;
    division: Schema.Attribute.String & Schema.Attribute.Required;
    language: Schema.Attribute.String & Schema.Attribute.Required;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-sales-area-text.customer-sales-area-text'
    >;
    long_text: Schema.Attribute.Text;
    long_text_id: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    sales_area: Schema.Attribute.Relation<
      'manyToOne',
      'api::customer-sales-area.customer-sales-area'
    >;
    sales_organization: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCustomerSalesAreaCustomerSalesArea
  extends Struct.CollectionTypeSchema {
  collectionName: 'customer_sales_areas';
  info: {
    description: '';
    displayName: 'Customer Sales Area';
    pluralName: 'customer-sales-areas';
    singularName: 'customer-sales-area';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    account_by_customer: Schema.Attribute.String;
    additional_customer_group1: Schema.Attribute.String;
    additional_customer_group2: Schema.Attribute.String;
    additional_customer_group3: Schema.Attribute.String;
    additional_customer_group4: Schema.Attribute.String;
    additional_customer_group5: Schema.Attribute.String;
    authorization_group: Schema.Attribute.String;
    billing_is_blocked_for_customer: Schema.Attribute.String;
    complete_delivery_is_defined: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    credit_control_area: Schema.Attribute.String;
    currency: Schema.Attribute.String;
    cust_is_rlvt_for_settlmt_mgmt: Schema.Attribute.Boolean;
    cust_prod_proposal_procedure: Schema.Attribute.String;
    customer: Schema.Attribute.Relation<'manyToOne', 'api::customer.customer'>;
    customer_abc_classification: Schema.Attribute.String;
    customer_account_assignment_group: Schema.Attribute.String;
    customer_account_group: Schema.Attribute.String;
    customer_group: Schema.Attribute.String;
    customer_id: Schema.Attribute.String & Schema.Attribute.Required;
    customer_is_rebate_relevant: Schema.Attribute.Boolean;
    customer_payment_terms: Schema.Attribute.String;
    customer_price_group: Schema.Attribute.String;
    customer_pricing_procedure: Schema.Attribute.String;
    customer_statistics_group: Schema.Attribute.String;
    deletion_indicator: Schema.Attribute.Boolean;
    delivery_is_blocked_for_customer: Schema.Attribute.Boolean;
    delivery_priority: Schema.Attribute.String;
    distribution_channel: Schema.Attribute.String & Schema.Attribute.Required;
    division: Schema.Attribute.String & Schema.Attribute.Required;
    exchange_rate_type: Schema.Attribute.String;
    incoterms_classification: Schema.Attribute.String;
    incoterms_location1: Schema.Attribute.String;
    incoterms_location2: Schema.Attribute.String;
    incoterms_sup_chn_dvtg_loc_addl_uuid: Schema.Attribute.String;
    incoterms_sup_chn_loc1_addl_uuid: Schema.Attribute.String;
    incoterms_sup_chn_loc2_addl_uuid: Schema.Attribute.String;
    incoterms_transfer_location: Schema.Attribute.String;
    incoterms_version: Schema.Attribute.String;
    insp_sbst_has_no_time_or_quantity: Schema.Attribute.Boolean;
    invoice_date: Schema.Attribute.String;
    invoice_list_schedule: Schema.Attribute.String;
    item_order_probability_in_percent: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-sales-area.customer-sales-area'
    >;
    manual_invoice_maint_is_relevant: Schema.Attribute.Boolean;
    max_nmbr_of_partial_delivery: Schema.Attribute.String;
    order_combination_is_allowed: Schema.Attribute.Boolean;
    order_is_blocked_for_customer: Schema.Attribute.Boolean;
    overdeliv_tolrtd_lmt_ratio_in_pct: Schema.Attribute.String;
    partial_delivery_is_allowed: Schema.Attribute.Boolean;
    payment_guarantee_procedure: Schema.Attribute.String;
    price_list_type: Schema.Attribute.String;
    product_unit_group: Schema.Attribute.String;
    proof_of_delivery_time_value: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    sales_area_texts: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-sales-area-text.customer-sales-area-text'
    >;
    sales_district: Schema.Attribute.String;
    sales_group: Schema.Attribute.String;
    sales_item_proposal: Schema.Attribute.String;
    sales_office: Schema.Attribute.String;
    sales_organization: Schema.Attribute.String & Schema.Attribute.Required;
    shipping_condition: Schema.Attribute.String;
    sls_doc_is_rlvt_for_proof_of_deliv: Schema.Attribute.Boolean;
    sls_unlmtd_ovrdeliv_is_allwd: Schema.Attribute.Boolean;
    supplying_plant: Schema.Attribute.String;
    underdeliv_tolrtd_lmt_ratio_in_pct: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCustomerServiceCategoryCustomerServiceCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'customer_service_categories';
  info: {
    description: '';
    displayName: 'Customer Service Category';
    pluralName: 'customer-service-categories';
    singularName: 'customer-service-category';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customer_service_questions: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-service-question.customer-service-question'
    >;
    Icon: Schema.Attribute.Media<'images'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-service-category.customer-service-category'
    > &
      Schema.Attribute.Private;
    Name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    slug: Schema.Attribute.UID<'Name'> & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCustomerServiceQuestionCustomerServiceQuestion
  extends Struct.CollectionTypeSchema {
  collectionName: 'customer_service_questions';
  info: {
    description: '';
    displayName: 'Customer Service Question';
    pluralName: 'customer-service-questions';
    singularName: 'customer-service-question';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customer_service_category: Schema.Attribute.Relation<
      'manyToOne',
      'api::customer-service-category.customer-service-category'
    >;
    Description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-service-question.customer-service-question'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    related_articles: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-service-question.customer-service-question'
    >;
    slug: Schema.Attribute.UID<'Title'> & Schema.Attribute.Required;
    Title: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCustomerTaxGroupingCustomerTaxGrouping
  extends Struct.CollectionTypeSchema {
  collectionName: 'customer_tax_groupings';
  info: {
    displayName: 'Customer Tax Grouping';
    pluralName: 'customer-tax-groupings';
    singularName: 'customer-tax-grouping';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    cust_tax_group_exemption_end_date: Schema.Attribute.DateTime;
    cust_tax_group_exemption_rate: Schema.Attribute.Decimal;
    cust_tax_group_exemption_start_date: Schema.Attribute.DateTime;
    cust_tax_group_subjected_end_date: Schema.Attribute.DateTime;
    cust_tax_group_subjected_start_date: Schema.Attribute.DateTime;
    cust_tax_grp_exemption_certificate: Schema.Attribute.String;
    customer: Schema.Attribute.Relation<'manyToOne', 'api::customer.customer'>;
    customer_id: Schema.Attribute.String & Schema.Attribute.Required;
    customer_tax_grouping_code: Schema.Attribute.String &
      Schema.Attribute.Required;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-tax-grouping.customer-tax-grouping'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCustomerTextCustomerText
  extends Struct.CollectionTypeSchema {
  collectionName: 'customer_texts';
  info: {
    description: '';
    displayName: 'Customer Text';
    pluralName: 'customer-texts';
    singularName: 'customer-text';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customer: Schema.Attribute.Relation<'manyToOne', 'api::customer.customer'>;
    customer_id: Schema.Attribute.String & Schema.Attribute.Required;
    language: Schema.Attribute.String & Schema.Attribute.Required;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-text.customer-text'
    >;
    long_text: Schema.Attribute.Text;
    long_text_id: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiCustomerCustomer extends Struct.CollectionTypeSchema {
  collectionName: 'customers';
  info: {
    displayName: 'Customer';
    pluralName: 'customers';
    singularName: 'customer';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    admin_users: Schema.Attribute.Relation<'manyToMany', 'admin::user'>;
    authorization_group: Schema.Attribute.String;
    billing_is_blocked_for_customer: Schema.Attribute.Boolean;
    bp_id: Schema.Attribute.String;
    business_partner: Schema.Attribute.Relation<
      'oneToOne',
      'api::business-partner.business-partner'
    >;
    cart: Schema.Attribute.Relation<'oneToMany', 'api::cart.cart'>;
    cart_reserves: Schema.Attribute.Relation<
      'oneToMany',
      'api::cart-reserve.cart-reserve'
    >;
    city_code: Schema.Attribute.String;
    companies: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-company.customer-company'
    >;
    county: Schema.Attribute.String;
    created_by_user: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    creation_date: Schema.Attribute.DateTime;
    cust_addr_depdnt_informations: Schema.Attribute.Relation<
      'oneToMany',
      'api::cust-addr-depdnt-information.cust-addr-depdnt-information'
    >;
    customer_account_group: Schema.Attribute.String;
    customer_classification: Schema.Attribute.String;
    customer_company_texts: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-company-text.customer-company-text'
    >;
    customer_corporate_group: Schema.Attribute.String;
    customer_full_name: Schema.Attribute.String;
    customer_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    customer_name: Schema.Attribute.String;
    customer_tax_groupings: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-tax-grouping.customer-tax-grouping'
    >;
    customer_texts: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-text.customer-text'
    >;
    deletion_indicator: Schema.Attribute.Boolean;
    delivery_is_blocked: Schema.Attribute.String;
    express_train_station_name: Schema.Attribute.String;
    fiscal_address: Schema.Attribute.String;
    free_defined_attribute_01: Schema.Attribute.String;
    free_defined_attribute_02: Schema.Attribute.String;
    free_defined_attribute_03: Schema.Attribute.String;
    free_defined_attribute_04: Schema.Attribute.String;
    free_defined_attribute_05: Schema.Attribute.String;
    industry: Schema.Attribute.String;
    industry_code_1: Schema.Attribute.String;
    industry_code_2: Schema.Attribute.String;
    international_location_number_1: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer.customer'
    >;
    nielsen_region: Schema.Attribute.String;
    order_is_blocked_for_customer: Schema.Attribute.String;
    partner_functions: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-partner-function.customer-partner-function'
    >;
    payment_reason: Schema.Attribute.String;
    posting_is_blocked: Schema.Attribute.Boolean;
    publishedAt: Schema.Attribute.DateTime;
    responsible_type: Schema.Attribute.String;
    sales_area_texts: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-sales-area-text.customer-sales-area-text'
    >;
    sales_areas: Schema.Attribute.Relation<
      'oneToMany',
      'api::customer-sales-area.customer-sales-area'
    >;
    tax_number_1: Schema.Attribute.String;
    tax_number_2: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    user_vendors: Schema.Attribute.Relation<
      'oneToMany',
      'api::user-vendor.user-vendor'
    >;
    users: Schema.Attribute.Relation<
      'manyToMany',
      'plugin::users-permissions.user'
    >;
    vat_registration: Schema.Attribute.String;
  };
}

export interface ApiFgBpRelationshipFgBpRelationship
  extends Struct.CollectionTypeSchema {
  collectionName: 'fg_bp_relationships';
  info: {
    displayName: 'Flexible Group Business Partner Relationship';
    pluralName: 'fg-bp-relationships';
    singularName: 'fg-bp-relationship';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-bp-relationship.fg-bp-relationship'
    >;
    locationbusinesspartner: Schema.Attribute.String;
    locationbusinesspartnername: Schema.Attribute.String;
    membershipparentbp: Schema.Attribute.String;
    membershipparentbpname: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    relationshipbelongsto: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    validityfrom: Schema.Attribute.Date;
    validityto: Schema.Attribute.Date;
  };
}

export interface ApiFgControlMainFgControlMain
  extends Struct.CollectionTypeSchema {
  collectionName: 'fg_control_mains';
  info: {
    displayName: 'Flexible Group Control Main';
    pluralName: 'fg-control-mains';
    singularName: 'fg-control-main';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.Text;
    flex_group_id: Schema.Attribute.String;
    flex_group_type: Schema.Attribute.String;
    icon_image: Schema.Attribute.Text;
    icon_text: Schema.Attribute.Text;
    indicator_type: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-control-main.fg-control-main'
    >;
    operand: Schema.Attribute.Enumeration<['ADD', 'UPDATE', 'DELETE']> &
      Schema.Attribute.DefaultTo<'ADD'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    valid_from: Schema.Attribute.Date;
    valid_to: Schema.Attribute.Date;
  };
}

export interface ApiFgCustomerBusinessFgCustomerBusiness
  extends Struct.CollectionTypeSchema {
  collectionName: 'fg_customer_businesses';
  info: {
    displayName: 'Flexible Group Customer Business';
    pluralName: 'fg-customer-businesses';
    singularName: 'fg-customer-business';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    bp_id: Schema.Attribute.String;
    business_partner: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    flex_group_id: Schema.Attribute.String;
    is_all_bp: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-customer-business.fg-customer-business'
    >;
    membership: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    membership_id: Schema.Attribute.String;
    operand: Schema.Attribute.Enumeration<['ADD', 'UPDATE', 'DELETE']> &
      Schema.Attribute.DefaultTo<'ADD'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiFgCustomerInternalBackupFgCustomerInternalBackup
  extends Struct.CollectionTypeSchema {
  collectionName: 'fg_customer_internal_backups';
  info: {
    displayName: 'Flexible Group Customer Internal Backup';
    pluralName: 'fg-customer-internal-backups';
    singularName: 'fg-customer-internal-backup';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    bp_id: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    flex_group_id: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-customer-internal-backup.fg-customer-internal-backup'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiFgCustomerInternalFgCustomerInternal
  extends Struct.CollectionTypeSchema {
  collectionName: 'fg_customer_internals';
  info: {
    displayName: 'Flexible Group Customer Internal';
    pluralName: 'fg-customer-internals';
    singularName: 'fg-customer-internal';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    bp_id: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    flex_group_id: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-customer-internal.fg-customer-internal'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiFgCustomerProductInternalFgCustomerProductInternal
  extends Struct.CollectionTypeSchema {
  collectionName: 'fg_customer_product_internals';
  info: {
    displayName: 'Flexible Group Customer Product Internal';
    pluralName: 'fg-customer-product-internals';
    singularName: 'fg-customer-product-internal';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    bp_id: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    flex_group_id: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-customer-product-internal.fg-customer-product-internal'
    >;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiFgProductBusinessFgProductBusiness
  extends Struct.CollectionTypeSchema {
  collectionName: 'fg_product_businesses';
  info: {
    displayName: 'Flexible Group Product Business';
    pluralName: 'fg-product-businesses';
    singularName: 'fg-product-business';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    flex_group_id: Schema.Attribute.String;
    is_all_product: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-product-business.fg-product-business'
    >;
    operand: Schema.Attribute.Enumeration<['ADD', 'UPDATE', 'DELETE']> &
      Schema.Attribute.DefaultTo<'ADD'>;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_hierarchy: Schema.Attribute.String;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    vendor: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    vendor_id: Schema.Attribute.String;
  };
}

export interface ApiFgProductInternalBackupFgProductInternalBackup
  extends Struct.CollectionTypeSchema {
  collectionName: 'fg_product_internal_backups';
  info: {
    displayName: 'Flexible Group Product Internal Backup';
    pluralName: 'fg-product-internal-backups';
    singularName: 'fg-product-internal-backup';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    flex_group_id: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-product-internal-backup.fg-product-internal-backup'
    >;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiFgProductInternalFgProductInternal
  extends Struct.CollectionTypeSchema {
  collectionName: 'fg_product_internals';
  info: {
    displayName: 'Flexible Group Product Internal';
    pluralName: 'fg-product-internals';
    singularName: 'fg-product-internal';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    flex_group_id: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-product-internal.fg-product-internal'
    >;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiFgPurchaseInfoRecordFgPurchaseInfoRecord
  extends Struct.CollectionTypeSchema {
  collectionName: 'fg_purchase_info_records';
  info: {
    displayName: 'Flexible Purchase Info Record';
    pluralName: 'fg-purchase-info-records';
    singularName: 'fg-purchase-info-record';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    is_deleted: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-purchase-info-record.fg-purchase-info-record'
    >;
    pir_id: Schema.Attribute.String;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    valid_from: Schema.Attribute.DateTime;
    valid_to: Schema.Attribute.DateTime;
    vendor_id: Schema.Attribute.String;
  };
}

export interface ApiFgRelationshipFgRelationship
  extends Struct.CollectionTypeSchema {
  collectionName: 'fg_relationships';
  info: {
    displayName: 'Flexible Group Relationship';
    pluralName: 'fg-relationships';
    singularName: 'fg-relationship';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    child_id: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    flex_group_id: Schema.Attribute.String;
    is_customer_based: Schema.Attribute.Boolean;
    is_product_based: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-relationship.fg-relationship'
    >;
    operand: Schema.Attribute.Enumeration<['ADD', 'UPDATE', 'DELETE']> &
      Schema.Attribute.DefaultTo<'ADD'>;
    publishedAt: Schema.Attribute.DateTime;
    relationship_type: Schema.Attribute.Enumeration<['ADD', 'SUBTRACT']>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiGuestUserCartItemGuestUserCartItem
  extends Struct.CollectionTypeSchema {
  collectionName: 'guest_user_cart_items';
  info: {
    displayName: 'Guest User Cart Item';
    pluralName: 'guest-user-cart-items';
    singularName: 'guest-user-cart-item';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    cart: Schema.Attribute.Relation<
      'manyToOne',
      'api::guest-user-cart.guest-user-cart'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::guest-user-cart-item.guest-user-cart-item'
    >;
    material: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    requested_quantity: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<1>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiGuestUserCartGuestUserCart
  extends Struct.CollectionTypeSchema {
  collectionName: 'guest_user_carts';
  info: {
    displayName: 'Guest User Cart';
    pluralName: 'guest-user-carts';
    singularName: 'guest-user-cart';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    cart_items: Schema.Attribute.Relation<
      'oneToMany',
      'api::guest-user-cart-item.guest-user-cart-item'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customer: Schema.Attribute.Relation<'manyToOne', 'api::customer.customer'>;
    expire_at: Schema.Attribute.DateTime & Schema.Attribute.Required;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::guest-user-cart.guest-user-cart'
    >;
    publishedAt: Schema.Attribute.DateTime;
    session_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiImportFileLogImportFileLog
  extends Struct.CollectionTypeSchema {
  collectionName: 'import_file_logs';
  info: {
    displayName: 'Import File Log';
    pluralName: 'import-file-logs';
    singularName: 'import-file-log';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    data: Schema.Attribute.JSON;
    import_file_id: Schema.Attribute.Relation<
      'manyToOne',
      'api::import-file-state.import-file-state'
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::import-file-log.import-file-log'
    > &
      Schema.Attribute.Private;
    log_status: Schema.Attribute.Enumeration<['SUCCESS', 'FAILED']>;
    message: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    row_number: Schema.Attribute.Integer;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiImportFileStateImportFileState
  extends Struct.CollectionTypeSchema {
  collectionName: 'import_file_states';
  info: {
    displayName: 'Import File State';
    pluralName: 'import-file-states';
    singularName: 'import-file-state';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    completed_count: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    failed_count: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
    file_name: Schema.Attribute.String;
    file_size: Schema.Attribute.Integer;
    file_status: Schema.Attribute.Enumeration<
      ['IN_PROGRESS', 'DONE', 'FAILED']
    > &
      Schema.Attribute.DefaultTo<'IN_PROGRESS'>;
    file_type: Schema.Attribute.String;
    import_file_logs: Schema.Attribute.Relation<
      'oneToMany',
      'api::import-file-log.import-file-log'
    >;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::import-file-state.import-file-state'
    > &
      Schema.Attribute.Private;
    message: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    success_count: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
    table_name: Schema.Attribute.Enumeration<
      [
        'FG_CONTROL_MAIN',
        'FG_CUSTOMER_BUSINESS',
        'FG_PRODUCT_BUSINESS',
        'FG_RELATIONSHIP',
        'PRODUCT',
        'PRODUCT_MEDIA',
        'PRODUCT_SUGGESTION',
        'CRM_ACTIVITY',
        'CONTACT',
      ]
    >;
    total_count: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiPartnerFunctionConfigPartnerFunctionConfig
  extends Struct.CollectionTypeSchema {
  collectionName: 'partner_function_configs';
  info: {
    displayName: 'Partner Function Config';
    pluralName: 'partner-function-configs';
    singularName: 'partner-function-config';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    category: Schema.Attribute.Enumeration<
      ['APPOINTMENT', 'TASK', 'PHONE_CALL', 'EMAIL']
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::partner-function-config.partner-function-config'
    >;
    pf_code: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.Enumeration<
      [
        'ACTIVITY',
        'SALES_ORDER',
        'SALES_QUOTE',
        'ACCOUNT',
        'CONTACT',
        'PROSPECT',
        'OPPORTUNITY',
        'SERVICE_TICKET',
      ]
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiPermissionApiBridgePermissionApiBridge
  extends Struct.CollectionTypeSchema {
  collectionName: 'permission_api_bridges';
  info: {
    displayName: 'Permission API Bridge';
    pluralName: 'permission-api-bridges';
    singularName: 'permission-api-bridge';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    canCreate: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    canDelete: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    canRead: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    canUpdate: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::permission-api-bridge.permission-api-bridge'
    > &
      Schema.Attribute.Private;
    module: Schema.Attribute.String;
    moduleUID: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Relation<'manyToOne', 'admin::role'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiPermissionCrmPermissionCrm
  extends Struct.CollectionTypeSchema {
  collectionName: 'permission_crms';
  info: {
    displayName: 'Permission CRM';
    pluralName: 'permission-crms';
    singularName: 'permission-crm';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    admin_user_role: Schema.Attribute.Relation<'oneToMany', 'admin::role'>;
    code: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::permission-crm.permission-crm'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    user_role: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.role'
    >;
  };
}

export interface ApiPermissionPimPermissionPim
  extends Struct.CollectionTypeSchema {
  collectionName: 'permission_pims';
  info: {
    displayName: 'Permission PIM';
    pluralName: 'permission-pims';
    singularName: 'permission-pim';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    admin_user_role: Schema.Attribute.Relation<'oneToMany', 'admin::role'>;
    code: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::permission-pim.permission-pim'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    user_role: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.role'
    >;
  };
}

export interface ApiPermissionVendorPermissionVendor
  extends Struct.CollectionTypeSchema {
  collectionName: 'permission_vendors';
  info: {
    displayName: 'Permission Vendor';
    pluralName: 'permission-vendors';
    singularName: 'permission-vendor';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    admin_user_role: Schema.Attribute.Relation<'oneToMany', 'admin::role'>;
    code: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::permission-vendor.permission-vendor'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    user_role: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.role'
    >;
  };
}

export interface ApiProductBasicTextProductBasicText
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_basic_texts';
  info: {
    displayName: 'Product Basic Text';
    pluralName: 'product-basic-texts';
    singularName: 'product-basic-text';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    language: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-basic-text.product-basic-text'
    >;
    long_text: Schema.Attribute.Text;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductCatalogProductCatalog
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_catalogs';
  info: {
    displayName: 'Product Catalog';
    pluralName: 'product-catalogs';
    singularName: 'product-catalog';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    catalog_status: Schema.Attribute.Enumeration<['ACTIVE', 'INACTIVE']> &
      Schema.Attribute.DefaultTo<'INACTIVE'>;
    categories: Schema.Attribute.Relation<
      'manyToMany',
      'api::product-category.product-category'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    language: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-catalog.product-catalog'
    >;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    products: Schema.Attribute.Relation<'manyToMany', 'api::product.product'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    web_store: Schema.Attribute.String;
  };
}

export interface ApiProductCategoryProductCategory
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_categories';
  info: {
    description: '';
    displayName: 'Product Category';
    pluralName: 'product-categories';
    singularName: 'product-category';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    catalogs: Schema.Attribute.Relation<
      'manyToMany',
      'api::product-catalog.product-catalog'
    >;
    category_id: Schema.Attribute.String & Schema.Attribute.Unique;
    children: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-category.product-category'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-category.product-category'
    >;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    parent: Schema.Attribute.Relation<
      'manyToOne',
      'api::product-category.product-category'
    >;
    parent_category_id: Schema.Attribute.Integer;
    products: Schema.Attribute.Relation<'manyToMany', 'api::product.product'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    url: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
  };
}

export interface ApiProductCharcValueTypeProductCharcValueType
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_charc_value_types';
  info: {
    description: '';
    displayName: 'Product Charc Value Type';
    pluralName: 'product-charc-value-types';
    singularName: 'product-charc-value-type';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    change_number: Schema.Attribute.String;
    charc_from_amount: Schema.Attribute.String;
    charc_from_decimal_value: Schema.Attribute.String;
    charc_from_numeric_value: Schema.Attribute.String;
    charc_from_numeric_value_unit: Schema.Attribute.String;
    charc_internal: Schema.Attribute.Relation<
      'manyToOne',
      'api::product-class-charc-type.product-class-charc-type'
    >;
    charc_internal_id: Schema.Attribute.String;
    charc_to_amount: Schema.Attribute.String;
    charc_to_decimal_value: Schema.Attribute.String;
    charc_to_numeric_value: Schema.Attribute.String;
    charc_to_numeric_value_unit: Schema.Attribute.String;
    charc_value: Schema.Attribute.String;
    charc_value_dependency: Schema.Attribute.String;
    charc_value_position_number: Schema.Attribute.String;
    class_internal: Schema.Attribute.Relation<
      'manyToOne',
      'api::product-class-type.product-class-type'
    >;
    class_internal_id: Schema.Attribute.String;
    class_type: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    currency: Schema.Attribute.String;
    key_date: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-charc-value-type.product-charc-value-type'
    >;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductClassCharcTypeDescriptionProductClassCharcTypeDescription
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_class_charc_type_descriptions';
  info: {
    displayName: 'Product Class Charc Type Description';
    pluralName: 'product-class-charc-type-descriptions';
    singularName: 'product-class-charc-type-description';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    charc_internal: Schema.Attribute.Relation<
      'manyToOne',
      'api::product-class-charc-type.product-class-charc-type'
    >;
    charc_internal_id: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    language: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-class-charc-type-description.product-class-charc-type-description'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductClassCharcTypeProductClassCharcType
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_class_charc_types';
  info: {
    description: '';
    displayName: 'Product Class Charc Type';
    pluralName: 'product-class-charc-types';
    singularName: 'product-class-charc-type';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    characteristic: Schema.Attribute.String;
    charc_data_type: Schema.Attribute.String;
    charc_internal_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    charc_status: Schema.Attribute.String;
    charc_status_name: Schema.Attribute.String;
    charc_value_types: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-charc-value-type.product-charc-value-type'
    >;
    charc_value_unit: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    descriptions: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-class-charc-type-description.product-class-charc-type-description'
    >;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-class-charc-type.product-class-charc-type'
    >;
    publishedAt: Schema.Attribute.DateTime;
    unit_of_measure_iso_code: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductClassTypeDescriptionProductClassTypeDescription
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_class_type_descriptions';
  info: {
    displayName: 'Product Class Type Description';
    pluralName: 'product-class-type-descriptions';
    singularName: 'product-class-type-description';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    class_internal: Schema.Attribute.Relation<
      'manyToOne',
      'api::product-class-type.product-class-type'
    >;
    class_internal_id: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    language: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-class-type-description.product-class-type-description'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductClassTypeProductClassType
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_class_types';
  info: {
    displayName: 'Product Class Type';
    pluralName: 'product-class-types';
    singularName: 'product-class-type';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    charc_value_types: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-charc-value-type.product-charc-value-type'
    >;
    class: Schema.Attribute.String;
    class_internal_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    class_status: Schema.Attribute.String;
    class_status_name: Schema.Attribute.String;
    class_type: Schema.Attribute.String;
    class_type_name: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    descriptions: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-class-type-description.product-class-type-description'
    >;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-class-type.product-class-type'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductDescriptionProductDescription
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_descriptions';
  info: {
    displayName: 'Product Description';
    pluralName: 'product-descriptions';
    singularName: 'product-description';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    language: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-description.product-description'
    >;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductHierarchyStagingProductHierarchyStaging
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_hierarchy_stagings';
  info: {
    displayName: 'Product Hierarchy Staging';
    pluralName: 'product-hierarchy-stagings';
    singularName: 'product-hierarchy-staging';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    data: Schema.Attribute.JSON & Schema.Attribute.Required;
    error_message: Schema.Attribute.Text;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-hierarchy-staging.product-hierarchy-staging'
    >;
    publishedAt: Schema.Attribute.DateTime;
    staging_status: Schema.Attribute.Enumeration<
      ['PENDING', 'IN_PROCESS', 'FAILED']
    > &
      Schema.Attribute.DefaultTo<'PENDING'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductHierarchyProductHierarchy
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_hierarchies';
  info: {
    displayName: 'Product Hierarchy';
    pluralName: 'product-hierarchies';
    singularName: 'product-hierarchy';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    child_node: Schema.Attribute.String;
    children: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-hierarchy.product-hierarchy'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-hierarchy.product-hierarchy'
    >;
    parent: Schema.Attribute.Relation<
      'manyToOne',
      'api::product-hierarchy.product-hierarchy'
    >;
    parent_node: Schema.Attribute.String;
    product_id: Schema.Attribute.String & Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    root_node: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductMediaProductMedia
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_medias';
  info: {
    description: '';
    displayName: 'Product Media';
    pluralName: 'product-medias';
    singularName: 'product-media';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    code: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    dimension: Schema.Attribute.String;
    file_path: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    is_cover_image: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-media.product-media'
    >;
    media_name: Schema.Attribute.String;
    media_type: Schema.Attribute.Enumeration<
      ['PDF', 'IMAGE', 'VIDEO', 'SPECIFICATION']
    >;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    url: Schema.Attribute.String;
  };
}

export interface ApiProductPlantProcurementProductPlantProcurement
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_plant_procurements';
  info: {
    displayName: 'Product Plant Procurement';
    pluralName: 'product-plant-procurements';
    singularName: 'product-plant-procurement';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    is_auto_pur_ord_creation_allowed: Schema.Attribute.Boolean;
    is_source_list_required: Schema.Attribute.Boolean;
    itm_is_rlvt_to_jit_deliv_schedules: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-plant-procurement.product-plant-procurement'
    >;
    plant: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-plant.product-plant'
    >;
    plant_id: Schema.Attribute.String;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    source_of_supply_category: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductPlantSaleProductPlantSale
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_plant_sales';
  info: {
    displayName: 'Product Plant Sales';
    pluralName: 'product-plant-sales';
    singularName: 'product-plant-sale';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    cap_planning_quantity_in_base_uom: Schema.Attribute.Integer;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    loading_group: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-plant-sale.product-plant-sale'
    >;
    plant: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-plant.product-plant'
    >;
    plant_id: Schema.Attribute.String;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    product_shipping_processing_time: Schema.Attribute.Integer;
    publishedAt: Schema.Attribute.DateTime;
    replacement_part_type: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    wrk_centers_shipg_setup_time_in_days: Schema.Attribute.Integer;
  };
}

export interface ApiProductPlantStorageProductPlantStorage
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_plant_storages';
  info: {
    displayName: 'Product Plant Storage';
    pluralName: 'product-plant-storages';
    singularName: 'product-plant-storage';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    cycle_counting_indicator_is_fixed: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    inventory_for_cycle_count_ind: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-plant-storage.product-plant-storage'
    >;
    plant: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-plant.product-plant'
    >;
    plant_id: Schema.Attribute.String;
    prod_maximum_storage_period_unit: Schema.Attribute.String;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    provisioning_service_level: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    wrhs_mgmt_ptwy_and_stk_removal_strgy: Schema.Attribute.String;
  };
}

export interface ApiProductPlantTextProductPlantText
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_plant_texts';
  info: {
    displayName: 'Product Plant Text';
    pluralName: 'product-plant-texts';
    singularName: 'product-plant-text';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-plant-text.product-plant-text'
    >;
    long_text: Schema.Attribute.Text;
    plant: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-plant.product-plant'
    >;
    plant_id: Schema.Attribute.String;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductPlantProductPlant
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_plants';
  info: {
    displayName: 'Product Plant';
    pluralName: 'product-plants';
    singularName: 'product-plant';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    abc_indicator: Schema.Attribute.String;
    availability_check_type: Schema.Attribute.String;
    base_iso_unit: Schema.Attribute.String;
    base_unit: Schema.Attribute.String;
    commodity: Schema.Attribute.String;
    consumption_tax_ctrl_code: Schema.Attribute.String;
    country_of_origin: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    fiscal_month_current_period: Schema.Attribute.String;
    fiscal_year_current_period: Schema.Attribute.String;
    fiscal_year_variant: Schema.Attribute.String;
    fixed_lot_size_quantity: Schema.Attribute.Integer;
    goods_receipt_blocked_stock_qty: Schema.Attribute.Integer;
    goods_receipt_duration: Schema.Attribute.Integer;
    has_consignment_ctrl: Schema.Attribute.Boolean;
    has_post_to_inspection_stock: Schema.Attribute.Boolean;
    is_batch_management_required: Schema.Attribute.Boolean;
    is_co_product: Schema.Attribute.Boolean;
    is_internal_batch_managed: Schema.Attribute.Boolean;
    is_marked_for_deletion: Schema.Attribute.Boolean;
    is_negative_stock_allowed: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-plant.product-plant'
    >;
    maintenance_status_name: Schema.Attribute.String;
    maximum_lot_size_quantity: Schema.Attribute.Integer;
    minimum_lot_size_quantity: Schema.Attribute.Integer;
    mrp_responsible: Schema.Attribute.String;
    mrp_type: Schema.Attribute.String;
    period_type: Schema.Attribute.String;
    plant: Schema.Attribute.String;
    plant_procurement: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-plant-procurement.product-plant-procurement'
    >;
    plant_sale: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-plant-sale.product-plant-sale'
    >;
    plant_storage: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-plant-storage.product-plant-storage'
    >;
    plant_text: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-plant-text.product-plant-text'
    >;
    procurement_type: Schema.Attribute.String;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_cfop_category: Schema.Attribute.String;
    product_id: Schema.Attribute.String;
    product_is_configurable: Schema.Attribute.Boolean;
    product_is_excise_tax_relevant: Schema.Attribute.Boolean;
    production_invtry_managed_loc: Schema.Attribute.String;
    profile_code: Schema.Attribute.String;
    profile_validity_start_date: Schema.Attribute.DateTime;
    profit_center: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    purchasing_group: Schema.Attribute.String;
    region_of_origin: Schema.Attribute.String;
    serial_number_profile: Schema.Attribute.String;
    stock_determination_group: Schema.Attribute.String;
    stock_in_transfer_quantity: Schema.Attribute.Integer;
    stock_in_transit_quantity: Schema.Attribute.Integer;
    storage_locations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-storage-location.product-storage-location'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductSaleProductSale extends Struct.CollectionTypeSchema {
  collectionName: 'product_sales';
  info: {
    displayName: 'Product Sales';
    pluralName: 'product-sales';
    singularName: 'product-sale';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-sale.product-sale'
    >;
    product: Schema.Attribute.Relation<'oneToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    sales_status: Schema.Attribute.String;
    sales_status_validity_date: Schema.Attribute.DateTime;
    tax_classification: Schema.Attribute.String;
    transportation_group: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductSalesDeliveryProductSalesDelivery
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_sales_deliveries';
  info: {
    displayName: 'Product Sales Delivery';
    pluralName: 'product-sales-deliveries';
    singularName: 'product-sales-delivery';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    account_detn_product_group: Schema.Attribute.String;
    cash_discount_is_deductible: Schema.Attribute.Boolean;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    delivery_note_proc_min_deliv_qty: Schema.Attribute.String;
    delivery_quantity: Schema.Attribute.String;
    delivery_quantity_unit: Schema.Attribute.String;
    fifth_sales_spec_product_group: Schema.Attribute.String;
    first_sales_spec_product_group: Schema.Attribute.String;
    fourth_sales_spec_product_group: Schema.Attribute.String;
    is_marked_for_deletion: Schema.Attribute.Boolean;
    item_category_group: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-sales-delivery.product-sales-delivery'
    >;
    logistics_statistics_group: Schema.Attribute.String;
    minimum_make_to_order_order_qty: Schema.Attribute.String;
    minimum_order_quantity: Schema.Attribute.String;
    price_specification_product_group: Schema.Attribute.String;
    pricing_reference_product: Schema.Attribute.String;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_commission_group: Schema.Attribute.String;
    product_distribution_chnl: Schema.Attribute.String;
    product_has_attribute_id_01: Schema.Attribute.Boolean;
    product_has_attribute_id_02: Schema.Attribute.Boolean;
    product_has_attribute_id_03: Schema.Attribute.Boolean;
    product_has_attribute_id_04: Schema.Attribute.Boolean;
    product_has_attribute_id_05: Schema.Attribute.Boolean;
    product_has_attribute_id_06: Schema.Attribute.Boolean;
    product_has_attribute_id_07: Schema.Attribute.Boolean;
    product_has_attribute_id_08: Schema.Attribute.Boolean;
    product_has_attribute_id_09: Schema.Attribute.Boolean;
    product_has_attribute_id_10: Schema.Attribute.Boolean;
    product_hierarchy: Schema.Attribute.String;
    product_id: Schema.Attribute.String;
    product_sales_org: Schema.Attribute.String;
    product_sales_status: Schema.Attribute.String;
    product_sales_status_validity_date: Schema.Attribute.DateTime;
    product_unit_group: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    rounding_profile: Schema.Attribute.String;
    sales_measure_unit: Schema.Attribute.String;
    sales_text: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-sales-text.product-sales-text'
    >;
    second_sales_spec_product_group: Schema.Attribute.String;
    supplying_plant: Schema.Attribute.String;
    third_sales_spec_product_group: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    variable_sales_unit_is_not_allowed: Schema.Attribute.Boolean;
    volume_rebate_group: Schema.Attribute.String;
  };
}

export interface ApiProductSalesTaxProductSalesTax
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_sales_taxes';
  info: {
    displayName: 'Product Sales Tax';
    pluralName: 'product-sales-taxes';
    singularName: 'product-sales-tax';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    country: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-sales-tax.product-sales-tax'
    >;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    tax_category: Schema.Attribute.String;
    tax_classification: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductSalesTextProductSalesText
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_sales_texts';
  info: {
    displayName: 'Product Sales Text';
    pluralName: 'product-sales-texts';
    singularName: 'product-sales-text';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    language: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-sales-text.product-sales-text'
    >;
    long_text: Schema.Attribute.Text;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    product_sales_org: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    sales_delivery: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-sales-delivery.product-sales-delivery'
    >;
    sales_status_validity_date: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductStagingProductStaging
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_stagings';
  info: {
    displayName: 'Product Staging';
    pluralName: 'product-stagings';
    singularName: 'product-staging';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    data: Schema.Attribute.JSON & Schema.Attribute.Required;
    error_message: Schema.Attribute.Text;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-staging.product-staging'
    >;
    publishedAt: Schema.Attribute.DateTime;
    staging_status: Schema.Attribute.Enumeration<
      ['PENDING', 'IN_PROCESS', 'FAILED']
    > &
      Schema.Attribute.DefaultTo<'PENDING'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductStorageLocationProductStorageLocation
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_storage_locations';
  info: {
    displayName: 'Product Storage Location';
    pluralName: 'product-storage-locations';
    singularName: 'product-storage-location';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    creation_date: Schema.Attribute.DateTime;
    date_of_last_posted_cnt_unrstrcd_stk: Schema.Attribute.DateTime;
    fiscal_year_current_invtry_period: Schema.Attribute.String;
    fiscal_year_current_period: Schema.Attribute.String;
    has_invtry_block_stock_prev_period: Schema.Attribute.Boolean;
    inventory_block_stock_ind: Schema.Attribute.Boolean;
    inventory_stock_prev_period: Schema.Attribute.Boolean;
    invtry_current_year_stock_ind: Schema.Attribute.Boolean;
    invtry_qual_insp_current_yr_stk_ind: Schema.Attribute.Boolean;
    invtry_rest_stock_prev_period_ind: Schema.Attribute.Boolean;
    invtry_restricted_use_stock_ind: Schema.Attribute.Boolean;
    invtry_stock_qlty_insp_prev_period: Schema.Attribute.Boolean;
    is_marked_for_deletion: Schema.Attribute.Boolean;
    lean_wrhs_management_picking_area: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-storage-location.product-storage-location'
    >;
    maintenance_status: Schema.Attribute.String;
    physical_inventory_block_ind: Schema.Attribute.Boolean;
    plant: Schema.Attribute.Relation<
      'manyToOne',
      'api::product-plant.product-plant'
    >;
    plant_id: Schema.Attribute.String;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    storage_location: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    warehouse_storage_bin: Schema.Attribute.String;
  };
}

export interface ApiProductStorageProductStorage
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_storages';
  info: {
    displayName: 'Product Storage';
    pluralName: 'product-storages';
    singularName: 'product-storage';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    expiration_date: Schema.Attribute.DateTime;
    hazardous_material_number: Schema.Attribute.String;
    label_form: Schema.Attribute.String;
    label_type: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-storage.product-storage'
    >;
    min_remaining_shelf_life: Schema.Attribute.Integer;
    nmbr_of_gr_or_gi_slips_to_print_qty: Schema.Attribute.Integer;
    product: Schema.Attribute.Relation<'oneToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    storage_conditions: Schema.Attribute.String;
    temperature_condition_ind: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductSuggestionTypeProductSuggestionType
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_suggestion_types';
  info: {
    displayName: 'Product Suggestion Type';
    pluralName: 'product-suggestion-types';
    singularName: 'product-suggestion-type';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-suggestion-type.product-suggestion-type'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductSuggestionProductSuggestion
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_suggestions';
  info: {
    displayName: 'Product Suggestion';
    pluralName: 'product-suggestions';
    singularName: 'product-suggestion';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-suggestion.product-suggestion'
    >;
    product_id: Schema.Attribute.String;
    product_suggested: Schema.Attribute.Relation<
      'manyToOne',
      'api::product.product'
    >;
    product_suggested_id: Schema.Attribute.String;
    product_suggestion_type_id: Schema.Attribute.Relation<
      'manyToOne',
      'api::product-suggestion-type.product-suggestion-type'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductUnitsOfMeasureEanProductUnitsOfMeasureEan
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_units_of_measure_eans';
  info: {
    displayName: 'Product Units Of Measure EAN';
    pluralName: 'product-units-of-measure-eans';
    singularName: 'product-units-of-measure-ean';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    alternative_unit: Schema.Attribute.String;
    consecutive_number: Schema.Attribute.Integer;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    international_article_number_cat: Schema.Attribute.String;
    is_main_global_trade_item_number: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-units-of-measure-ean.product-units-of-measure-ean'
    >;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    product_standard_id: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    units_of_measure: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-units-of-measure.product-units-of-measure'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiProductUnitsOfMeasureProductUnitsOfMeasure
  extends Struct.CollectionTypeSchema {
  collectionName: 'product_units_of_measures';
  info: {
    displayName: 'Product Units Of Measure';
    pluralName: 'product-units-of-measures';
    singularName: 'product-units-of-measure';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    alternative_unit: Schema.Attribute.String;
    capacity_usage: Schema.Attribute.Decimal;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    global_trade_item_number: Schema.Attribute.String;
    global_trade_item_number_category: Schema.Attribute.String;
    gross_weight: Schema.Attribute.Decimal;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-units-of-measure.product-units-of-measure'
    >;
    lower_level_packaging_unit: Schema.Attribute.String;
    material_volume: Schema.Attribute.Decimal;
    maximum_stacking_factor: Schema.Attribute.Integer;
    product: Schema.Attribute.Relation<'manyToOne', 'api::product.product'>;
    product_id: Schema.Attribute.String;
    product_measurement_unit: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    quantity_denominator: Schema.Attribute.Integer;
    quantity_numerator: Schema.Attribute.Integer;
    remaining_volume_after_nesting: Schema.Attribute.Decimal;
    unit_specific_product_height: Schema.Attribute.Decimal;
    unit_specific_product_length: Schema.Attribute.Decimal;
    unit_specific_product_width: Schema.Attribute.Decimal;
    units_of_measure_ean: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-units-of-measure-ean.product-units-of-measure-ean'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    volume_unit: Schema.Attribute.String;
    weight_unit: Schema.Attribute.String;
  };
}

export interface ApiProductProduct extends Struct.CollectionTypeSchema {
  collectionName: 'products';
  info: {
    description: '';
    displayName: 'Product';
    pluralName: 'products';
    singularName: 'product';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    adaCompliant: Schema.Attribute.Boolean;
    ahVariantType: Schema.Attribute.String;
    amperage: Schema.Attribute.String;
    anp_code: Schema.Attribute.String;
    applicableMaterial: Schema.Attribute.String;
    approvalStatus: Schema.Attribute.String;
    averageCoveringArea: Schema.Attribute.String;
    base_iso_unit: Schema.Attribute.String;
    base_iso_unit_desc: Schema.Attribute.String;
    base_unit: Schema.Attribute.String;
    base_unit_desc: Schema.Attribute.String;
    basic_texts: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-basic-text.product-basic-text'
    >;
    brand: Schema.Attribute.String;
    brandCode: Schema.Attribute.String;
    brandName: Schema.Attribute.String;
    capacity: Schema.Attribute.String;
    casePack: Schema.Attribute.Integer;
    catalogs: Schema.Attribute.Relation<
      'manyToMany',
      'api::product-catalog.product-catalog'
    >;
    categories: Schema.Attribute.Relation<
      'manyToMany',
      'api::product-category.product-category'
    >;
    certification: Schema.Attribute.String;
    charc_value_types: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-charc-value-type.product-charc-value-type'
    >;
    cleaner: Schema.Attribute.String;
    cleaningPath: Schema.Attribute.String;
    closureType: Schema.Attribute.String;
    coating: Schema.Attribute.String;
    collectionCode: Schema.Attribute.String;
    collectionName: Schema.Attribute.String;
    color: Schema.Attribute.String;
    competitor_id: Schema.Attribute.String;
    concentrated: Schema.Attribute.Boolean;
    coolBTU: Schema.Attribute.Integer;
    cordLength: Schema.Attribute.Decimal;
    country_of_origin: Schema.Attribute.String;
    created_by_user: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    creation_date: Schema.Attribute.DateTime;
    creationSystem: Schema.Attribute.String;
    crm_competitor_products: Schema.Attribute.Relation<
      'oneToMany',
      'api::crm-competitor-product.crm-competitor-product'
    >;
    cross_plant_status: Schema.Attribute.String;
    cross_plant_status_validity_date: Schema.Attribute.DateTime;
    culApproved: Schema.Attribute.Boolean;
    density: Schema.Attribute.String;
    description: Schema.Attribute.Text;
    descriptions: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-description.product-description'
    >;
    distri_channel_desc: Schema.Attribute.String;
    distri_channel_id: Schema.Attribute.String;
    diversityCertificate: Schema.Attribute.String;
    diversitySupplier: Schema.Attribute.Boolean;
    division_desc: Schema.Attribute.String;
    division_id: Schema.Attribute.String;
    doorStyle: Schema.Attribute.String;
    downloadUrl: Schema.Attribute.String;
    dropShip: Schema.Attribute.Boolean;
    ean: Schema.Attribute.String;
    eel: Schema.Attribute.String;
    energyStar: Schema.Attribute.Boolean;
    fiberContent: Schema.Attribute.String;
    fillType: Schema.Attribute.String;
    fillWeight: Schema.Attribute.String;
    filter: Schema.Attribute.String;
    flavor: Schema.Attribute.String;
    fscCertified: Schema.Attribute.Boolean;
    gauge: Schema.Attribute.String;
    greenSealCertified: Schema.Attribute.Boolean;
    gross_weight: Schema.Attribute.Decimal;
    gsm: Schema.Attribute.Integer;
    guage: Schema.Attribute.String;
    guid: Schema.Attribute.String;
    handling_unit_type: Schema.Attribute.String;
    has_variable_tare_weight: Schema.Attribute.Boolean;
    hasInstructions: Schema.Attribute.Boolean;
    hasPersonalization: Schema.Attribute.Boolean;
    hazmatCode: Schema.Attribute.String;
    heat: Schema.Attribute.Boolean;
    heatBTU: Schema.Attribute.Integer;
    hemColor: Schema.Attribute.String;
    hertz: Schema.Attribute.String;
    hetz: Schema.Attribute.Integer;
    hoseLength: Schema.Attribute.Decimal;
    howToVideoUrl: Schema.Attribute.String;
    industry_standard_name: Schema.Attribute.String;
    is_batch_management_required: Schema.Attribute.Boolean;
    is_deleted: Schema.Attribute.Boolean;
    is_marked_for_deletion: Schema.Attribute.Boolean;
    is_pilferable: Schema.Attribute.Boolean;
    is_relevant_for_hzds_substances: Schema.Attribute.Boolean;
    item_category_group: Schema.Attribute.String;
    item_category_group_desc: Schema.Attribute.String;
    itemsPerSalesUnit: Schema.Attribute.String;
    last_change_date: Schema.Attribute.DateTime;
    last_change_date_time: Schema.Attribute.DateTime;
    last_changed_by_user: Schema.Attribute.String;
    lbsPerDozen: Schema.Attribute.Decimal;
    livingGreen: Schema.Attribute.Boolean;
    livingGreenLabel: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product.product'
    >;
    low_level_code: Schema.Attribute.String;
    madeInUsa: Schema.Attribute.Boolean;
    manufacturerName: Schema.Attribute.String;
    manufacturerPartNumber: Schema.Attribute.String;
    marginDecile: Schema.Attribute.Integer;
    material: Schema.Attribute.String;
    material_volume: Schema.Attribute.Decimal;
    maximum_packaging_height: Schema.Attribute.Decimal;
    maximum_packaging_length: Schema.Attribute.Decimal;
    maximum_packaging_width: Schema.Attribute.Decimal;
    medias: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-media.product-media'
    >;
    minOrderQuantity: Schema.Attribute.Integer;
    mount: Schema.Attribute.String;
    name: Schema.Attribute.String;
    net_weight: Schema.Attribute.Decimal;
    noiseLevel: Schema.Attribute.String;
    numberOfPlys: Schema.Attribute.Integer;
    numberOfShelves: Schema.Attribute.Integer;
    oekoTex: Schema.Attribute.Boolean;
    orderCount: Schema.Attribute.Integer;
    orderDecile: Schema.Attribute.Integer;
    originCountry: Schema.Attribute.String;
    outlet: Schema.Attribute.String;
    parent_product: Schema.Attribute.Relation<
      'oneToOne',
      'api::product.product'
    >;
    parentProduct: Schema.Attribute.String;
    pdpCmsId: Schema.Attribute.String;
    personalizationText: Schema.Attribute.String;
    pfas: Schema.Attribute.Boolean;
    piecePerSet: Schema.Attribute.Integer;
    plant_desc: Schema.Attribute.String;
    plant_id: Schema.Attribute.String;
    plant_procurement: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-plant-procurement.product-plant-procurement'
    >;
    plant_sale: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-plant-sale.product-plant-sale'
    >;
    plant_storage: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-plant-storage.product-plant-storage'
    >;
    plant_text: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-plant-text.product-plant-text'
    >;
    plants: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-plant.product-plant'
    >;
    plugType: Schema.Attribute.String;
    pocketDepth: Schema.Attribute.String;
    powerSource: Schema.Attribute.String;
    privateLabel: Schema.Attribute.Boolean;
    procurement_rule: Schema.Attribute.String;
    product_businesses: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-product-business.fg-product-business'
    >;
    product_desc: Schema.Attribute.String;
    product_group: Schema.Attribute.String;
    product_group_desc: Schema.Attribute.String;
    product_hierarchy: Schema.Attribute.String;
    product_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    product_internals: Schema.Attribute.Relation<
      'oneToMany',
      'api::fg-product-internal.fg-product-internal'
    >;
    product_is_configurable: Schema.Attribute.Boolean;
    product_old_id: Schema.Attribute.String;
    product_old_id_desc: Schema.Attribute.String;
    product_standard_id: Schema.Attribute.String;
    product_status: Schema.Attribute.Enumeration<['ACTIVE', 'INACTIVE']> &
      Schema.Attribute.DefaultTo<'INACTIVE'>;
    product_summary: Schema.Attribute.Text;
    product_type_desc: Schema.Attribute.String;
    productForm: Schema.Attribute.String;
    productHeight: Schema.Attribute.Decimal;
    productLength: Schema.Attribute.Decimal;
    productType: Schema.Attribute.String;
    productWeight: Schema.Attribute.Decimal;
    productWidth: Schema.Attribute.Decimal;
    prop65: Schema.Attribute.Boolean;
    prop65Chemical: Schema.Attribute.String;
    prop65ChemType: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    purchase_order_quantity_unit: Schema.Attribute.String;
    quality_inspection_group: Schema.Attribute.String;
    quarantine_period: Schema.Attribute.Integer;
    refigerantType: Schema.Attribute.String;
    refrigerantType: Schema.Attribute.String;
    registryBrand: Schema.Attribute.String;
    sale: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-sale.product-sale'
    >;
    sales_deliveries: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-sales-delivery.product-sales-delivery'
    >;
    sales_org_desc: Schema.Attribute.String;
    sales_org_id: Schema.Attribute.String;
    sales_taxes: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-sales-tax.product-sales-tax'
    >;
    sales_texts: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-sales-text.product-sales-text'
    >;
    salesUnit: Schema.Attribute.String;
    sapBlocked: Schema.Attribute.Boolean;
    sapBlockedDate: Schema.Attribute.Date;
    sapEAN: Schema.Attribute.String;
    sapHierarchy1: Schema.Attribute.String;
    sapHierarchy2: Schema.Attribute.String;
    sapHierarchy3: Schema.Attribute.String;
    sapHierarchy4: Schema.Attribute.String;
    sapHierarchy5: Schema.Attribute.String;
    scent: Schema.Attribute.String;
    sds: Schema.Attribute.String;
    sealType: Schema.Attribute.String;
    serial_number_profile: Schema.Attribute.String;
    shape: Schema.Attribute.String;
    sheetCount: Schema.Attribute.Integer;
    shippingHeight: Schema.Attribute.Decimal;
    shippingLength: Schema.Attribute.Decimal;
    shippingUnit: Schema.Attribute.String;
    shippingWeight: Schema.Attribute.Decimal;
    shippingWidth: Schema.Attribute.Decimal;
    size: Schema.Attribute.String;
    size_or_dimension_text: Schema.Attribute.String;
    slug: Schema.Attribute.UID<'name'>;
    source_of_supply: Schema.Attribute.String;
    specification: Schema.Attribute.Text;
    stock: Schema.Attribute.String;
    storage: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-storage.product-storage'
    >;
    storage_locations: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-storage-location.product-storage-location'
    >;
    style: Schema.Attribute.String;
    suggestions: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-suggestion.product-suggestion'
    >;
    threadCount: Schema.Attribute.Integer;
    time_unit_for_quarantine_period: Schema.Attribute.String;
    truckOnly: Schema.Attribute.Boolean;
    ulApproved: Schema.Attribute.Boolean;
    unit: Schema.Attribute.String;
    unit_measure: Schema.Attribute.String;
    units_of_measure_ean: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-units-of-measure-ean.product-units-of-measure-ean'
    >;
    units_of_measures: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-units-of-measure.product-units-of-measure'
    >;
    unspscCode: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    validity_start_date: Schema.Attribute.DateTime;
    variable_purchase_order_unit_is_active: Schema.Attribute.Boolean;
    variantPlpName: Schema.Attribute.String;
    variantSort: Schema.Attribute.Integer;
    variantType: Schema.Attribute.String;
    vcClass: Schema.Attribute.String;
    vcGroup: Schema.Attribute.String;
    videoDescription: Schema.Attribute.Text;
    videoTitle: Schema.Attribute.String;
    videoUrl: Schema.Attribute.String;
    virtualInventory: Schema.Attribute.Boolean;
    voltage: Schema.Attribute.Integer;
    volume_unit: Schema.Attribute.String;
    warranty: Schema.Attribute.String;
    warrantyType: Schema.Attribute.String;
    wattage: Schema.Attribute.Integer;
    weight_unit: Schema.Attribute.String;
  };
}

export interface ApiPromotionApplicationMethodPromotionApplicationMethod
  extends Struct.CollectionTypeSchema {
  collectionName: 'promotion_application_methods';
  info: {
    displayName: 'Promotion Application Method';
    pluralName: 'promotion-application-methods';
    singularName: 'promotion-application-method';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    allocation: Schema.Attribute.Enumeration<['EACH', 'ACROSS']>;
    apply_to_quantity: Schema.Attribute.Integer;
    buy_rules: Schema.Attribute.Relation<
      'manyToMany',
      'api::promotion-rule.promotion-rule'
    >;
    buy_rules_min_quantity: Schema.Attribute.Integer;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    currency_code: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::promotion-application-method.promotion-application-method'
    >;
    max_quantity: Schema.Attribute.Integer;
    promotion: Schema.Attribute.Relation<
      'manyToOne',
      'api::promotion.promotion'
    >;
    publishedAt: Schema.Attribute.DateTime;
    target_rules: Schema.Attribute.Relation<
      'manyToMany',
      'api::promotion-rule.promotion-rule'
    >;
    target_type: Schema.Attribute.Enumeration<
      ['CART_ITEMS', 'SHIPPING_METHODS', 'ORDER']
    > &
      Schema.Attribute.Required;
    type: Schema.Attribute.Enumeration<['FIXED', 'PERCENTAGE']> &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'FIXED'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    value: Schema.Attribute.Decimal;
  };
}

export interface ApiPromotionRuleValuePromotionRuleValue
  extends Struct.CollectionTypeSchema {
  collectionName: 'promotion_rule_values';
  info: {
    displayName: 'Promotion Rule Value';
    pluralName: 'promotion-rule-values';
    singularName: 'promotion-rule-value';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::promotion-rule-value.promotion-rule-value'
    >;
    promotion_rule: Schema.Attribute.Relation<
      'manyToOne',
      'api::promotion-rule.promotion-rule'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    value: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface ApiPromotionRulePromotionRule
  extends Struct.CollectionTypeSchema {
  collectionName: 'promotion_rules';
  info: {
    displayName: 'Promotion Rule';
    pluralName: 'promotion-rules';
    singularName: 'promotion-rule';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    attribute: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.Text;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::promotion-rule.promotion-rule'
    >;
    method_buy_rules: Schema.Attribute.Relation<
      'manyToMany',
      'api::promotion-application-method.promotion-application-method'
    >;
    method_target_rules: Schema.Attribute.Relation<
      'manyToMany',
      'api::promotion-application-method.promotion-application-method'
    >;
    operator: Schema.Attribute.Enumeration<
      ['GTE', 'LTE', 'GT', 'LT', 'EQ', 'NE', 'IN']
    > &
      Schema.Attribute.Required;
    promotions: Schema.Attribute.Relation<
      'manyToMany',
      'api::promotion.promotion'
    >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    values: Schema.Attribute.Relation<
      'oneToMany',
      'api::promotion-rule-value.promotion-rule-value'
    >;
  };
}

export interface ApiPromotionPromotion extends Struct.CollectionTypeSchema {
  collectionName: 'promotions';
  info: {
    displayName: 'Promotion';
    pluralName: 'promotions';
    singularName: 'promotion';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    application_method: Schema.Attribute.Relation<
      'oneToOne',
      'api::promotion-application-method.promotion-application-method'
    >;
    code: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    is_automatic: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::promotion.promotion'
    >;
    publishedAt: Schema.Attribute.DateTime;
    rules: Schema.Attribute.Relation<
      'manyToMany',
      'api::promotion-rule.promotion-rule'
    >;
    type: Schema.Attribute.Enumeration<
      ['CART_DISCOUNT', 'BUY_X_GET_Y', 'FREE_SHIPPING']
    > &
      Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiSchedulerScheduler extends Struct.CollectionTypeSchema {
  collectionName: 'schedulers';
  info: {
    displayName: 'Scheduler';
    pluralName: 'schedulers';
    singularName: 'scheduler';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    day_of_month: Schema.Attribute.Integer &
      Schema.Attribute.SetMinMax<
        {
          max: 31;
          min: 1;
        },
        number
      >;
    end_date: Schema.Attribute.Date & Schema.Attribute.Required;
    is_active: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::scheduler.scheduler'
    > &
      Schema.Attribute.Private;
    operation: Schema.Attribute.Enumeration<
      [
        'FG_CUSTOMER_BUSINESS',
        'FG_PRODUCT_BUSINESS',
        'FG_CONTROL_MAIN_FEED',
        'PRODUCT_WEB_ATTRIBUTE_FEED',
        'PRODUCT_SPECIFICATION_FEED',
        'PRODUCT_MEDIA_FEED',
      ]
    > &
      Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    schedule_type: Schema.Attribute.Enumeration<
      ['DAILY', 'WEEKLY', 'MONTHLY']
    > &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'DAILY'>;
    start_date: Schema.Attribute.Date & Schema.Attribute.Required;
    time_of_day: Schema.Attribute.String & Schema.Attribute.DefaultTo<'00:00'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    weekdays_to_generate: Schema.Attribute.String &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 50;
      }>;
  };
}

export interface ApiSecurityQuestionSecurityQuestion
  extends Struct.CollectionTypeSchema {
  collectionName: 'security_questions';
  info: {
    displayName: 'Security Question';
    pluralName: 'security-questions';
    singularName: 'security-question';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::security-question.security-question'
    >;
    publishedAt: Schema.Attribute.DateTime;
    question: Schema.Attribute.String &
      Schema.Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiSettingSetting extends Struct.CollectionTypeSchema {
  collectionName: 'settings';
  info: {
    displayName: 'Setting';
    pluralName: 'settings';
    singularName: 'setting';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    base_price_code: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    crm_admin_user_emails: Schema.Attribute.JSON;
    cust_return_type_code: Schema.Attribute.String;
    cust_return_type_descr: Schema.Attribute.String;
    discount_code: Schema.Attribute.String;
    global_miscellaneous_code: Schema.Attribute.String;
    global_miscellaneous_descr: Schema.Attribute.String;
    global_po_code: Schema.Attribute.String;
    global_po_descr: Schema.Attribute.String;
    guest_user_customer: Schema.Attribute.String;
    is_fgci_in_progress: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    is_fgpi_in_progress: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::setting.setting'
    >;
    low_stock_qty: Schema.Attribute.Integer;
    min_quote_price: Schema.Attribute.Integer;
    publishedAt: Schema.Attribute.DateTime;
    quote_text_code: Schema.Attribute.String;
    quote_text_descr: Schema.Attribute.String;
    sales_quote_type_code: Schema.Attribute.String;
    sales_quote_type_descr: Schema.Attribute.String;
    shipping_code: Schema.Attribute.String;
    tax_code: Schema.Attribute.String;
    text_code: Schema.Attribute.String;
    text_descr: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    vendor_admin_user_emails: Schema.Attribute.JSON;
    vendor_user_role: Schema.Attribute.Relation<
      'oneToOne',
      'plugin::users-permissions.role'
    >;
  };
}

export interface ApiSupplierCompanyTextSupplierCompanyText
  extends Struct.CollectionTypeSchema {
  collectionName: 'supplier_company_texts';
  info: {
    displayName: 'Supplier Company Text';
    pluralName: 'supplier-company-texts';
    singularName: 'supplier-company-text';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    company: Schema.Attribute.Relation<
      'manyToOne',
      'api::supplier-company.supplier-company'
    >;
    company_code: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    language: Schema.Attribute.String & Schema.Attribute.Required;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier-company-text.supplier-company-text'
    >;
    long_text: Schema.Attribute.Text;
    long_text_id: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    supplier: Schema.Attribute.Relation<'manyToOne', 'api::supplier.supplier'>;
    supplier_id: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiSupplierCompanySupplierCompany
  extends Struct.CollectionTypeSchema {
  collectionName: 'supplier_companies';
  info: {
    displayName: 'Supplier Company';
    pluralName: 'supplier-companies';
    singularName: 'supplier-company';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    accounting_clerk: Schema.Attribute.String;
    accounting_clerk_fax_number: Schema.Attribute.String;
    accounting_clerk_phone_number: Schema.Attribute.String;
    alternative_payee: Schema.Attribute.String;
    apar_tolerance_group: Schema.Attribute.String;
    authorization_group: Schema.Attribute.String;
    bill_of_exch_lmt_amt_in_co_code_crcy: Schema.Attribute.Decimal;
    cash_planning_group: Schema.Attribute.String;
    check_paid_duration_in_days: Schema.Attribute.Integer;
    clear_customer_supplier: Schema.Attribute.Boolean;
    company_code: Schema.Attribute.String & Schema.Attribute.Required;
    company_code_name: Schema.Attribute.String;
    company_texts: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier-company-text.supplier-company-text'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    currency: Schema.Attribute.String;
    deletion_indicator: Schema.Attribute.Boolean;
    house_bank: Schema.Attribute.String;
    interest_calculation_code: Schema.Attribute.String;
    interest_calculation_date: Schema.Attribute.DateTime;
    intrst_calc_frequency_in_months: Schema.Attribute.Integer;
    is_to_be_checked_for_duplicates: Schema.Attribute.Boolean;
    is_to_be_locally_processed: Schema.Attribute.Boolean;
    item_is_to_be_paid_separately: Schema.Attribute.Boolean;
    last_interest_calc_run_date: Schema.Attribute.DateTime;
    layout_sorting_rule: Schema.Attribute.String;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier-company.supplier-company'
    >;
    minority_group: Schema.Attribute.String;
    payment_blocking_reason: Schema.Attribute.String;
    payment_is_to_be_sent_by_edi: Schema.Attribute.Boolean;
    payment_methods_list: Schema.Attribute.String;
    payment_reason: Schema.Attribute.String;
    payment_terms: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    reconciliation_account: Schema.Attribute.String;
    supplier: Schema.Attribute.Relation<'manyToOne', 'api::supplier.supplier'>;
    supplier_account_group: Schema.Attribute.String;
    supplier_account_note: Schema.Attribute.Text;
    supplier_certification_date: Schema.Attribute.DateTime;
    supplier_clerk: Schema.Attribute.String;
    supplier_clerk_id_by_supplier: Schema.Attribute.String;
    supplier_clerk_url: Schema.Attribute.String;
    supplier_head_office: Schema.Attribute.String;
    supplier_id: Schema.Attribute.String & Schema.Attribute.Required;
    supplier_is_blocked_for_posting: Schema.Attribute.Boolean;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    withholding_tax_country: Schema.Attribute.String;
    withholding_tax_country_code: Schema.Attribute.String;
  };
}

export interface ApiSupplierPartnerFuncSupplierPartnerFunc
  extends Struct.CollectionTypeSchema {
  collectionName: 'supplier_partner_funcs';
  info: {
    displayName: 'Supplier Purchasing Partner Functions';
    pluralName: 'supplier-partner-funcs';
    singularName: 'supplier-partner-func';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    authorization_group: Schema.Attribute.String;
    created_by_user: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    creation_date: Schema.Attribute.DateTime;
    default_partner: Schema.Attribute.Boolean &
      Schema.Attribute.DefaultTo<false>;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier-partner-func.supplier-partner-func'
    >;
    partner_counter: Schema.Attribute.Integer & Schema.Attribute.Required;
    partner_function: Schema.Attribute.String & Schema.Attribute.Required;
    plant: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    purchasing_organization: Schema.Attribute.String &
      Schema.Attribute.Required;
    reference_supplier: Schema.Attribute.String;
    supplier: Schema.Attribute.Relation<'manyToOne', 'api::supplier.supplier'>;
    supplier_id: Schema.Attribute.String & Schema.Attribute.Required;
    supplier_subrange: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiSupplierPurchasingOrgTextSupplierPurchasingOrgText
  extends Struct.CollectionTypeSchema {
  collectionName: 'supplier_purchasing_org_texts';
  info: {
    displayName: 'Supplier Purchasing Organization Text';
    pluralName: 'supplier-purchasing-org-texts';
    singularName: 'supplier-purchasing-org-text';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    language: Schema.Attribute.String & Schema.Attribute.Required;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier-purchasing-org-text.supplier-purchasing-org-text'
    >;
    long_text: Schema.Attribute.Text;
    long_text_id: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    purchasing_organization: Schema.Attribute.String &
      Schema.Attribute.Required;
    supplier_id: Schema.Attribute.String & Schema.Attribute.Required;
    supplier_purchasing_org: Schema.Attribute.Relation<
      'manyToOne',
      'api::supplier-purchasing-org.supplier-purchasing-org'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiSupplierPurchasingOrgSupplierPurchasingOrg
  extends Struct.CollectionTypeSchema {
  collectionName: 'supplier_purchasing_orgs';
  info: {
    displayName: 'Supplier Purchasing Organization';
    pluralName: 'supplier-purchasing-orgs';
    singularName: 'supplier-purchasing-org';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    authorization_group: Schema.Attribute.String;
    automatic_evaluated_rcpt_settlmt: Schema.Attribute.Boolean;
    calculation_schema_group_code: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    deletion_indicator: Schema.Attribute.Boolean;
    evald_receipt_settlement_is_active: Schema.Attribute.Boolean;
    incoterms_classification: Schema.Attribute.String;
    incoterms_location1: Schema.Attribute.String;
    incoterms_location2: Schema.Attribute.String;
    incoterms_sup_chn_dvtg_loc_addl_uuid: Schema.Attribute.String;
    incoterms_sup_chn_loc1_addl_uuid: Schema.Attribute.String;
    incoterms_sup_chn_loc2_addl_uuid: Schema.Attribute.String;
    incoterms_transfer_location: Schema.Attribute.String;
    incoterms_version: Schema.Attribute.String;
    intrastat_crs_border_tr_mode: Schema.Attribute.String;
    invoice_is_goods_receipt_based: Schema.Attribute.Boolean;
    invoice_is_mm_service_entry_based: Schema.Attribute.Boolean;
    is_order_ackn_rqd: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier-purchasing-org.supplier-purchasing-org'
    >;
    material_planned_delivery_durn: Schema.Attribute.Integer;
    minimum_order_amount: Schema.Attribute.Decimal;
    payment_terms: Schema.Attribute.String;
    planning_cycle: Schema.Attribute.String;
    pricing_date_control: Schema.Attribute.String;
    prod_stock_and_sls_data_transf_prfl: Schema.Attribute.String;
    product_unit_group: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    pur_ord_auto_generation_is_allowed: Schema.Attribute.Boolean;
    purchase_order_currency: Schema.Attribute.String;
    purchasing_group: Schema.Attribute.String;
    purchasing_is_blocked_for_supplier: Schema.Attribute.Boolean;
    purchasing_organization: Schema.Attribute.String &
      Schema.Attribute.Required;
    rounding_profile: Schema.Attribute.String;
    shipping_condition: Schema.Attribute.String;
    suplr_discount_in_kind_is_granted: Schema.Attribute.Boolean;
    suplr_invc_reval_is_allowed: Schema.Attribute.Boolean;
    suplr_is_rlvt_for_settlmt_mgmt: Schema.Attribute.Boolean;
    suplr_purg_org_is_rlvt_for_price_detn: Schema.Attribute.Boolean;
    supplier: Schema.Attribute.Relation<'manyToOne', 'api::supplier.supplier'>;
    supplier_abc_classification_code: Schema.Attribute.String;
    supplier_account_group: Schema.Attribute.String;
    supplier_account_number: Schema.Attribute.String;
    supplier_confirmation_control_key: Schema.Attribute.String;
    supplier_id: Schema.Attribute.String & Schema.Attribute.Required;
    supplier_is_returns_supplier: Schema.Attribute.String;
    supplier_phone_number: Schema.Attribute.String;
    supplier_resp_sales_person_name: Schema.Attribute.String;
    texts: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier-purchasing-org-text.supplier-purchasing-org-text'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiSupplierTextSupplierText
  extends Struct.CollectionTypeSchema {
  collectionName: 'supplier_texts';
  info: {
    displayName: 'Supplier Text';
    pluralName: 'supplier-texts';
    singularName: 'supplier-text';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    language: Schema.Attribute.String & Schema.Attribute.Required;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier-text.supplier-text'
    >;
    long_text: Schema.Attribute.Text;
    long_text_id: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    supplier: Schema.Attribute.Relation<'manyToOne', 'api::supplier.supplier'>;
    supplier_id: Schema.Attribute.String & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiSupplierSupplier extends Struct.CollectionTypeSchema {
  collectionName: 'suppliers';
  info: {
    displayName: 'Supplier';
    pluralName: 'suppliers';
    singularName: 'supplier';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    admin_users: Schema.Attribute.Relation<'manyToMany', 'admin::user'>;
    alternative_payee_account_number: Schema.Attribute.String;
    authorization_group: Schema.Attribute.String;
    birth_date: Schema.Attribute.DateTime;
    bp_id: Schema.Attribute.String;
    br_tax_is_split: Schema.Attribute.Boolean;
    business_partner: Schema.Attribute.Relation<
      'oneToOne',
      'api::business-partner.business-partner'
    >;
    companies: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier-company.supplier-company'
    >;
    company_texts: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier-company-text.supplier-company-text'
    >;
    concatenated_international_loc_no: Schema.Attribute.String;
    created_by_user: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    creation_date: Schema.Attribute.DateTime;
    customer_id: Schema.Attribute.String;
    data_exchange_instruction_key: Schema.Attribute.String;
    deletion_indicator: Schema.Attribute.Boolean;
    fiscal_address: Schema.Attribute.String;
    industry: Schema.Attribute.String;
    international_location_number_1: Schema.Attribute.String;
    international_location_number_2: Schema.Attribute.String;
    international_location_number_3: Schema.Attribute.String;
    is_natural_person: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier.supplier'
    >;
    partner_functions: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier-partner-func.supplier-partner-func'
    >;
    payment_is_blocked_for_supplier: Schema.Attribute.Boolean;
    payment_reason: Schema.Attribute.String;
    posting_is_blocked: Schema.Attribute.Boolean;
    publishedAt: Schema.Attribute.DateTime;
    purchasing_is_blocked: Schema.Attribute.Boolean;
    responsible_type: Schema.Attribute.String;
    suplr_proof_of_deliv_rlvt_code: Schema.Attribute.String;
    suplr_qlty_in_procmt_certfn_valid_to: Schema.Attribute.DateTime;
    suplr_quality_management_system: Schema.Attribute.String;
    supplier_account_group: Schema.Attribute.String;
    supplier_corporate_group: Schema.Attribute.String;
    supplier_full_name: Schema.Attribute.String;
    supplier_id: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    supplier_name: Schema.Attribute.String;
    supplier_procurement_block: Schema.Attribute.String;
    supplier_purchasing_orgs: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier-purchasing-org.supplier-purchasing-org'
    >;
    tax_number_1: Schema.Attribute.String;
    tax_number_2: Schema.Attribute.String;
    tax_number_3: Schema.Attribute.String;
    tax_number_4: Schema.Attribute.String;
    tax_number_5: Schema.Attribute.String;
    tax_number_responsible: Schema.Attribute.String;
    tax_number_type: Schema.Attribute.String;
    texts: Schema.Attribute.Relation<
      'oneToMany',
      'api::supplier-text.supplier-text'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    user_vendors: Schema.Attribute.Relation<
      'oneToMany',
      'api::user-vendor.user-vendor'
    >;
    users: Schema.Attribute.Relation<
      'manyToMany',
      'plugin::users-permissions.user'
    >;
    vat_registration: Schema.Attribute.String;
  };
}

export interface ApiTicketTicket extends Struct.CollectionTypeSchema {
  collectionName: 'tickets';
  info: {
    description: '';
    displayName: 'Ticket';
    pluralName: 'tickets';
    singularName: 'ticket';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    account_id: Schema.Attribute.String;
    assigned_to: Schema.Attribute.String;
    contact_id: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.Text;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::ticket.ticket'
    > &
      Schema.Attribute.Private;
    priority: Schema.Attribute.Enumeration<['Low', 'Medium', 'High']>;
    publishedAt: Schema.Attribute.DateTime;
    scheduled_date: Schema.Attribute.DateTime;
    status_id: Schema.Attribute.String;
    subject: Schema.Attribute.String;
    support_team: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface ApiUserVendorUserVendor extends Struct.CollectionTypeSchema {
  collectionName: 'user_vendors';
  info: {
    displayName: 'User Vendor';
    pluralName: 'user-vendors';
    singularName: 'user-vendor';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    address: Schema.Attribute.String;
    admin_user: Schema.Attribute.Relation<'oneToOne', 'admin::user'>;
    city: Schema.Attribute.String;
    country: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customer: Schema.Attribute.Relation<'manyToOne', 'api::customer.customer'>;
    department: Schema.Attribute.String;
    invoice_ref: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'api::user-vendor.user-vendor'
    > &
      Schema.Attribute.Private;
    phone: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    purchase_order: Schema.Attribute.String;
    security_que_1: Schema.Attribute.Relation<
      'manyToOne',
      'api::security-question.security-question'
    >;
    security_que_1_ans: Schema.Attribute.String;
    security_que_2: Schema.Attribute.Relation<
      'manyToOne',
      'api::security-question.security-question'
    >;
    security_que_2_ans: Schema.Attribute.String;
    supplier: Schema.Attribute.Relation<'manyToOne', 'api::supplier.supplier'>;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    user: Schema.Attribute.Relation<
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    vendor: Schema.Attribute.Relation<
      'manyToOne',
      'api::business-partner.business-partner'
    >;
    vendor_id: Schema.Attribute.String;
    website: Schema.Attribute.Text;
    zipcode: Schema.Attribute.String;
  };
}

export interface PluginContentReleasesRelease
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_releases';
  info: {
    displayName: 'Release';
    pluralName: 'releases';
    singularName: 'release';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    actions: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release-action'
    >;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    publishedAt: Schema.Attribute.DateTime;
    releasedAt: Schema.Attribute.DateTime;
    scheduledAt: Schema.Attribute.DateTime;
    status: Schema.Attribute.Enumeration<
      ['ready', 'blocked', 'failed', 'done', 'empty']
    > &
      Schema.Attribute.Required;
    timezone: Schema.Attribute.String;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginContentReleasesReleaseAction
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_release_actions';
  info: {
    displayName: 'Release Action';
    pluralName: 'release-actions';
    singularName: 'release-action';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentType: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    entryDocumentId: Schema.Attribute.String;
    isEntryValid: Schema.Attribute.Boolean;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::content-releases.release-action'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    release: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::content-releases.release'
    >;
    type: Schema.Attribute.Enumeration<['publish', 'unpublish']> &
      Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginEmailDesigner5EmailDesignerTemplate
  extends Struct.CollectionTypeSchema {
  collectionName: 'email-designer-templates';
  info: {
    description: 'This collection stores email templates created with the email designer.';
    displayName: 'Email Designer Templates';
    pluralName: 'email-designer-templates';
    singularName: 'email-designer-template';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    bodyHtml: Schema.Attribute.Text;
    bodyText: Schema.Attribute.Text;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    design: Schema.Attribute.JSON;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::email-designer-5.email-designer-template'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    publishedAt: Schema.Attribute.DateTime;
    subject: Schema.Attribute.String;
    tags: Schema.Attribute.JSON;
    templateReferenceId: Schema.Attribute.Integer & Schema.Attribute.Unique;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginI18NLocale extends Struct.CollectionTypeSchema {
  collectionName: 'i18n_locale';
  info: {
    collectionName: 'locales';
    description: '';
    displayName: 'Locale';
    pluralName: 'locales';
    singularName: 'locale';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    code: Schema.Attribute.String & Schema.Attribute.Unique;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::i18n.locale'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.SetMinMax<
        {
          max: 50;
          min: 1;
        },
        number
      >;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginReviewWorkflowsWorkflow
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows';
  info: {
    description: '';
    displayName: 'Workflow';
    name: 'Workflow';
    pluralName: 'workflows';
    singularName: 'workflow';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    contentTypes: Schema.Attribute.JSON &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'[]'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    stageRequiredToPublish: Schema.Attribute.Relation<
      'oneToOne',
      'plugin::review-workflows.workflow-stage'
    >;
    stages: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow-stage'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginReviewWorkflowsWorkflowStage
  extends Struct.CollectionTypeSchema {
  collectionName: 'strapi_workflows_stages';
  info: {
    description: '';
    displayName: 'Stages';
    name: 'Workflow Stage';
    pluralName: 'workflow-stages';
    singularName: 'workflow-stage';
  };
  options: {
    draftAndPublish: false;
    version: '1.1.0';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    color: Schema.Attribute.String & Schema.Attribute.DefaultTo<'#4945FF'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::review-workflows.workflow-stage'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String;
    permissions: Schema.Attribute.Relation<'manyToMany', 'admin::permission'>;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    workflow: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::review-workflows.workflow'
    >;
  };
}

export interface PluginUploadFile extends Struct.CollectionTypeSchema {
  collectionName: 'files';
  info: {
    description: '';
    displayName: 'File';
    pluralName: 'files';
    singularName: 'file';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    alternativeText: Schema.Attribute.String;
    caption: Schema.Attribute.String;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    ext: Schema.Attribute.String;
    folder: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'> &
      Schema.Attribute.Private;
    folderPath: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    formats: Schema.Attribute.JSON;
    hash: Schema.Attribute.String & Schema.Attribute.Required;
    height: Schema.Attribute.Integer;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::upload.file'
    > &
      Schema.Attribute.Private;
    mime: Schema.Attribute.String & Schema.Attribute.Required;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    previewUrl: Schema.Attribute.String;
    provider: Schema.Attribute.String & Schema.Attribute.Required;
    provider_metadata: Schema.Attribute.JSON;
    publishedAt: Schema.Attribute.DateTime;
    related: Schema.Attribute.Relation<'morphToMany'>;
    size: Schema.Attribute.Decimal & Schema.Attribute.Required;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    url: Schema.Attribute.String & Schema.Attribute.Required;
    width: Schema.Attribute.Integer;
  };
}

export interface PluginUploadFolder extends Struct.CollectionTypeSchema {
  collectionName: 'upload_folders';
  info: {
    displayName: 'Folder';
    pluralName: 'folders';
    singularName: 'folder';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    children: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.folder'>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    files: Schema.Attribute.Relation<'oneToMany', 'plugin::upload.file'>;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::upload.folder'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    parent: Schema.Attribute.Relation<'manyToOne', 'plugin::upload.folder'>;
    path: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    pathId: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.Unique;
    publishedAt: Schema.Attribute.DateTime;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_permissions';
  info: {
    description: '';
    displayName: 'Permission';
    name: 'permission';
    pluralName: 'permissions';
    singularName: 'permission';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Schema.Attribute.String & Schema.Attribute.Required;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.permission'
    > &
      Schema.Attribute.Private;
    publishedAt: Schema.Attribute.DateTime;
    role: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_roles';
  info: {
    description: '';
    displayName: 'Role';
    name: 'role';
    pluralName: 'roles';
    singularName: 'role';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    description: Schema.Attribute.String;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.role'
    > &
      Schema.Attribute.Private;
    name: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    permissions: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    publishedAt: Schema.Attribute.DateTime;
    type: Schema.Attribute.String & Schema.Attribute.Unique;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    users: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.user'
    >;
  };
}

export interface PluginUsersPermissionsUser
  extends Struct.CollectionTypeSchema {
  collectionName: 'up_users';
  info: {
    description: '';
    displayName: 'User';
    name: 'user';
    pluralName: 'users';
    singularName: 'user';
  };
  options: {
    draftAndPublish: false;
    timestamps: true;
  };
  attributes: {
    address: Schema.Attribute.String;
    blocked: Schema.Attribute.Boolean;
    cart: Schema.Attribute.Relation<'oneToOne', 'api::cart.cart'>;
    cart_reserves: Schema.Attribute.Relation<
      'oneToMany',
      'api::cart-reserve.cart-reserve'
    >;
    confirmationToken: Schema.Attribute.String & Schema.Attribute.Private;
    confirmed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
    createdAt: Schema.Attribute.DateTime;
    createdBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    customers: Schema.Attribute.Relation<
      'manyToMany',
      'api::customer.customer'
    >;
    email: Schema.Attribute.Email & Schema.Attribute.Required;
    firstname: Schema.Attribute.String & Schema.Attribute.Required;
    last_logout_at: Schema.Attribute.DateTime;
    lastname: Schema.Attribute.String & Schema.Attribute.Required;
    locale: Schema.Attribute.String & Schema.Attribute.Private;
    localizations: Schema.Attribute.Relation<
      'oneToMany',
      'plugin::users-permissions.user'
    > &
      Schema.Attribute.Private;
    password: Schema.Attribute.Password &
      Schema.Attribute.Private &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    provider: Schema.Attribute.String & Schema.Attribute.DefaultTo<'local'>;
    publishedAt: Schema.Attribute.DateTime;
    resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
    role: Schema.Attribute.Relation<
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    suppliers: Schema.Attribute.Relation<
      'manyToMany',
      'api::supplier.supplier'
    >;
    updatedAt: Schema.Attribute.DateTime;
    updatedBy: Schema.Attribute.Relation<'oneToOne', 'admin::user'> &
      Schema.Attribute.Private;
    username: Schema.Attribute.String & Schema.Attribute.Unique;
    vendor: Schema.Attribute.Relation<
      'oneToOne',
      'api::user-vendor.user-vendor'
    >;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ContentTypeSchemas {
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::permission': AdminPermission;
      'admin::role': AdminRole;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'admin::user': AdminUser;
      'api::admin-user-extension.admin-user-extension': ApiAdminUserExtensionAdminUserExtension;
      'api::admin-user-role-extention.admin-user-role-extention': ApiAdminUserRoleExtentionAdminUserRoleExtention;
      'api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number': ApiBpAddrDepdntIntlLocNumberBpAddrDepdntIntlLocNumber;
      'api::bp-address-usage.bp-address-usage': ApiBpAddressUsageBpAddressUsage;
      'api::bp-contact-staging.bp-contact-staging': ApiBpContactStagingBpContactStaging;
      'api::bp-contact-to-address.bp-contact-to-address': ApiBpContactToAddressBpContactToAddress;
      'api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept': ApiBpContactToFuncAndDeptBpContactToFuncAndDept;
      'api::bp-credit-worthiness.bp-credit-worthiness': ApiBpCreditWorthinessBpCreditWorthiness;
      'api::bp-email-address.bp-email-address': ApiBpEmailAddressBpEmailAddress;
      'api::bp-fax-number.bp-fax-number': ApiBpFaxNumberBpFaxNumber;
      'api::bp-home-page-url.bp-home-page-url': ApiBpHomePageUrlBpHomePageUrl;
      'api::bp-intl-address-version.bp-intl-address-version': ApiBpIntlAddressVersionBpIntlAddressVersion;
      'api::bp-marketing-attribute.bp-marketing-attribute': ApiBpMarketingAttributeBpMarketingAttribute;
      'api::bp-phone-number.bp-phone-number': ApiBpPhoneNumberBpPhoneNumber;
      'api::business-partner-address.business-partner-address': ApiBusinessPartnerAddressBusinessPartnerAddress;
      'api::business-partner-bank.business-partner-bank': ApiBusinessPartnerBankBusinessPartnerBank;
      'api::business-partner-contact.business-partner-contact': ApiBusinessPartnerContactBusinessPartnerContact;
      'api::business-partner-extension.business-partner-extension': ApiBusinessPartnerExtensionBusinessPartnerExtension;
      'api::business-partner-identification.business-partner-identification': ApiBusinessPartnerIdentificationBusinessPartnerIdentification;
      'api::business-partner-payment-card.business-partner-payment-card': ApiBusinessPartnerPaymentCardBusinessPartnerPaymentCard;
      'api::business-partner-relationship.business-partner-relationship': ApiBusinessPartnerRelationshipBusinessPartnerRelationship;
      'api::business-partner-role.business-partner-role': ApiBusinessPartnerRoleBusinessPartnerRole;
      'api::business-partner-staging.business-partner-staging': ApiBusinessPartnerStagingBusinessPartnerStaging;
      'api::business-partner.business-partner': ApiBusinessPartnerBusinessPartner;
      'api::cart-item.cart-item': ApiCartItemCartItem;
      'api::cart-reserve-item.cart-reserve-item': ApiCartReserveItemCartReserveItem;
      'api::cart-reserve.cart-reserve': ApiCartReserveCartReserve;
      'api::cart.cart': ApiCartCart;
      'api::configuration.configuration': ApiConfigurationConfiguration;
      'api::content-crm.content-crm': ApiContentCrmContentCrm;
      'api::content-ecom.content-ecom': ApiContentEcomContentEcom;
      'api::content-pim.content-pim': ApiContentPimContentPim;
      'api::content-vendor.content-vendor': ApiContentVendorContentVendor;
      'api::crm-activity.crm-activity': ApiCrmActivityCrmActivity;
      'api::crm-attachment.crm-attachment': ApiCrmAttachmentCrmAttachment;
      'api::crm-competitor-product.crm-competitor-product': ApiCrmCompetitorProductCrmCompetitorProduct;
      'api::crm-competitor.crm-competitor': ApiCrmCompetitorCrmCompetitor;
      'api::crm-follow-up-and-related-item.crm-follow-up-and-related-item': ApiCrmFollowUpAndRelatedItemCrmFollowUpAndRelatedItem;
      'api::crm-involved-party.crm-involved-party': ApiCrmInvolvedPartyCrmInvolvedParty;
      'api::crm-note.crm-note': ApiCrmNoteCrmNote;
      'api::crm-opportunity-party-contact-party.crm-opportunity-party-contact-party': ApiCrmOpportunityPartyContactPartyCrmOpportunityPartyContactParty;
      'api::crm-opportunity-party.crm-opportunity-party': ApiCrmOpportunityPartyCrmOpportunityParty;
      'api::crm-opportunity-preceding-and-follow-up-document.crm-opportunity-preceding-and-follow-up-document': ApiCrmOpportunityPrecedingAndFollowUpDocumentCrmOpportunityPrecedingAndFollowUpDocument;
      'api::crm-opportunity-sales-team-party.crm-opportunity-sales-team-party': ApiCrmOpportunitySalesTeamPartyCrmOpportunitySalesTeamParty;
      'api::crm-opportunity.crm-opportunity': ApiCrmOpportunityCrmOpportunity;
      'api::crm-organisational-unit-address.crm-organisational-unit-address': ApiCrmOrganisationalUnitAddressCrmOrganisationalUnitAddress;
      'api::crm-organisational-unit-company.crm-organisational-unit-company': ApiCrmOrganisationalUnitCompanyCrmOrganisationalUnitCompany;
      'api::crm-organisational-unit-employee.crm-organisational-unit-employee': ApiCrmOrganisationalUnitEmployeeCrmOrganisationalUnitEmployee;
      'api::crm-organisational-unit-function.crm-organisational-unit-function': ApiCrmOrganisationalUnitFunctionCrmOrganisationalUnitFunction;
      'api::crm-organisational-unit-manager.crm-organisational-unit-manager': ApiCrmOrganisationalUnitManagerCrmOrganisationalUnitManager;
      'api::crm-organisational-unit.crm-organisational-unit': ApiCrmOrganisationalUnitCrmOrganisationalUnit;
      'api::cust-addr-depdnt-information.cust-addr-depdnt-information': ApiCustAddrDepdntInformationCustAddrDepdntInformation;
      'api::customer-company-text.customer-company-text': ApiCustomerCompanyTextCustomerCompanyText;
      'api::customer-company.customer-company': ApiCustomerCompanyCustomerCompany;
      'api::customer-partner-function.customer-partner-function': ApiCustomerPartnerFunctionCustomerPartnerFunction;
      'api::customer-sales-area-text.customer-sales-area-text': ApiCustomerSalesAreaTextCustomerSalesAreaText;
      'api::customer-sales-area.customer-sales-area': ApiCustomerSalesAreaCustomerSalesArea;
      'api::customer-service-category.customer-service-category': ApiCustomerServiceCategoryCustomerServiceCategory;
      'api::customer-service-question.customer-service-question': ApiCustomerServiceQuestionCustomerServiceQuestion;
      'api::customer-tax-grouping.customer-tax-grouping': ApiCustomerTaxGroupingCustomerTaxGrouping;
      'api::customer-text.customer-text': ApiCustomerTextCustomerText;
      'api::customer.customer': ApiCustomerCustomer;
      'api::fg-bp-relationship.fg-bp-relationship': ApiFgBpRelationshipFgBpRelationship;
      'api::fg-control-main.fg-control-main': ApiFgControlMainFgControlMain;
      'api::fg-customer-business.fg-customer-business': ApiFgCustomerBusinessFgCustomerBusiness;
      'api::fg-customer-internal-backup.fg-customer-internal-backup': ApiFgCustomerInternalBackupFgCustomerInternalBackup;
      'api::fg-customer-internal.fg-customer-internal': ApiFgCustomerInternalFgCustomerInternal;
      'api::fg-customer-product-internal.fg-customer-product-internal': ApiFgCustomerProductInternalFgCustomerProductInternal;
      'api::fg-product-business.fg-product-business': ApiFgProductBusinessFgProductBusiness;
      'api::fg-product-internal-backup.fg-product-internal-backup': ApiFgProductInternalBackupFgProductInternalBackup;
      'api::fg-product-internal.fg-product-internal': ApiFgProductInternalFgProductInternal;
      'api::fg-purchase-info-record.fg-purchase-info-record': ApiFgPurchaseInfoRecordFgPurchaseInfoRecord;
      'api::fg-relationship.fg-relationship': ApiFgRelationshipFgRelationship;
      'api::guest-user-cart-item.guest-user-cart-item': ApiGuestUserCartItemGuestUserCartItem;
      'api::guest-user-cart.guest-user-cart': ApiGuestUserCartGuestUserCart;
      'api::import-file-log.import-file-log': ApiImportFileLogImportFileLog;
      'api::import-file-state.import-file-state': ApiImportFileStateImportFileState;
      'api::partner-function-config.partner-function-config': ApiPartnerFunctionConfigPartnerFunctionConfig;
      'api::permission-api-bridge.permission-api-bridge': ApiPermissionApiBridgePermissionApiBridge;
      'api::permission-crm.permission-crm': ApiPermissionCrmPermissionCrm;
      'api::permission-pim.permission-pim': ApiPermissionPimPermissionPim;
      'api::permission-vendor.permission-vendor': ApiPermissionVendorPermissionVendor;
      'api::product-basic-text.product-basic-text': ApiProductBasicTextProductBasicText;
      'api::product-catalog.product-catalog': ApiProductCatalogProductCatalog;
      'api::product-category.product-category': ApiProductCategoryProductCategory;
      'api::product-charc-value-type.product-charc-value-type': ApiProductCharcValueTypeProductCharcValueType;
      'api::product-class-charc-type-description.product-class-charc-type-description': ApiProductClassCharcTypeDescriptionProductClassCharcTypeDescription;
      'api::product-class-charc-type.product-class-charc-type': ApiProductClassCharcTypeProductClassCharcType;
      'api::product-class-type-description.product-class-type-description': ApiProductClassTypeDescriptionProductClassTypeDescription;
      'api::product-class-type.product-class-type': ApiProductClassTypeProductClassType;
      'api::product-description.product-description': ApiProductDescriptionProductDescription;
      'api::product-hierarchy-staging.product-hierarchy-staging': ApiProductHierarchyStagingProductHierarchyStaging;
      'api::product-hierarchy.product-hierarchy': ApiProductHierarchyProductHierarchy;
      'api::product-media.product-media': ApiProductMediaProductMedia;
      'api::product-plant-procurement.product-plant-procurement': ApiProductPlantProcurementProductPlantProcurement;
      'api::product-plant-sale.product-plant-sale': ApiProductPlantSaleProductPlantSale;
      'api::product-plant-storage.product-plant-storage': ApiProductPlantStorageProductPlantStorage;
      'api::product-plant-text.product-plant-text': ApiProductPlantTextProductPlantText;
      'api::product-plant.product-plant': ApiProductPlantProductPlant;
      'api::product-sale.product-sale': ApiProductSaleProductSale;
      'api::product-sales-delivery.product-sales-delivery': ApiProductSalesDeliveryProductSalesDelivery;
      'api::product-sales-tax.product-sales-tax': ApiProductSalesTaxProductSalesTax;
      'api::product-sales-text.product-sales-text': ApiProductSalesTextProductSalesText;
      'api::product-staging.product-staging': ApiProductStagingProductStaging;
      'api::product-storage-location.product-storage-location': ApiProductStorageLocationProductStorageLocation;
      'api::product-storage.product-storage': ApiProductStorageProductStorage;
      'api::product-suggestion-type.product-suggestion-type': ApiProductSuggestionTypeProductSuggestionType;
      'api::product-suggestion.product-suggestion': ApiProductSuggestionProductSuggestion;
      'api::product-units-of-measure-ean.product-units-of-measure-ean': ApiProductUnitsOfMeasureEanProductUnitsOfMeasureEan;
      'api::product-units-of-measure.product-units-of-measure': ApiProductUnitsOfMeasureProductUnitsOfMeasure;
      'api::product.product': ApiProductProduct;
      'api::promotion-application-method.promotion-application-method': ApiPromotionApplicationMethodPromotionApplicationMethod;
      'api::promotion-rule-value.promotion-rule-value': ApiPromotionRuleValuePromotionRuleValue;
      'api::promotion-rule.promotion-rule': ApiPromotionRulePromotionRule;
      'api::promotion.promotion': ApiPromotionPromotion;
      'api::scheduler.scheduler': ApiSchedulerScheduler;
      'api::security-question.security-question': ApiSecurityQuestionSecurityQuestion;
      'api::setting.setting': ApiSettingSetting;
      'api::supplier-company-text.supplier-company-text': ApiSupplierCompanyTextSupplierCompanyText;
      'api::supplier-company.supplier-company': ApiSupplierCompanySupplierCompany;
      'api::supplier-partner-func.supplier-partner-func': ApiSupplierPartnerFuncSupplierPartnerFunc;
      'api::supplier-purchasing-org-text.supplier-purchasing-org-text': ApiSupplierPurchasingOrgTextSupplierPurchasingOrgText;
      'api::supplier-purchasing-org.supplier-purchasing-org': ApiSupplierPurchasingOrgSupplierPurchasingOrg;
      'api::supplier-text.supplier-text': ApiSupplierTextSupplierText;
      'api::supplier.supplier': ApiSupplierSupplier;
      'api::ticket.ticket': ApiTicketTicket;
      'api::user-vendor.user-vendor': ApiUserVendorUserVendor;
      'plugin::content-releases.release': PluginContentReleasesRelease;
      'plugin::content-releases.release-action': PluginContentReleasesReleaseAction;
      'plugin::email-designer-5.email-designer-template': PluginEmailDesigner5EmailDesignerTemplate;
      'plugin::i18n.locale': PluginI18NLocale;
      'plugin::review-workflows.workflow': PluginReviewWorkflowsWorkflow;
      'plugin::review-workflows.workflow-stage': PluginReviewWorkflowsWorkflowStage;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
    }
  }
}
