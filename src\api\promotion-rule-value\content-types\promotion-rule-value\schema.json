{"kind": "collectionType", "collectionName": "promotion_rule_values", "info": {"singularName": "promotion-rule-value", "pluralName": "promotion-rule-values", "displayName": "Promotion Rule Value"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"value": {"type": "string", "required": true}, "promotion_rule": {"type": "relation", "relation": "manyToOne", "target": "api::promotion-rule.promotion-rule", "inversedBy": "values"}}}