module.exports = {
  async beforeCreate(event) {
    const { data } = event.params;
    await linkParentChild(data);
  },

  async beforeUpdate(event) {
    const { data } = event.params;
    await linkParentChild(data);
  },
};

async function linkParentChild(data) {
  if (data.parent_node) {
    // Find the parent where the parent_node matches an existing child_node
    const parent = await strapi.db
      .query("api::product-hierarchy.product-hierarchy")
      .findOne({
        where: { child_node: data.parent_node },
      });

    if (parent) {
      data.parent = parent.id; // Link child to parent
    }
  }

  if (data.child_node) {
    // Find the child where the child_node matches an existing parent_node
    const child = await strapi.db
      .query("api::product-hierarchy.product-hierarchy")
      .findOne({
        where: { parent_node: data.child_node },
      });

    if (child) {
      data.children = [child.id]; // Ensure parent has this child linked
    }
  }
}
