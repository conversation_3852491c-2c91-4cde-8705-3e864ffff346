import { PassThrough } from "stream";
import { stringify } from "csv-stringify";
import {
  BlobServiceClient,
  StorageSharedKeyCredential,
} from "@azure/storage-blob";

const createProductCsvStream = () =>
  stringify({
    header: true,
    delimiter: "|",
    columns: [
      "code",
      "creationSystem",
      "name",
      "description",
      "productType",
      "itemsPerSalesUnit",
      "color",
      "size",
      "style",
      "capacity",
      "livingGreen",
      "threadCount",
      "fiberContent",
      "fillType",
      "fillWeight",
      "manufacturerPartNumber",
      "adaCompliant",
      "supercategories",
      "parentProduct",
      "variantType",
      "variantSort",
      "variantPlpName",
      "sapHierarchy1",
      "sapHierarchy2",
      "sapHierarchy3",
      "sapHierarchy4",
      "sapHierarchy5",
      "sapHierarchy5",
      "truckOnly",
      "collectionCode",
      "dropShip",
      "unspscCode",
      "originCountry",
      "registryBrand",
      "collectionName",
      "vcClass",
      "hazmatCode",
      "diversitySupplier",
      "hasPersonalization",
      "personalizationText",
      "hasInstructions",
      "salesUnit",
      "brandCode",
      "vcGroup",
    ],
    quote: true,
  });

const processProductWebAttributesToStream = async (stream: any) => {
  const batchSize = 1000;
  let offset = 0;
  let hasMore = true;

  while (hasMore) {
    const products = await strapi.db.query("api::product.product").findMany({
      offset,
      limit: batchSize,
      populate: ["categories"],
    });

    if (!products.length) {
      hasMore = false;
      break;
    }

    for (const product of products) {
      const row = {
        code: product.product_id || "",
        creationSystem: product.creationSystem || "",
        name: product.name || "",
        description: product.description || "",
        productType: product.productType || "",
        itemsPerSalesUnit: "",
        color: product.color || "",
        size: product.size || "",
        style: product.style || "",
        capacity: product.capacity || "",
        livingGreen: product.livingGreen ?? "",
        threadCount: product.threadCount || "",
        fiberContent: product.fiberContent || "",
        fillType: product.fillType || "",
        fillWeight: product.fillWeight || "",
        manufacturerPartNumber: "",
        adaCompliant: product.adaCompliant ?? "",
        supercategories:
          product.categories?.map((cat) => cat.name).join(", ") || "",
        parentProduct: product.parentProduct || "",
        variantType: product.ah_variant_type || "",
        variantSort: product.variantSort || "",
        variantPlpName: product.variantPlpName || "",
        sapHierarchy1: product.sapHierarchy1 || "",
        sapHierarchy2: product.sapHierarchy2 || "",
        sapHierarchy3: product.sapHierarchy3 || "",
        sapHierarchy4: product.sapHierarchy4 || "",
        sapHierarchy5: product.sapHierarchy5 || "",
        truckOnly: product.truckOnly || "",
        collectionCode: product.collectionCode || "",
        dropShip: product.dropShip || "",
        unspscCode: product.unspscCode || "",
        originCountry: product.originCountry || "",
        registryBrand: product.registryBrand || "",
        collectionName: product.collectionName || "",
        vcClass: product.vcClass || "",
        hazmatCode: product.hazmatCode || "",
        diversitySupplier: product.diversitySupplier || "",
        hasPersonalization: product.hasPersonalization || "",
        personalizationText: product.personalizationText || "",
        hasInstructions: product.hasInstructions || "",
        salesUnit: product.salesUnit || "",
        brandCode: product.brandCode || "",
        vcGroup: product.vcGroup || ""
      };

      stream.write(row);
    }

    offset += batchSize;
  }

  stream.end();
};

const uploadProductWebAttributeToAzure = async () => {
  try {
    const passThrough = new PassThrough();
    const stream = createProductCsvStream();
    stream.pipe(passThrough);

    const accountName = process.env.AZURE_BLOB_ACCOUNT_NAME;
    const accountKey = process.env.AZURE_BLOB_ACCOUNT_KEY;
    const containerName = process.env.AZURE_BLOB_CONTAINER_NAME;
    const blobBasePath = process.env.AZURE_BLOB_FILE_PATH || "";

    const now = new Date();
    const timestamp = now
      .toISOString()
      .replace(/[-:.TZ]/g, "")
      .slice(0, 14);
    const fileName = `product_information-${timestamp}.csv`;
    const fullBlobPath = `${blobBasePath.replace(/\/$/, "")}/${fileName}`;

    const sharedKeyCredential = new StorageSharedKeyCredential(
      accountName,
      accountKey
    );
    const blobServiceClient = new BlobServiceClient(
      `https://${accountName}.blob.core.windows.net`,
      sharedKeyCredential
    );

    const containerClient = blobServiceClient.getContainerClient(containerName);

    const exists = await containerClient.exists();
    if (!exists) {
      strapi.log.info(
        `Container '${containerName}' does not exist. Creating it...`
      );
      await containerClient.create();
      strapi.log.info(`Container '${containerName}' created successfully.`);
    }

    const blockBlobClient = containerClient.getBlockBlobClient(fullBlobPath);

    const azureUploadPromise = blockBlobClient.uploadStream(
      passThrough,
      undefined,
      undefined,
      {
        blobHTTPHeaders: { blobContentType: "text/csv" },
      }
    );

    await Promise.all([
      processProductWebAttributesToStream(stream),
      azureUploadPromise,
    ]);

    strapi.log.info(
      `Product export uploaded to Azure Blob Storage at '${fullBlobPath}'.`
    );
  } catch (err) {
    strapi.log.error("Error exporting/uploading Product:", err);
  }
};

const createProductSpecificationCsvStream = () =>
  stringify({
    header: true,
    delimiter: "|",
    columns: [
      "code",
      "adaCompliant",
      "originCountry",
      "hazmatCode",
      "sds",
      "livingGreen",
      "ulApproved",
      "culApproved",
      "prop65",
      "prop65Chemical",
      "prop65ChemType",
      "energyStar",
      "warranty",
      "warrantyType",
      "certification",
      "productWeight",
      "productLength",
      "productHeight",
      "productWidth",
      "shippingWeight",
      "shippingLength",
      "shippingHeight",
      "shippingWidth",
      "shippingUnit",
      "flavor",
      "coating",
      "shape",
      "applicableMaterial",
      "mount",
      "sealType",
      "cleaner",
      "productForm",
      "scent",
      "concentrated",
      "density",
      "gauge",
      "material",
      "closureType",
      "numberOfShelves",
      "doorStyle",
      "piecePerSet",
      "numberOfPlys",
      "sheetCount",
      "amperage",
      "hetz",
      "voltage",
      "wattage",
      "eel",
      "heat",
      "heatBTU",
      "coolBTU",
      "refrigerantType",
      "averageCoveringArea",
      "noiseLevel",
      "powerSource",
      "plugType",
      "outlet",
      "cordLength",
      "filter",
      "lbsPerDozen",
      "hemColor",
      "pocketDepth",
      "gsm",
      "hoseLength",
      "cleaningPath",
      "oekoTex",
      "fscCertified",
      "greenSealCertified",
    ],
    quote: true,
  });

const processProductSpecificationsToStream = async (stream: any) => {
  const batchSize = 1000;
  let offset = 0;
  let hasMore = true;

  while (hasMore) {
    const products = await strapi.db.query("api::product.product").findMany({
      offset,
      limit: batchSize,
      populate: ["categories"],
    });

    if (!products.length) {
      hasMore = false;
      break;
    }

    for (const product of products) {
      const row = {
        code: product.product_id || "",
        adaCompliant: product.adaCompliant ?? "",
        originCountry: product.originCountry || "",
        hazmatCode: product.hazmatCode || "",
        sds: product.sds || "",
        livingGreen: product.livingGreen ?? "",
        ulApproved: product.ulApproved ?? "",
        culApproved: product.culApproved ?? "",
        prop65: product.prop65 ?? "",
        prop65Chemical: product.prop65Chemical || "",
        prop65ChemType: product.prop65ChemType || "",
        energyStar: product.energyStar ?? "",
        warranty: product.warranty || "",
        warrantyType: product.warrantyType || "",
        certification: product.certification || "",
        productWeight: product.productWeight || "",
        productLength: product.productLength || "",
        productHeight: product.productHeight || "",
        productWidth: product.productWidth || "",
        shippingWeight: product.shippingWeight || "",
        shippingLength: product.shippingLength || "",
        shippingHeight: product.shippingHeight || "",
        shippingWidth: product.shippingWidth || "",
        shippingUnit: product.shippingUnit ?? "",
        flavor: product.flavor || "",
        coating: product.coating || "",
        shape: product.shape || "",
        applicableMaterial: product.applicableMaterial || "",
        mount: product.mount || "",
        sealType: product.sealType || "",
        cleaner: product.cleaner || "",
        productForm: product.productForm || "",
        scent: product.scent || "",
        concentrated: product.concentrated ?? "",
        density: product.density || "",
        gauge: product.gauge || "",
        material: product.material || "",
        closureType: product.closureType || "",
        numberOfShelves: product.numberOfShelves || "",
        doorStyle: product.doorStyle || "",
        piecePerSet: product.piecePerSet || "",
        numberOfPlys: product.numberOfPlys || "",
        sheetCount: product.sheetCount || "",
        amperage: product.amperage || "",
        hetz: product.hetz || "",
        voltage: product.voltage || "",
        wattage: product.wattage || "",
        eel: product.eel || "",
        heat: product.heat ?? "",
        heatBTU: product.heatBTU || "",
        coolBTU: product.coolBTU || "",
        refrigerantType: product.refrigerantType || "",
        averageCoveringArea: product.averageCoveringArea || "",
        noiseLevel: product.noiseLevel || "",
        powerSource: product.powerSource || "",
        plugType: product.plugType || "",
        outlet: product.outlet || "",
        cordLength: product.cordLength || "",
        filter: product.filter || "",
        lbsPerDozen: product.lbsPerDozen || "",
        hemColor: product.hemColor || "",
        pocketDepth: product.pocketDepth || "",
        gsm: product.gsm || "",
        hoseLength: product.hoseLength || "",
        cleaningPath: product.cleaningPath || "",
        oekoTex: product.oekoTex ?? "",
        fscCertified: product.fscCertified ?? "",
        greenSealCertified: product.greenSealCertified ?? "",
      };

      stream.write(row);
    }

    offset += batchSize;
  }

  stream.end();
};

// Azure upload function
const uploadProductSpecificationToAzure = async () => {
  try {
    const passThrough = new PassThrough();
    const stream = createProductSpecificationCsvStream();
    stream.pipe(passThrough);

    const accountName = process.env.AZURE_BLOB_ACCOUNT_NAME;
    const accountKey = process.env.AZURE_BLOB_ACCOUNT_KEY;
    const containerName = process.env.AZURE_BLOB_CONTAINER_NAME;
    const blobBasePath = process.env.AZURE_BLOB_FILE_PATH || "";

    const now = new Date();
    const timestamp = now
      .toISOString()
      .replace(/[-:.TZ]/g, "")
      .slice(0, 14);
    const fileName = `specifications-${timestamp}.csv`;
    const fullBlobPath = `${blobBasePath.replace(/\/$/, "")}/${fileName}`;

    const sharedKeyCredential = new StorageSharedKeyCredential(
      accountName,
      accountKey
    );
    const blobServiceClient = new BlobServiceClient(
      `https://${accountName}.blob.core.windows.net`,
      sharedKeyCredential
    );

    const containerClient = blobServiceClient.getContainerClient(containerName);

    const exists = await containerClient.exists();
    if (!exists) {
      strapi.log.info(
        `Container '${containerName}' does not exist. Creating it...`
      );
      await containerClient.create();
      strapi.log.info(`Container '${containerName}' created successfully.`);
    }

    const blockBlobClient = containerClient.getBlockBlobClient(fullBlobPath);

    const azureUploadPromise = blockBlobClient.uploadStream(
      passThrough,
      undefined,
      undefined,
      {
        blobHTTPHeaders: { blobContentType: "text/csv" },
      }
    );

    await Promise.all([
      processProductSpecificationsToStream(stream),
      azureUploadPromise,
    ]);

    strapi.log.info(
      `Product specification export uploaded to Azure Blob Storage at '${fullBlobPath}'.`
    );
  } catch (err) {
    strapi.log.error("Error exporting/uploading Product specification:", err);
  }
};

const uploadProductMediaToAzure = async () => {
  try {
    const passThrough = new PassThrough();

    // Create the CSV stream
    const stream = stringify({
      header: true,
      delimiter: "|",
      columns: ["code", "product", "order"],
      quote: true,
    });

    // Pipe the CSV data to passThrough
    stream.pipe(passThrough);

    const accountName = process.env.AZURE_BLOB_ACCOUNT_NAME;
    const accountKey = process.env.AZURE_BLOB_ACCOUNT_KEY;
    const containerName = process.env.AZURE_BLOB_CONTAINER_NAME;
    const blobBasePath = process.env.AZURE_BLOB_FILE_PATH || "";

    // Generate timestamped filename
    const now = new Date();
    const timestamp = now
      .toISOString()
      .replace(/[-:.TZ]/g, "")
      .slice(0, 14);
    const fileName = `product_media-${timestamp}.csv`;
    const fullBlobPath = `${blobBasePath.replace(/\/$/, "")}/${fileName}`;

    const sharedKeyCredential = new StorageSharedKeyCredential(
      accountName,
      accountKey
    );
    const blobServiceClient = new BlobServiceClient(
      `https://${accountName}.blob.core.windows.net`,
      sharedKeyCredential
    );

    const containerClient = blobServiceClient.getContainerClient(containerName);

    const exists = await containerClient.exists();
    if (!exists) {
      strapi.log.info(
        `Container '${containerName}' does not exist. Creating it...`
      );
      await containerClient.create();
      strapi.log.info(`Container '${containerName}' created successfully.`);
    }

    const blockBlobClient = containerClient.getBlockBlobClient(fullBlobPath);

    const azureUploadPromise = blockBlobClient.uploadStream(
      passThrough,
      undefined,
      undefined,
      {
        blobHTTPHeaders: { blobContentType: "text/csv" },
      }
    );

    // Process data and write to the stream
    const batchSize = 1000;
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const productMedias = await strapi.db
        .query("api::product-media.product-media")
        .findMany({
          offset,
          limit: batchSize,
        });

      if (!productMedias.length) {
        hasMore = false;
        break;
      }

      for (const productMedia of productMedias) {
        const row = {
          code: productMedia.file_path || "",
          product: productMedia.product_id || "",
          order: productMedia.code || "",
        };

        stream.write(row);
      }

      offset += batchSize;
    }

    // End the stream
    stream.end();

    // Wait for Azure upload to finish
    await azureUploadPromise;

    strapi.log.info(
      `Product Media export uploaded to Azure Blob Storage at '${fullBlobPath}'.`
    );
  } catch (err) {
    strapi.log.error("Error exporting/uploading Product Media:", err);
  }
};

export {
  createProductCsvStream,
  processProductWebAttributesToStream,
  uploadProductWebAttributeToAzure,
  createProductSpecificationCsvStream,
  processProductSpecificationsToStream,
  uploadProductSpecificationToAzure,
  uploadProductMediaToAzure,
};
