/**
 * crm-organisational-unit controller
 */

import { factories } from "@strapi/strapi";
import { Context } from "koa";

export default factories.createCoreController(
  "api::crm-organisational-unit.crm-organisational-unit",
  ({ strapi }) => ({
    async registration(ctx: Context) {
      try {
        const {
          name,
          valid_from,
          valid_to,
          parent_organisational_unit_id,
          company_name,
          country_code,
          state,
          city,
          house_number,
          street,
          postal_code,
          sales_indicator,
          sales_organisation_indicator,
          service_indicator,
          service_organisation_indicator,
          marketing_indicator,
          reporting_line_indicator,
          business_partner_internal_id,
        } = ctx.request.body;

        // Required fields check
        if (!name) {
          return ctx.throw(400, "Missing required fields: name");
        }

        const locale = "en";

        const newOrgUnit = await strapi.db.transaction(async () => {
          // Create the organisational unit
          let data: any = {
            name,
            start_date: valid_from,
            end_date: valid_to,
            parent_organisational_unit_id,
            lifecycle_status_code: "ACTIVE",
            locale,
          };

          const orgUnit = await strapi
            .query("api::crm-organisational-unit.crm-organisational-unit")
            .create({
              data,
            });

          // Create the company if company_name field is provided
          if (company_name) {
            const companyData = {
              company_name,
              start_date: valid_from,
              end_date: valid_to,
              organisational_unit_id: orgUnit.organisational_unit_id,
              locale,
            };

            await strapi
              .query(
                "api::crm-organisational-unit-company.crm-organisational-unit-company"
              )
              .create({
                data: companyData,
              });
          }

          // Create the address if address fields are provided
          if (
            house_number ||
            street ||
            city ||
            postal_code ||
            country_code ||
            state
          ) {
            const addressData = {
              name,
              house_number,
              start_date: valid_from,
              end_date: valid_to,
              city_name: city,
              country_code,
              region_code: state,
              street_name: street,
              street_postal_code: postal_code,
              organisational_unit_id: orgUnit.organisational_unit_id,
              locale,
            };

            await strapi
              .query(
                "api::crm-organisational-unit-address.crm-organisational-unit-address"
              )
              .create({
                data: addressData,
              });
          }

          // Create organisational unit function if function-related fields are provided
          if (
            sales_indicator ||
            sales_organisation_indicator ||
            service_indicator ||
            service_organisation_indicator ||
            marketing_indicator ||
            reporting_line_indicator
          ) {
            const functionData = {
              start_date: valid_from,
              end_date: valid_to,
              sales_indicator: !!sales_indicator,
              sales_organisation_indicator: !!sales_organisation_indicator,
              service_indicator: !!service_indicator,
              service_organisation_indicator: !!service_organisation_indicator,
              marketing_indicator: !!marketing_indicator,
              reporting_line_indicator: !!reporting_line_indicator,
              organisational_unit_id: orgUnit.organisational_unit_id,
              locale,
            };

            await strapi
              .query(
                "api::crm-organisational-unit-function.crm-organisational-unit-function"
              )
              .create({
                data: functionData,
              });
          }

          // Create organisational unit manager if manager is provided
          if (business_partner_internal_id) {
            const managerData = {
              start_date: valid_from,
              end_date: valid_to,
              organisational_unit_id: orgUnit.organisational_unit_id,
              business_partner_internal_id,
              locale,
            };

            await strapi
              .query(
                "api::crm-organisational-unit-manager.crm-organisational-unit-manager"
              )
              .create({
                data: managerData,
              });
          }

          return orgUnit;
        });

        return ctx.send({
          message: "Organisation unit registered successfully",
          data: newOrgUnit,
        });
      } catch (error) {
        return ctx.internalServerError(
          `Failed to register organisation unit: ${error.message}`
        );
      }
    },
  })
);
