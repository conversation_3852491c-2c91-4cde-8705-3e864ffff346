{"kind": "collectionType", "collectionName": "guest_user_cart_items", "info": {"singularName": "guest-user-cart-item", "pluralName": "guest-user-cart-items", "displayName": "Guest User <PERSON><PERSON>"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"material": {"type": "string", "required": true}, "requested_quantity": {"type": "integer", "required": true, "default": 1}, "cart": {"type": "relation", "relation": "manyToOne", "target": "api::guest-user-cart.guest-user-cart", "inversedBy": "cart_items"}}}