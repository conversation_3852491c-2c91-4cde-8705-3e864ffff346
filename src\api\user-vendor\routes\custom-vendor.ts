/**
 * configuration custom router
 */

export default {
  routes: [
    {
      method: "POST",
      path: "/user-vendor/registration",
      handler: "user-vendor.registration",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "POST",
      path: "/user-vendor/forgot-password",
      handler: "user-vendor.forgotPassword",
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
