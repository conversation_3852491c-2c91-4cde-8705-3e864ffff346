{"kind": "collectionType", "collectionName": "product_basic_texts", "info": {"singularName": "product-basic-text", "pluralName": "product-basic-texts", "displayName": "Product Basic Text"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"product_id": {"type": "string"}, "language": {"type": "string"}, "long_text": {"type": "text"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "basic_texts"}}}