import { generateUniqueID } from "../../../../utils/cpi";

export default {
  async beforeCreate(event) {
    const { params } = event;
    const { data } = params;

    if (!data.organisational_unit_id) {
      try {
        const customId = `${await generateUniqueID("crm_organisational_unit_id_seq")}`;
        data.organisational_unit_id = customId;
      } catch (error) {
        console.error("Error generating unique ID:", error);
      }
    }

    if (data.parent_organisational_unit_id) {
      try {
        const parent = await strapi.db
          .query("api::crm-organisational-unit.crm-organisational-unit")
          .findOne({
            where: {
              organisational_unit_id: data.parent_organisational_unit_id,
            },
          });

        if (parent) {
          data.parent_organisational_unit = parent.id;
        }
      } catch (error) {
        console.error("Error linking parent organisational unit:", error);
      }
    }
  },
  async beforeUpdate(event) {
    const { params } = event;
    const { data } = params;

    if (data.parent_organisational_unit_id) {
      try {
        const parent = await strapi.db
          .query("api::crm-organisational-unit.crm-organisational-unit")
          .findOne({
            where: {
              organisational_unit_id: data.parent_organisational_unit_id,
            },
          });

        if (parent) {
          data.parent_organisational_unit = parent.id;
        }
      } catch (error) {
        console.error("Error linking parent organisational unit:", error);
      }
    }
  },
};
