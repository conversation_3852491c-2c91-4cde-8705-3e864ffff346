{"kind": "collectionType", "collectionName": "customers", "info": {"singularName": "customer", "pluralName": "customers", "displayName": "Customer"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"customer_id": {"type": "string", "required": true, "unique": true, "column": {"unique": true}}, "customer_account_group": {"type": "string"}, "customer_name": {"type": "string"}, "delivery_is_blocked": {"type": "string"}, "order_is_blocked_for_customer": {"type": "string"}, "posting_is_blocked": {"type": "boolean"}, "deletion_indicator": {"type": "boolean"}, "billing_is_blocked_for_customer": {"type": "boolean"}, "created_by_user": {"type": "string"}, "city_code": {"type": "string"}, "county": {"type": "string"}, "creation_date": {"type": "datetime"}, "customer_classification": {"type": "string"}, "customer_corporate_group": {"type": "string"}, "customer_full_name": {"type": "string"}, "express_train_station_name": {"type": "string"}, "fiscal_address": {"type": "string"}, "free_defined_attribute_01": {"type": "string"}, "free_defined_attribute_02": {"type": "string"}, "free_defined_attribute_03": {"type": "string"}, "free_defined_attribute_04": {"type": "string"}, "free_defined_attribute_05": {"type": "string"}, "industry": {"type": "string"}, "industry_code_1": {"type": "string"}, "industry_code_2": {"type": "string"}, "international_location_number_1": {"type": "string"}, "nielsen_region": {"type": "string"}, "payment_reason": {"type": "string"}, "responsible_type": {"type": "string"}, "tax_number_1": {"type": "string"}, "tax_number_2": {"type": "string"}, "vat_registration": {"type": "string"}, "authorization_group": {"type": "string"}, "bp_id": {"type": "string"}, "business_partner": {"type": "relation", "relation": "oneToOne", "target": "api::business-partner.business-partner", "inversedBy": "customer"}, "companies": {"type": "relation", "relation": "oneToMany", "target": "api::customer-company.customer-company", "mappedBy": "customer"}, "cust_addr_depdnt_informations": {"type": "relation", "relation": "oneToMany", "target": "api::cust-addr-depdnt-information.cust-addr-depdnt-information", "mappedBy": "customer"}, "customer_tax_groupings": {"type": "relation", "relation": "oneToMany", "target": "api::customer-tax-grouping.customer-tax-grouping", "mappedBy": "customer"}, "partner_functions": {"type": "relation", "relation": "oneToMany", "target": "api::customer-partner-function.customer-partner-function", "mappedBy": "customer"}, "sales_areas": {"type": "relation", "relation": "oneToMany", "target": "api::customer-sales-area.customer-sales-area", "mappedBy": "customer"}, "sales_area_texts": {"type": "relation", "relation": "oneToMany", "target": "api::customer-sales-area-text.customer-sales-area-text", "mappedBy": "customer"}, "customer_texts": {"type": "relation", "relation": "oneToMany", "target": "api::customer-text.customer-text", "mappedBy": "customer"}, "customer_company_texts": {"type": "relation", "relation": "oneToMany", "target": "api::customer-company-text.customer-company-text", "mappedBy": "customer"}, "users": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user", "mappedBy": "customers"}, "admin_users": {"type": "relation", "relation": "manyToMany", "target": "admin::user"}, "cart": {"type": "relation", "relation": "oneToMany", "target": "api::cart.cart", "mappedBy": "customer"}, "cart_reserves": {"type": "relation", "relation": "oneToMany", "target": "api::cart-reserve.cart-reserve", "mappedBy": "customer"}, "user_vendors": {"type": "relation", "relation": "oneToMany", "target": "api::user-vendor.user-vendor", "mappedBy": "customer"}}}