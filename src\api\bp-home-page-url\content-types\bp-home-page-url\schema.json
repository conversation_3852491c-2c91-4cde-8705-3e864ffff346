{"kind": "collectionType", "collectionName": "bp_home_page_urls", "info": {"singularName": "bp-home-page-url", "pluralName": "bp-home-page-urls", "displayName": "Business Partner Home Page URL"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"address_id": {"type": "string", "required": true}, "is_default_url_address": {"type": "boolean", "required": true}, "ordinal_number": {"type": "integer", "required": true}, "person": {"type": "string", "required": true}, "validity_start_date": {"type": "datetime"}, "search_url_address": {"type": "string"}, "url_field_length": {"type": "integer"}, "website_url": {"type": "string"}, "address_communication_remark_text": {"type": "text"}, "business_partner_address": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner-address.business-partner-address", "inversedBy": "home_page_urls"}, "bp_contact_address": {"type": "relation", "relation": "manyToOne", "target": "api::bp-contact-to-address.bp-contact-to-address", "inversedBy": "home_page_urls"}}}