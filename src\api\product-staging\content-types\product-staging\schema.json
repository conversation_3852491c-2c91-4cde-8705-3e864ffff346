{"kind": "collectionType", "collectionName": "product_stagings", "info": {"singularName": "product-staging", "pluralName": "product-stagings", "displayName": "Product Staging"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"data": {"type": "json", "required": true}, "staging_status": {"type": "enumeration", "enum": ["PENDING", "IN_PROCESS", "FAILED"], "default": "PENDING"}, "error_message": {"type": "text"}}}