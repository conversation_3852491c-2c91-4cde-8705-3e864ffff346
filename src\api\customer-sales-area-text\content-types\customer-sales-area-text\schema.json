{"kind": "collectionType", "collectionName": "customer_sales_area_texts", "info": {"singularName": "customer-sales-area-text", "pluralName": "customer-sales-area-texts", "displayName": "Customer Sales Area Text"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"long_text_id": {"type": "string", "required": true}, "long_text": {"type": "text"}, "language": {"type": "string", "required": true}, "distribution_channel": {"type": "string", "required": true}, "division": {"type": "string", "required": true}, "sales_organization": {"type": "string", "required": true}, "sales_area": {"type": "relation", "relation": "manyToOne", "target": "api::customer-sales-area.customer-sales-area", "inversedBy": "sales_area_texts"}, "customer_id": {"type": "string", "required": true}, "customer": {"type": "relation", "relation": "manyToOne", "target": "api::customer.customer", "inversedBy": "sales_area_texts"}}}