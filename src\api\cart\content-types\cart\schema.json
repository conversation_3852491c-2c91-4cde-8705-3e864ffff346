{"kind": "collectionType", "collectionName": "carts", "info": {"singularName": "cart", "pluralName": "carts", "displayName": "<PERSON><PERSON>"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"customer": {"type": "relation", "relation": "manyToOne", "target": "api::customer.customer", "inversedBy": "cart"}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "mappedBy": "cart"}, "admin_user": {"type": "relation", "relation": "oneToOne", "target": "admin::user"}, "cart_items": {"type": "relation", "relation": "oneToMany", "target": "api::cart-item.cart-item", "mappedBy": "cart"}}}