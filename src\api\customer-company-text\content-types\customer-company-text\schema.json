{"kind": "collectionType", "collectionName": "customer_company_texts", "info": {"singularName": "customer-company-text", "pluralName": "customer-company-texts", "displayName": "Customer Company Text"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"long_text_id": {"type": "string", "required": true}, "long_text": {"type": "text"}, "language": {"type": "string", "required": true}, "company_code": {"type": "string", "required": true}, "company": {"type": "relation", "relation": "manyToOne", "target": "api::customer-company.customer-company", "inversedBy": "company_texts"}, "customer_id": {"type": "string", "required": true}, "customer": {"type": "relation", "relation": "manyToOne", "target": "api::customer.customer", "inversedBy": "customer_company_texts"}}}