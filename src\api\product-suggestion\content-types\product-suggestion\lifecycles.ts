export default {
  /**
   * After a product-suggestion is created, connect relationships.
   */
  async afterCreate(event: { result: any; params: any }) {
    const { result, params } = event;

    const { product_suggested_id } = params.data;

    try {

      // Update product_suggested relationship if product_suggested_id is provided
      if (product_suggested_id) {
        const productSuggested = await strapi
          .query("api::product.product")
          .findOne({
            where: { product_id: product_suggested_id },
          });

        if (productSuggested) {
          await strapi
            .query("api::product-suggestion.product-suggestion")
            .update({
              where: { id: result.id },
              data: {
                product_suggested: {
                  connect: [productSuggested.id],
                },
              },
            });
        }
      }
    } catch (error) {
      console.error("Error in afterCreate:", error);
    }
  },

  /**
   * After a product-suggestion is updated, connect relationships.
   */
  async afterUpdate(event: { result: any; params: any }) {
    const { result, params } = event;

    const { product_suggested_id } = params.data;

    try {
      // Update product_suggested relationship if product_suggested_id is provided
      if (product_suggested_id) {
        const productSuggested = await strapi
          .query("api::product.product")
          .findOne({
            where: { product_id: product_suggested_id },
          });

        if (productSuggested) {
          await strapi
            .query("api::product-suggestion.product-suggestion")
            .update({
              where: { id: result.id },
              data: {
                product_suggested: {
                  connect: [productSuggested.id],
                },
              },
            });
        } else {
          await strapi
            .query("api::product-suggestion.product-suggestion")
            .update({
              where: { id: result.id },
              data: {
                product_suggested: {
                  set: [],
                },
              },
            });
        }
      }
    } catch (error) {
      console.error("Error in afterUpdate:", error);
    }
  },
};
