{"kind": "collectionType", "collectionName": "crm_organisational_unit_companies", "info": {"singularName": "crm-organisational-unit-company", "pluralName": "crm-organisational-unit-companies", "displayName": "CRM Organisational Unit Company"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"start_date": {"type": "date"}, "end_date": {"type": "date"}, "company_name": {"type": "string"}, "organisational_unit_id": {"type": "string"}, "organisational_unit": {"type": "relation", "relation": "manyToOne", "target": "api::crm-organisational-unit.crm-organisational-unit", "inversedBy": "crm_org_unit_companies"}}}