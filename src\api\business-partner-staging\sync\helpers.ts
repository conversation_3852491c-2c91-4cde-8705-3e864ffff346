const BusinessPartner = async (data) => {
  const { bp_id, locale } = data;

  if (!bp_id) throw new Error("bp_id is required");

  try {
    // Check if the business partner already exists by bp_id with locale
    const existingPartner = await strapi.db
      .query("api::business-partner.business-partner")
      .findOne({
        where: { bp_id, locale },
        populate: {
          roles: true,
        },
      });

    if (existingPartner) {
      const isBPProspect: any = existingPartner?.roles?.find(
        (role: any) => role.bp_role === "PRO001"
      );
      if (isBPProspect) {
        await await strapi.db
          .query("api::business-partner.business-partner")
          .delete({ where: { id: existingPartner.id } });

        // Create a new business partner
        await strapi.db.query("api::business-partner.business-partner").create({
          data,
        });
      } else {
        // Update the existing business partner
        await strapi.db.query("api::business-partner.business-partner").update({
          where: { documentId: existingPartner.documentId },
          data,
        });
      }
    } else {
      // Create a new business partner
      await strapi.db.query("api::business-partner.business-partner").create({
        data,
      });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize BusinessPartner ::: ${error.message}`
    );
  }
};

const BusinessPartnerAddress = async (data) => {
  const { bp_id, bp_address_id, locale } = data;

  if (!bp_id || !bp_address_id)
    throw new Error("bp_id && bp_address_id is required");

  try {
    // Check if the business partner address already exists by bp_address_id with locale
    let address = await strapi.db
      .query("api::business-partner-address.business-partner-address")
      .findOne({
        where: { bp_id, bp_address_id, locale },
      });

    const {
      email_addresses,
      fax_numbers,
      home_page_urls,
      phone_numbers,
      mobile_phone_numbers,
      ...addressData
    } = data;

    if (address) {
      // Update the existing business partner address
      address = await strapi.db
        .query("api::business-partner-address.business-partner-address")
        .update({
          where: { documentId: address.documentId },
          data: addressData,
        });
    } else {
      // Create a new business partner address
      address = await strapi.db
        .query("api::business-partner-address.business-partner-address")
        .create({
          data: addressData,
        });
    }

    // Sync Business Partner Email Address Data
    if (Array.isArray(email_addresses)) {
      for (const email of email_addresses) {
        if (!email?.locale) {
          email.locale = locale;
        }
        email.business_partner_address = address.id;
        await BusinessPartnerEmailAddress(email);
      }
    }

    // Sync Business Partner Fax Number Data
    if (Array.isArray(fax_numbers)) {
      for (const fax of fax_numbers) {
        if (!fax?.locale) {
          fax.locale = locale;
        }
        fax.business_partner_address = address.id;
        await BusinessPartnerFaxNumber(fax);
      }
    }

    // Sync Business Partner Home Page URL Data
    if (Array.isArray(home_page_urls)) {
      for (const url of home_page_urls) {
        if (!url?.locale) {
          url.locale = locale;
        }
        url.business_partner_address = address.id;
        await BusinessPartnerHomePageUrl(url);
      }
    }

    // Sync Business Partner Phone Number Data
    if (Array.isArray(phone_numbers)) {
      for (const phone of phone_numbers) {
        if (!phone?.locale) {
          phone.locale = locale;
        }
        phone.business_partner_address = address.id;
        await BusinessPartnerPhoneNumber(phone);
      }
    }

    // Sync Business Partner Mobile Phone Number Data
    if (Array.isArray(mobile_phone_numbers)) {
      for (const mobile of mobile_phone_numbers) {
        if (!mobile?.locale) {
          mobile.locale = locale;
        }
        mobile.business_partner_address = address.id;
        await BusinessPartnerPhoneNumber(mobile);
      }
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Address ::: ${error.message}`
    );
  }
};

const BusinessPartnerBank = async (data) => {
  const { bp_id, bank_identification, locale } = data;

  if (!bp_id || !bank_identification)
    throw new Error("bp_id && bank_identification is required");

  try {
    // Check if the business partner bank exists by bank_identification with locale
    const existingBank = await strapi.db
      .query("api::business-partner-bank.business-partner-bank")
      .findOne({
        where: { bp_id, bank_identification, locale },
      });

    if (existingBank) {
      // Update the existing business partner bank
      await strapi.db
        .query("api::business-partner-bank.business-partner-bank")
        .update({
          where: { documentId: existingBank.documentId },
          data,
        });
    } else {
      // Create a new business partner bank
      await strapi.db
        .query("api::business-partner-bank.business-partner-bank")
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Bank ::: ${error.message}`
    );
  }
};

const BusinessPartnerIdentification = async (data) => {
  const { bp_id, bp_identification_type, bp_identification_number, locale } =
    data;

  if (!bp_id || !bp_identification_type || !bp_identification_number)
    throw new Error(
      "bp_id, bp_identification_type && bp_identification_number are required"
    );

  try {
    // Check if the business partner identification exists
    const existing = await strapi.db
      .query(
        "api::business-partner-identification.business-partner-identification"
      )
      .findOne({
        where: {
          bp_id,
          bp_identification_type,
          bp_identification_number,
          locale,
        },
      });

    if (existing) {
      // Update the existing business partner identification
      await strapi.db
        .query(
          "api::business-partner-identification.business-partner-identification"
        )
        .update({
          where: { documentId: existing.documentId },
          data,
        });
    } else {
      // Create a new business partner identification
      await strapi.db
        .query(
          "api::business-partner-identification.business-partner-identification"
        )
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Identification ::: ${error.message}`
    );
  }
};

const BusinessPartnerContact = async (data) => {
  const { bp_person_id, bp_company_id, locale } = data;

  if (!bp_person_id || !bp_company_id) {
    throw new Error("bp_person_id and bp_company_id are required");
  }

  try {
    // Check if the business partner contact already exists
    const existingContact = await strapi.db
      .query("api::business-partner-contact.business-partner-contact")
      .findOne({
        where: {
          bp_person_id,
          bp_company_id,
          locale,
        },
      });

    if (existingContact) {
      // Update the existing business partner contact
      await strapi.db
        .query("api::business-partner-contact.business-partner-contact")
        .update({
          where: { documentId: existingContact.documentId },
          data,
        });
    } else {
      // Create a new business partner contact
      await strapi.db
        .query("api::business-partner-contact.business-partner-contact")
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Contact ::: ${error.message}`
    );
  }
};

const BusinessPartnerContactToAddress = async (data) => {
  const { bp_contact_address_id, bp_person_id, bp_company_id, locale } = data;

  if (!bp_contact_address_id || !bp_person_id || !bp_company_id) {
    throw new Error(
      "bp_contact_address_id, bp_person_id and bp_company_id are required"
    );
  }

  try {
    // Check if the business partner contact address exists
    let address = await strapi.db
      .query("api::bp-contact-to-address.bp-contact-to-address")
      .findOne({
        where: {
          bp_contact_address_id,
          bp_person_id,
          bp_company_id,
          locale,
        },
      });

    const {
      email_addresses,
      fax_numbers,
      home_page_urls,
      phone_numbers,
      mobile_phone_numbers,
      ...addressData
    } = data;

    if (address) {
      // Update the existing business partner contact address
      address = await strapi.db
        .query("api::bp-contact-to-address.bp-contact-to-address")
        .update({
          where: { documentId: address.documentId },
          data: addressData,
        });
    } else {
      // Create a new business partner contact address
      address = await strapi.db
        .query("api::bp-contact-to-address.bp-contact-to-address")
        .create({
          data: addressData,
        });
    }

    // Sync Business Partner Email Address Data
    if (Array.isArray(email_addresses)) {
      for (const email of email_addresses) {
        if (!email?.locale) {
          email.locale = locale;
        }
        email.address_id = bp_contact_address_id;
        email.bp_contact_address = address.id;
        await BusinessPartnerEmailAddress(email);
      }
    }

    // Sync Business Partner Fax Number Data
    if (Array.isArray(fax_numbers)) {
      for (const fax of fax_numbers) {
        if (!fax?.locale) {
          fax.locale = locale;
        }
        fax.address_id = bp_contact_address_id;
        fax.bp_contact_address = address.id;
        await BusinessPartnerFaxNumber(fax);
      }
    }

    // Sync Business Partner Home Page URL Data
    if (Array.isArray(home_page_urls)) {
      for (const url of home_page_urls) {
        if (!url?.locale) {
          url.locale = locale;
        }
        url.address_id = bp_contact_address_id;
        url.bp_contact_address = address.id;
        await BusinessPartnerHomePageUrl(url);
      }
    }

    // Sync Business Partner Phone Number Data
    if (Array.isArray(phone_numbers)) {
      for (const phone of phone_numbers) {
        if (!phone?.locale) {
          phone.locale = locale;
        }
        phone.address_id = bp_contact_address_id;
        phone.bp_contact_address = address.id;
        await BusinessPartnerPhoneNumber(phone);
      }
    }

    // Sync Business Partner Mobile Phone Number Data
    if (Array.isArray(mobile_phone_numbers)) {
      for (const mobile of mobile_phone_numbers) {
        if (!mobile?.locale) {
          mobile.locale = locale;
        }
        mobile.address_id = bp_contact_address_id;
        mobile.bp_contact_address = address.id;
        await BusinessPartnerPhoneNumber(mobile);
      }
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Contact Address ::: ${error.message}`
    );
  }
};

const BusinessPartnerContactToFuncAndDepts = async (data) => {
  const { bp_person_id, bp_company_id, locale } = data;

  if (!bp_person_id || !bp_company_id) {
    throw new Error(
      "bp_contact_address_id, bp_person_id and bp_company_id are required"
    );
  }

  try {
    // Check if the business partner contact function & department exists
    const existingContactFuncDept = await strapi.db
      .query("api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept")
      .findOne({
        where: {
          bp_person_id,
          bp_company_id,
          locale,
        },
      });

    if (existingContactFuncDept) {
      // Update the existing contact function & department
      await strapi.db
        .query("api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept")
        .update({
          where: { documentId: existingContactFuncDept.documentId },
          data,
        });
    } else {
      // Create a new contact function & department
      await strapi.db
        .query("api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept")
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Contact Contact Function & Department ::: ${error.message}`
    );
  }
};

const BusinessPartnerInternationalAddressVersion = async (data) => {
  const { bp_id, address_id, address_representation_code, locale } = data;

  if (!bp_id || !address_id || !address_representation_code) {
    throw new Error(
      "bp_id, address_id and address_representation_code are required"
    );
  }

  try {
    // Check if the business partner international address exists
    const existingAddress = await strapi.db
      .query("api::bp-intl-address-version.bp-intl-address-version")
      .findOne({
        where: { bp_id, address_id, address_representation_code, locale },
      });

    if (existingAddress) {
      // Update the existing business partner international address
      await strapi.db
        .query("api::bp-intl-address-version.bp-intl-address-version")
        .update({
          where: { documentId: existingAddress.documentId },
          data,
        });
    } else {
      // Create a new business partner international address
      await strapi.db
        .query("api::bp-intl-address-version.bp-intl-address-version")
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner International Address Version ::: ${error.message}`
    );
  }
};

const BusinessPartnerPaymentCard = async (data) => {
  const { payment_card_id, payment_card_type, card_number, bp_id, locale } =
    data;

  if (!payment_card_id || !payment_card_type || !card_number || !bp_id) {
    throw new Error(
      "payment_card_id, payment_card_type, card_number and bp_id is required"
    );
  }

  try {
    // Check if the business partner payment card exists
    const existingPaymentCard = await strapi.db
      .query("api::business-partner-payment-card.business-partner-payment-card")
      .findOne({
        where: {
          payment_card_id,
          payment_card_type,
          card_number,
          bp_id,
          locale,
        },
      });

    if (existingPaymentCard) {
      // Update the existing business partner payment card
      await strapi.db
        .query(
          "api::business-partner-payment-card.business-partner-payment-card"
        )
        .update({
          where: { documentId: existingPaymentCard.documentId },
          data,
        });
    } else {
      // Create a new business partner payment card
      await strapi.db
        .query(
          "api::business-partner-payment-card.business-partner-payment-card"
        )
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Payment Card ::: ${error.message}`
    );
  }
};

const BusinessPartnerRelationship = async (data) => {
  const { bp_id1, bp_id2, locale } = data;

  if (!bp_id1 || !bp_id2) throw new Error("bp_id1 and bp_id2 and required");

  try {
    // Check if the business partner relationship exists
    const existingRelationship = await strapi.db
      .query("api::business-partner-relationship.business-partner-relationship")
      .findOne({
        where: {
          bp_id1,
          bp_id2,
          locale,
        },
      });

    if (existingRelationship) {
      // Update the existing business partner relationship
      await strapi.db
        .query(
          "api::business-partner-relationship.business-partner-relationship"
        )
        .update({
          where: { id: existingRelationship.id },
          data,
        });
    } else {
      // Create a new business partner relationship
      await strapi.db
        .query(
          "api::business-partner-relationship.business-partner-relationship"
        )
        .create({
          data,
        });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Relationship ::: ${error.message}`
    );
  }
};

const BusinessPartnerRole = async (data) => {
  const { bp_role, bp_id, locale } = data;

  if (!bp_id || !bp_role) throw new Error("bp_id and bp_role are required");

  try {
    // Check if the business partner role exists
    const existingRole = await strapi.db
      .query("api::business-partner-role.business-partner-role")
      .findOne({
        where: { bp_id, bp_role, locale },
      });

    if (existingRole) {
      // Update the existing business partner role
      await strapi.db
        .query("api::business-partner-role.business-partner-role")
        .update({
          where: { id: existingRole.id },
          data,
        });
    } else {
      // Create a new business partner role
      await strapi.db
        .query("api::business-partner-role.business-partner-role")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Role ::: ${error.message}`
    );
  }
};

const BPAddrDepdntIntlLocNumber = async (data) => {
  const { bp_id, bp_address_id, locale } = data;

  if (!bp_id || !bp_address_id) {
    throw new Error("bp_id and bp_address_id are required");
  }

  try {
    // Check if the Business Partner Address-Dependent ILN exists by bp_id and bp_address_id
    const existingLocNumber = await strapi.db
      .query(
        "api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number"
      )
      .findOne({
        where: {
          bp_id,
          bp_address_id,
          locale,
        },
      });

    if (existingLocNumber) {
      // Update the existing record
      await strapi.db
        .query(
          "api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number"
        )
        .update({
          where: { id: existingLocNumber.id },
          data,
        });
    } else {
      // Create a new record
      await strapi.db
        .query(
          "api::bp-addr-depdnt-intl-loc-number.bp-addr-depdnt-intl-loc-number"
        )
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Address-Dependent ILN ::: ${error.message}`
    );
  }
};

const BusinessPartnerAddressUsage = async (data) => {
  const { bp_id, bp_address_id, address_usage, locale } = data;

  if (!bp_id || !bp_address_id || !address_usage) {
    throw new Error("bp_id, address_usage and bp_address_id are required");
  }

  try {
    // Check if the Business Partner Address Usage exists
    const existingUsage = await strapi.db
      .query("api::bp-address-usage.bp-address-usage")
      .findOne({
        where: {
          bp_id,
          bp_address_id,
          address_usage,
          locale,
        },
      });

    if (existingUsage) {
      // Update the existing record
      await strapi.db.query("api::bp-address-usage.bp-address-usage").update({
        where: { id: existingUsage.id },
        data,
      });
    } else {
      // Create a new record
      await strapi.db
        .query("api::bp-address-usage.bp-address-usage")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Address Usage ::: ${error.message}`
    );
  }
};

const BusinessPartnerCreditWorthiness = async (data) => {
  const { bp_id, locale } = data;

  if (!bp_id) {
    throw new Error("bp_id is required");
  }

  try {
    // Check if the Business Partner Credit Worthiness record exists by bp_id
    const existingRecord = await strapi.db
      .query("api::bp-credit-worthiness.bp-credit-worthiness")
      .findOne({
        where: { bp_id, locale },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db
        .query("api::bp-credit-worthiness.bp-credit-worthiness")
        .update({
          where: { id: existingRecord.id },
          data,
        });
    } else {
      // Create a new record
      await strapi.db
        .query("api::bp-credit-worthiness.bp-credit-worthiness")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Credit Worthiness ::: ${error.message}`
    );
  }
};

const BusinessPartnerEmailAddress = async (data) => {
  const { email_address, business_partner_address, locale } = data;

  if (!email_address) {
    throw new Error("email_address is required");
  }

  // Convert email_address to lowercase
  const lowerCaseData = {
    ...data,
    email_address: email_address?.toLowerCase(),
  };

  try {
    // Check if the Business Partner Email Address exists
    const existingRecord = await strapi.db
      .query("api::bp-email-address.bp-email-address")
      .findOne({
        where: {
          business_partner_address,
          locale,
        },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db.query("api::bp-email-address.bp-email-address").update({
        where: { id: existingRecord.id },
        data: lowerCaseData,
      });
    } else {
      // Create a new record
      await strapi.db
        .query("api::bp-email-address.bp-email-address")
        .create({ data: lowerCaseData });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Email Address ::: ${error.message}`
    );
  }
};

const BusinessPartnerFaxNumber = async (data) => {
  const { ordinal_number, address_id, person, locale } = data;

  if (!person || !ordinal_number || !address_id) {
    throw new Error("person, ordinal_number and address_id are required");
  }

  try {
    // Check if the Business Partner Fax Number exists
    const existingRecord = await strapi.db
      .query("api::bp-fax-number.bp-fax-number")
      .findOne({
        where: {
          person,
          ordinal_number,
          address_id,
          locale,
        },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db.query("api::bp-fax-number.bp-fax-number").update({
        where: { id: existingRecord.id },
        data,
      });
    } else {
      // Create a new record
      await strapi.db
        .query("api::bp-fax-number.bp-fax-number")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Fax Number ::: ${error.message}`
    );
  }
};

const BusinessPartnerHomePageUrl = async (data) => {
  const { ordinal_number, address_id, person, is_default_url_address, locale } =
    data;

  if (!person || !ordinal_number || !address_id || !is_default_url_address) {
    throw new Error(
      "person, ordinal_number, is_default_url_address and address_id are required"
    );
  }

  try {
    // Check if the Business Partner Home Page URL exists
    const existingRecord = await strapi.db
      .query("api::bp-home-page-url.bp-home-page-url")
      .findOne({
        where: {
          person,
          ordinal_number,
          address_id,
          locale,
        },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db.query("api::bp-home-page-url.bp-home-page-url").update({
        where: { id: existingRecord.id },
        data,
      });
    } else {
      // Create a new record
      await strapi.db
        .query("api::bp-home-page-url.bp-home-page-url")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Home Page URL ::: ${error.message}`
    );
  }
};

const BusinessPartnerPhoneNumber = async (data) => {
  const { ordinal_number, address_id, person, locale } = data;

  if (!person || !ordinal_number || !address_id) {
    throw new Error("person, ordinal_number and address_id are required");
  }

  try {
    // Check if the Business Partner Phone Number exists
    const existingRecord = await strapi.db
      .query("api::bp-phone-number.bp-phone-number")
      .findOne({
        where: {
          person,
          ordinal_number,
          address_id,
          locale,
        },
      });

    if (existingRecord) {
      // Update the existing record
      await strapi.db.query("api::bp-phone-number.bp-phone-number").update({
        where: { id: existingRecord.id },
        data,
      });
    } else {
      // Create a new record
      await strapi.db
        .query("api::bp-phone-number.bp-phone-number")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Business Partner Phone Number ::: ${error.message}`
    );
  }
};

const Customer = async (data) => {
  const { customer_id, locale } = data;

  if (!customer_id) throw new Error("customer_id is required");

  try {
    // Check if the customer already exists
    const existingCustomer = await strapi.db
      .query("api::customer.customer")
      .findOne({
        where: { customer_id, locale },
      });

    if (existingCustomer) {
      // Update the existing customer
      await strapi.db.query("api::customer.customer").update({
        where: { id: existingCustomer.id },
        data,
      });
    } else {
      // Create a new customer
      await strapi.db.query("api::customer.customer").create({ data });
    }
  } catch (error) {
    throw new Error(`Unable to synchronize Customer ::: ${error.message}`);
  }
};

const CustomerCompany = async (data) => {
  const { customer_id, company_code, locale } = data;

  if (!customer_id || !company_code)
    throw new Error("customer_id and company_code are required");

  try {
    // Check if the customer company already exists
    const existingCustomerCompany = await strapi.db
      .query("api::customer-company.customer-company")
      .findOne({
        where: { customer_id, company_code, locale },
      });

    if (existingCustomerCompany) {
      // Update the existing customer company
      await strapi.db.query("api::customer-company.customer-company").update({
        where: { id: existingCustomerCompany.id },
        data,
      });
    } else {
      // Create a new customer company
      await strapi.db
        .query("api::customer-company.customer-company")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Customer Company ::: ${error.message}`
    );
  }
};

const CustomerCompanyText = async (data) => {
  const { long_text_id, language, customer_id, company_code, locale } = data;

  if (!long_text_id || !language || !customer_id || !company_code) {
    throw new Error(
      "long_text_id, language, company_code and customer_id are required"
    );
  }

  try {
    // Check if the Customer Company Text already exists
    const existingText = await strapi.db
      .query("api::customer-company-text.customer-company-text")
      .findOne({
        where: { long_text_id, language, customer_id, company_code, locale },
      });

    if (existingText) {
      // Update the existing Customer Company Text
      await strapi.db
        .query("api::customer-company-text.customer-company-text")
        .update({
          where: { id: existingText.id },
          data,
        });
    } else {
      // Create a new Customer Company Text
      await strapi.db
        .query("api::customer-company-text.customer-company-text")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Customer Company Text ::: ${error.message}`
    );
  }
};

const CustomerPartnerFunction = async (data) => {
  const {
    customer_id,
    partner_function,
    sales_organization,
    distribution_channel,
    division,
    partner_counter,
    locale,
  } = data;

  if (
    !customer_id ||
    !partner_function ||
    !sales_organization ||
    !division ||
    !partner_counter ||
    !distribution_channel
  )
    throw new Error("customer_id and partner_function are required");

  try {
    // Check if the customer partner function already exists
    const existingPartnerFunction = await strapi.db
      .query("api::customer-partner-function.customer-partner-function")
      .findOne({
        where: {
          customer_id,
          partner_function,
          sales_organization,
          division,
          partner_counter,
          locale,
        },
      });

    if (existingPartnerFunction) {
      // Update the existing customer partner function
      await strapi.db
        .query("api::customer-partner-function.customer-partner-function")
        .update({
          where: { id: existingPartnerFunction.id },
          data,
        });
    } else {
      // Create a new customer partner function
      await strapi.db
        .query("api::customer-partner-function.customer-partner-function")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Customer Partner Function ::: ${error.message}`
    );
  }
};

const CustomerSalesArea = async (data) => {
  const {
    customer_id,
    sales_organization,
    distribution_channel,
    division,
    locale,
  } = data;

  if (
    !customer_id ||
    !sales_organization ||
    !distribution_channel ||
    !division
  ) {
    throw new Error(
      "customer_id, sales_organization, distribution_channel, and division are required"
    );
  }

  try {
    // Check if the Customer Sales Area exists with the specified fields
    const existingSalesArea = await strapi.db
      .query("api::customer-sales-area.customer-sales-area")
      .findOne({
        where: {
          customer_id,
          sales_organization,
          distribution_channel,
          division,
          locale,
        },
      });

    if (existingSalesArea) {
      // Update the existing Customer Sales Area
      await strapi.db
        .query("api::customer-sales-area.customer-sales-area")
        .update({
          where: { id: existingSalesArea.id },
          data,
        });
    } else {
      // Create a new Customer Sales Area
      await strapi.db
        .query("api::customer-sales-area.customer-sales-area")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Customer Sales Area ::: ${error.message}`
    );
  }
};

const CustomerSalesAreaText = async (data) => {
  const {
    long_text_id,
    language,
    distribution_channel,
    division,
    sales_organization,
    customer_id,
    locale,
  } = data;

  if (
    !long_text_id ||
    !language ||
    !distribution_channel ||
    !division ||
    !customer_id ||
    !sales_organization
  ) {
    throw new Error(
      "long_text_id, language, distribution_channel, division, customer_id and sales_organization required"
    );
  }

  try {
    // Check if the Customer Sales Area Text already exists by long_text_id
    const existingText = await strapi.db
      .query("api::customer-sales-area-text.customer-sales-area-text")
      .findOne({
        where: {
          long_text_id,
          language,
          distribution_channel,
          division,
          sales_organization,
          customer_id,
          locale,
        },
      });

    if (existingText) {
      // Update the existing Customer Sales Area Text
      await strapi.db
        .query("api::customer-sales-area-text.customer-sales-area-text")
        .update({
          where: { id: existingText.id },
          data,
        });
    } else {
      // Create a new Customer Sales Area Text
      await strapi.db
        .query("api::customer-sales-area-text.customer-sales-area-text")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Customer Sales Area Text ::: ${error.message}`
    );
  }
};

const CustomerTaxGrouping = async (data) => {
  const { customer_id, customer_tax_grouping_code, locale } = data;

  if (!customer_id || !customer_tax_grouping_code) {
    throw new Error("customer_id and customer_tax_grouping_code are required");
  }

  try {
    // Check if the Customer Tax Grouping already exists by customer_id and customer_tax_grouping_code
    const existingGrouping = await strapi.db
      .query("api::customer-tax-grouping.customer-tax-grouping")
      .findOne({
        where: {
          customer_id,
          customer_tax_grouping_code,
          locale,
        },
      });

    if (existingGrouping) {
      // Update the existing Customer Tax Grouping
      await strapi.db
        .query("api::customer-tax-grouping.customer-tax-grouping")
        .update({
          where: { id: existingGrouping.id },
          data,
        });
    } else {
      // Create a new Customer Tax Grouping
      await strapi.db
        .query("api::customer-tax-grouping.customer-tax-grouping")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Customer Tax Grouping ::: ${error.message}`
    );
  }
};

const CustomerText = async (data) => {
  const { long_text_id, language, customer_id, locale } = data;

  if (!long_text_id || !language || !customer_id) {
    throw new Error("long_text_id, language and customer_id are required");
  }

  try {
    // Check if the Customer Text already exists by long_text_id
    const existingText = await strapi.db
      .query("api::customer-text.customer-text")
      .findOne({
        where: { long_text_id, language, customer_id, locale },
      });

    if (existingText) {
      // Update the existing Customer Text
      await strapi.db.query("api::customer-text.customer-text").update({
        where: { id: existingText.id },
        data,
      });
    } else {
      // Create a new Customer Text
      await strapi.db
        .query("api::customer-text.customer-text")
        .create({ data });
    }
  } catch (error) {
    throw new Error(`Unable to synchronize Customer Text ::: ${error.message}`);
  }
};

const Supplier = async (data) => {
  const { supplier_id, locale } = data;

  if (!supplier_id) {
    throw new Error("supplier_id is required");
  }

  try {
    // Check if the Supplier already exists by supplier_id
    const existingSupplier = await strapi.db
      .query("api::supplier.supplier")
      .findOne({
        where: { supplier_id, locale },
      });

    if (existingSupplier) {
      // Update the existing Supplier
      await strapi.db.query("api::supplier.supplier").update({
        where: { id: existingSupplier.id },
        data,
      });
    } else {
      // Create a new Supplier
      await strapi.db.query("api::supplier.supplier").create({ data });
    }
  } catch (error) {
    throw new Error(`Unable to synchronize Supplier ::: ${error.message}`);
  }
};

const SupplierCompany = async (data) => {
  const { supplier_id, company_code, locale } = data;

  if (!supplier_id || !company_code) {
    throw new Error("supplier_id and company_code are required");
  }

  try {
    // Check if the Supplier Company already exists by supplier_id and company_code
    const existingCompany = await strapi.db
      .query("api::supplier-company.supplier-company")
      .findOne({
        where: { supplier_id, company_code, locale },
      });

    if (existingCompany) {
      // Update the existing Supplier Company
      await strapi.db.query("api::supplier-company.supplier-company").update({
        where: { id: existingCompany.id },
        data,
      });
    } else {
      // Create a new Supplier Company
      await strapi.db
        .query("api::supplier-company.supplier-company")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Supplier Company ::: ${error.message}`
    );
  }
};

const SupplierCompanyText = async (data) => {
  const { long_text_id, language, supplier_id, company_code, locale } = data;

  if (!long_text_id || !language || !supplier_id || !company_code) {
    throw new Error(
      "long_text_id, language, company_code and supplier_id are required"
    );
  }

  try {
    // Check if the Supplier Company Text exists by long_text_id and locale
    const existingText = await strapi.db
      .query("api::supplier-company-text.supplier-company-text")
      .findOne({
        where: { long_text_id, language, supplier_id, company_code, locale },
      });

    if (existingText) {
      // Update the existing Supplier Company Text
      await strapi.db
        .query("api::supplier-company-text.supplier-company-text")
        .update({
          where: { id: existingText.id },
          data,
        });
    } else {
      // Create a new Supplier Company Text
      await strapi.db
        .query("api::supplier-company-text.supplier-company-text")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Supplier Company Text ::: ${error.message}`
    );
  }
};

const SupplierText = async (data) => {
  const { long_text_id, language, supplier_id, locale } = data;

  if (!long_text_id || !language || !supplier_id) {
    throw new Error("long_text_id, language and supplier_id are required");
  }

  try {
    // Check if the Supplier Text exists by long_text_id and locale
    const existingText = await strapi.db
      .query("api::supplier-text.supplier-text")
      .findOne({
        where: {
          long_text_id,
          language,
          supplier_id,
          locale,
        },
      });

    if (existingText) {
      // Update the existing Supplier Text
      await strapi.db.query("api::supplier-text.supplier-text").update({
        where: { id: existingText.id },
        data,
      });
    } else {
      // Create a new Supplier Text
      await strapi.db
        .query("api::supplier-text.supplier-text")
        .create({ data });
    }
  } catch (error) {
    throw new Error(`Unable to synchronize Supplier Text ::: ${error.message}`);
  }
};

const SupplierPartnerFunc = async (data) => {
  const {
    supplier_id,
    partner_function,
    purchasing_organization,
    partner_counter,
    locale,
  } = data;

  if (
    !supplier_id ||
    !partner_function ||
    !purchasing_organization ||
    !partner_counter
  ) {
    throw new Error(
      "supplier_id, partner_function, purchasing_organization and partner_counter are required"
    );
  }

  try {
    // Check if the Supplier Partner Function exists by supplier_id, partner_function, and partner_counter
    const existingFunc = await strapi.db
      .query("api::supplier-partner-func.supplier-partner-func")
      .findOne({
        where: {
          supplier_id,
          partner_function,
          purchasing_organization,
          partner_counter,
          locale,
        },
      });

    if (existingFunc) {
      // Update the existing Supplier Partner Function
      await strapi.db
        .query("api::supplier-partner-func.supplier-partner-func")
        .update({
          where: { id: existingFunc.id },
          data,
        });
    } else {
      // Create a new Supplier Partner Function
      await strapi.db
        .query("api::supplier-partner-func.supplier-partner-func")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Supplier Partner Function ::: ${error.message}`
    );
  }
};

const SupplierPurchasingOrganization = async (data) => {
  const { purchasing_organization, supplier_id, locale } = data;

  if (!purchasing_organization || !supplier_id) {
    throw new Error("purchasing_organization and supplier_id are required");
  }

  try {
    // Check if the Supplier Purchasing Organization exists
    const existingSPO = await strapi.db
      .query("api::supplier-purchasing-org.supplier-purchasing-org")
      .findOne({
        where: {
          purchasing_organization,
          supplier_id,
          locale,
        },
      });

    if (existingSPO) {
      // Update the existing Supplier Purchasing Organization
      await strapi.db
        .query("api::supplier-purchasing-org.supplier-purchasing-org")
        .update({
          where: { id: existingSPO.id },
          data,
        });
    } else {
      // Create a new Supplier Purchasing Organization
      await strapi.db
        .query("api::supplier-purchasing-org.supplier-purchasing-org")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Supplier Purchasing Organization ::: ${error.message}`
    );
  }
};

const SupplierPurchasingOrganizationText = async (data) => {
  const {
    long_text_id,
    language,
    supplier_id,
    purchasing_organization,
    locale,
  } = data;

  if (!long_text_id || !language || !supplier_id || !purchasing_organization) {
    throw new Error(
      "long_text_id, language, purchasing_organization and supplier_id are required"
    );
  }

  try {
    // Check if the Supplier Purchasing Organization Text exists by long_text_id and locale
    const existingText = await strapi.db
      .query("api::supplier-purchasing-org-text.supplier-purchasing-org-text")
      .findOne({
        where: {
          long_text_id,
          language,
          supplier_id,
          purchasing_organization,
          locale,
        },
      });

    if (existingText) {
      // Update the existing Supplier Purchasing Organization Text
      await strapi.db
        .query("api::supplier-purchasing-org-text.supplier-purchasing-org-text")
        .update({
          where: { id: existingText.id },
          data,
        });
    } else {
      // Create a new Supplier Purchasing Organization Text
      await strapi.db
        .query("api::supplier-purchasing-org-text.supplier-purchasing-org-text")
        .create({ data });
    }
  } catch (error) {
    throw new Error(
      `Unable to synchronize Supplier Purchasing Organization Text ::: ${error.message}`
    );
  }
};

export {
  BusinessPartner,
  BusinessPartnerAddress,
  BusinessPartnerBank,
  BusinessPartnerIdentification,
  BusinessPartnerContact,
  BusinessPartnerContactToAddress,
  BusinessPartnerContactToFuncAndDepts,
  BusinessPartnerInternationalAddressVersion,
  BusinessPartnerPaymentCard,
  BusinessPartnerRelationship,
  BusinessPartnerRole,
  BPAddrDepdntIntlLocNumber,
  BusinessPartnerAddressUsage,
  BusinessPartnerCreditWorthiness,
  BusinessPartnerEmailAddress,
  BusinessPartnerFaxNumber,
  BusinessPartnerHomePageUrl,
  BusinessPartnerPhoneNumber,
  Customer,
  CustomerCompany,
  CustomerCompanyText,
  CustomerPartnerFunction,
  CustomerSalesArea,
  CustomerSalesAreaText,
  CustomerTaxGrouping,
  CustomerText,
  Supplier,
  SupplierCompany,
  SupplierCompanyText,
  SupplierText,
  SupplierPartnerFunc,
  SupplierPurchasingOrganization,
  SupplierPurchasingOrganizationText,
};
