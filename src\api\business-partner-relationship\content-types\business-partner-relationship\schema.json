{"kind": "collectionType", "collectionName": "business_partner_relationships", "info": {"singularName": "business-partner-relationship", "pluralName": "business-partner-relationships", "displayName": "Business Partner Relationship"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"relationship_number": {"type": "string", "required": true}, "validity_start_date": {"type": "datetime"}, "validity_end_date": {"type": "datetime", "required": true}, "is_standard_relationship": {"type": "boolean"}, "relationship_category": {"type": "string"}, "bp_relationship_type": {"type": "string"}, "created_by_user": {"type": "string"}, "creation_date": {"type": "datetime"}, "creation_time": {"type": "datetime"}, "last_changed_by_user": {"type": "string"}, "last_change_date": {"type": "datetime"}, "last_change_time": {"type": "datetime"}, "bp_id1": {"type": "string", "required": true}, "business_partner1": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "relationships1"}, "bp_id2": {"type": "string", "required": true}, "business_partner2": {"type": "relation", "relation": "manyToOne", "target": "api::business-partner.business-partner", "inversedBy": "relationships2"}, "contact_relationships": {"type": "relation", "relation": "oneToMany", "target": "api::business-partner-contact.business-partner-contact", "mappedBy": "relationship"}, "contact_func_and_dept_relationships": {"type": "relation", "relation": "oneToMany", "target": "api::bp-contact-to-func-and-dept.bp-contact-to-func-and-dept", "mappedBy": "relationship"}, "bp_contact_to_address_relationships": {"type": "relation", "relation": "oneToMany", "target": "api::bp-contact-to-address.bp-contact-to-address", "mappedBy": "relationship"}}}